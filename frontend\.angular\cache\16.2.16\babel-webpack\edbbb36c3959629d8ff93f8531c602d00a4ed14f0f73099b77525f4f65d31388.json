{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/services/data.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"confirmDialog\"];\nfunction ListProjectComponent_div_83_div_1_div_34_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 61);\n    i0.ɵɵelement(4, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 90);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 91)(8, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 92);\n    i0.ɵɵelement(10, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getFileName(file_r9), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r8.getFileUrl(file_r9), i0.ɵɵsanitizeUrl)(\"download\", ctx_r8.getFileName(file_r9));\n  }\n}\nfunction ListProjectComponent_div_83_div_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 61);\n    i0.ɵɵelement(3, \"path\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 84);\n    i0.ɵɵtemplate(7, ListProjectComponent_div_83_div_1_div_34_div_7_Template, 13, 3, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Fichiers (\", projet_r5.fichiers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", projet_r5.fichiers);\n  }\n}\nfunction ListProjectComponent_div_83_div_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 61);\n    i0.ɵɵelement(3, \"path\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5, \" Fichiers \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 94)(7, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 96);\n    i0.ɵɵelement(9, \"path\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"p\", 64);\n    i0.ɵɵtext(11, \"Aucun fichier joint\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c2 = function (a1) {\n  return [\"/admin/projects/details\", a1];\n};\nconst _c3 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c4 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nfunction ListProjectComponent_div_83_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57)(4, \"h3\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 61);\n    i0.ɵɵelement(9, \"path\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"span\", 62);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 63);\n    i0.ɵɵelement(14, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"span\", 64);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"a\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 67);\n    i0.ɵɵelement(21, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_83_div_1_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const projet_r5 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(projet_r5._id && ctx_r11.openDeleteDialog(projet_r5._id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 67);\n    i0.ɵɵelement(24, \"path\", 70);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 71)(26, \"div\", 72)(27, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 61);\n    i0.ɵɵelement(29, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"h4\", 75);\n    i0.ɵɵtext(31, \" Description \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"p\", 76);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, ListProjectComponent_div_83_div_1_div_34_Template, 8, 2, \"div\", 77);\n    i0.ɵɵtemplate(35, ListProjectComponent_div_83_div_1_div_35_Template, 12, 0, \"div\", 77);\n    i0.ɵɵelementStart(36, \"div\", 78)(37, \"a\", 79)(38, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 80);\n    i0.ɵɵelement(40, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Voir d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"a\", 81)(44, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 80);\n    i0.ɵɵelement(46, \"path\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48, \"Voir rendus\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const projet_r5 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.titre, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.groupe || \"Tous\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(17, 10, projet_r5.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c1, projet_r5._id));\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.description || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r5.fichiers && projet_r5.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !projet_r5.fichiers || projet_r5.fichiers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c2, projet_r5._id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c3))(\"queryParams\", i0.ɵɵpureFunction1(18, _c4, projet_r5._id));\n  }\n}\nfunction ListProjectComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, ListProjectComponent_div_83_div_1_Template, 49, 20, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projets);\n  }\n}\nfunction ListProjectComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99);\n    i0.ɵɵelement(3, \"div\", 100);\n    i0.ɵɵelementStart(4, \"div\", 101);\n    i0.ɵɵelement(5, \"div\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 103);\n    i0.ɵɵtext(7, \"Chargement des projets...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 104);\n    i0.ɵɵtext(9, \"Veuillez patienter\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ListProjectComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 108);\n    i0.ɵɵtext(6, \"Aucun projet disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 109);\n    i0.ɵɵtext(8, \"Commencez par cr\\u00E9er votre premier projet pour organiser vos cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 110);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 111);\n    i0.ɵɵelement(11, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Cr\\u00E9er un projet \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ListProjectComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"div\", 114)(3, \"div\", 115);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 116);\n    i0.ɵɵelement(5, \"path\", 117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_86_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDeleteCancel());\n    });\n    i0.ɵɵtext(12, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_86_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onDeleteConfirm());\n    });\n    i0.ɵɵtext(14, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.dialogData.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.dialogData.message);\n  }\n}\nexport class ListProjectComponent {\n  constructor(projetService, router, dialog, authService) {\n    this.projetService = projetService;\n    this.router = router;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.projets = [];\n    this.isLoading = true;\n    this.isAdmin = false;\n    this.showDeleteDialog = false;\n    this.projectIdToDelete = null;\n    this.dialogData = {\n      title: 'Confirmer la suppression',\n      message: 'Êtes-vous sûr de vouloir supprimer ce projet?'\n    };\n  }\n  ngOnInit() {\n    this.loadProjets();\n    this.checkAdminStatus();\n  }\n  loadProjets() {\n    this.isLoading = true;\n    this.projetService.getProjets().subscribe({\n      next: projets => {\n        this.projets = projets;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.isLoading = false;\n        alert('Erreur lors du chargement des projets: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n      }\n    });\n  }\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\n  loadProjects() {\n    this.loadProjets();\n  }\n  checkAdminStatus() {\n    this.isAdmin = this.authService.isAdmin();\n  }\n  editProjet(projetId) {\n    if (!projetId) {\n      return;\n    }\n    this.router.navigate(['/admin/projects/edit', projetId]);\n  }\n  viewProjetDetails(projetId) {\n    if (!projetId) {\n      return;\n    }\n    this.router.navigate(['/admin/projects/detail', projetId]);\n  }\n  deleteProjet(projetId) {\n    if (!projetId) {\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projetService.deleteProjet(projetId).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.loadProjets();\n        },\n        error: err => {\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n        }\n      });\n    }\n  }\n  openDeleteDialog(id) {\n    if (!id) {\n      return;\n    }\n    this.showDeleteDialog = true;\n    this.projectIdToDelete = id;\n  }\n  onDeleteConfirm() {\n    if (this.projectIdToDelete) {\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.loadProjets();\n          this.showDeleteDialog = false;\n        },\n        error: err => {\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n          this.showDeleteDialog = false;\n        }\n      });\n    }\n  }\n  onDeleteCancel() {\n    this.showDeleteDialog = false;\n  }\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  getActiveProjectsCount() {\n    if (!this.projets) return 0;\n    const now = new Date();\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite) return true; // Considérer comme actif si pas de date limite\n      const dateLimit = new Date(projet.dateLimite);\n      return dateLimit >= now; // Actif si la date limite n'est pas dépassée\n    }).length;\n  }\n  getExpiredProjectsCount() {\n    if (!this.projets) return 0;\n    const now = new Date();\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite) return false; // Pas expiré si pas de date limite\n      const dateLimit = new Date(projet.dateLimite);\n      return dateLimit < now; // Expiré si la date limite est dépassée\n    }).length;\n  }\n  getUniqueGroupsCount() {\n    if (!this.projets) return 0;\n    const uniqueGroups = new Set(this.projets.map(projet => projet.groupe).filter(groupe => groupe && groupe.trim() !== ''));\n    return uniqueGroups.size;\n  }\n  getCompletionPercentage() {\n    if (!this.projets || this.projets.length === 0) return 0;\n    const expiredCount = this.getExpiredProjectsCount();\n    const totalCount = this.projets.length;\n    return Math.round(expiredCount / totalCount * 100);\n  }\n  getProjectsByGroup() {\n    if (!this.projets) return {};\n    const groupCounts = {};\n    this.projets.forEach(projet => {\n      const groupe = projet.groupe || 'Non spécifié';\n      groupCounts[groupe] = (groupCounts[groupe] || 0) + 1;\n    });\n    return groupCounts;\n  }\n  getRecentProjects() {\n    if (!this.projets) return [];\n    // Comme nous n'avons pas de dateCreation, on retourne les projets récents basés sur la date limite\n    const now = new Date();\n    const oneMonthFromNow = new Date();\n    oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite) return false;\n      const deadline = new Date(projet.dateLimite);\n      return deadline >= now && deadline <= oneMonthFromNow;\n    });\n  }\n  getUpcomingDeadlines() {\n    if (!this.projets) return [];\n    const now = new Date();\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite) return false;\n      const deadline = new Date(projet.dateLimite);\n      return deadline >= now && deadline <= oneWeekFromNow;\n    }).sort((a, b) => {\n      const dateA = new Date(a.dateLimite);\n      const dateB = new Date(b.dateLimite);\n      return dateA.getTime() - dateB.getTime();\n    });\n  }\n  static {\n    this.ɵfac = function ListProjectComponent_Factory(t) {\n      return new (t || ListProjectComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListProjectComponent,\n      selectors: [[\"app-list-project\"]],\n      viewQuery: function ListProjectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.confirmDialog = _t.first);\n        }\n      },\n      decls: 87,\n      vars: 13,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-6\", \"lg:mb-0\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"grid\", \"grid-cols-2\", \"md:grid-cols-4\", \"gap-4\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-primary/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"font-medium\", \"text-primary\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-1\"], [1, \"bg-primary/20\", \"dark:bg-dark-accent-primary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"bg-gradient-to-br\", \"from-success/10\", \"to-success/5\", \"dark:from-dark-accent-secondary/20\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-success/20\", \"dark:border-dark-accent-secondary/30\"], [1, \"text-xs\", \"font-medium\", \"text-success\", \"dark:text-dark-accent-secondary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-success\", \"dark:text-dark-accent-secondary\"], [1, \"bg-success/20\", \"dark:bg-dark-accent-secondary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-warning/10\", \"to-warning/5\", \"dark:from-warning/20\", \"dark:to-warning/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-warning/20\", \"dark:border-warning/30\"], [1, \"text-xs\", \"font-medium\", \"text-warning\", \"dark:text-warning\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-warning/20\", \"dark:bg-warning/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-info/10\", \"to-info/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-info/20\", \"dark:border-dark-accent-primary/30\"], [1, \"text-xs\", \"font-medium\", \"text-info\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"bg-info/20\", \"dark:bg-dark-accent-primary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"mt-6\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-2\"], [1, \"bg-gradient-to-r\", \"from-success\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-3\"], [\"routerLink\", \"/admin/projects/new\", 1, \"group\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\", \"overflow-hidden\"], [1, \"relative\", \"p-6\", \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex-1\", \"pr-4\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\", \"line-clamp-2\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-sm\", \"font-medium\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"space-x-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-200\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"routerLink\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-danger\", \"dark:text-danger-dark\", \"hover:bg-danger\", \"hover:text-white\", \"dark:hover:bg-danger-dark\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"p-6\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"text-sm\", \"line-clamp-3\", \"leading-relaxed\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-3\", \"pt-4\", \"border-t\", \"border-gray-100\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex-1\", \"group\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex-1\", \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"space-y-2\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [\"class\", \"flex items-center justify-between p-2 bg-white dark:bg-dark-bg-secondary rounded-lg shadow-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-2\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"rounded-lg\", \"shadow-sm\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"flex-1\", \"min-w-0\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-1.5\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"ml-2\", \"px-3\", \"py-1.5\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\", \"download\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"text-center\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/20\", \"border-t-primary\", \"dark:border-dark-accent-primary/20\", \"dark:border-t-dark-accent-primary\", \"mx-auto\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-pulse\"], [1, \"mt-6\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"font-medium\"], [1, \"mt-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-6\"], [\"routerLink\", \"/admin/projects/new\", 1, \"inline-flex\", \"items-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"bg-black/50\", \"backdrop-blur-sm\"], [1, \"bg-white/95\", \"dark:bg-dark-bg-secondary/95\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-2xl\", \"w-full\", \"max-w-md\", \"p-8\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"mx-4\"], [1, \"text-center\", \"mb-6\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"rounded-full\", \"p-4\", \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-danger\", \"dark:text-danger-dark\", \"mx-auto\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"], [1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"]],\n      template: function ListProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(7, \"svg\", 7);\n          i0.ɵɵelement(8, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(9, \"div\")(10, \"h1\", 9);\n          i0.ɵɵtext(11, \" Gestion des Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 10);\n          i0.ɵɵtext(13, \" Cr\\u00E9ez, g\\u00E9rez et suivez vos projets acad\\u00E9miques \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13)(17, \"div\")(18, \"p\", 14);\n          i0.ɵɵtext(19, \"Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\", 15);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 16);\n          i0.ɵɵtext(23, \"Projets cr\\u00E9\\u00E9s\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(25, \"svg\", 18);\n          i0.ɵɵelement(26, \"path\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(27, \"div\", 19)(28, \"div\", 13)(29, \"div\")(30, \"p\", 20);\n          i0.ɵɵtext(31, \"Actifs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\", 21);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 16);\n          i0.ɵɵtext(35, \"En cours\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(37, \"svg\", 23);\n          i0.ɵɵelement(38, \"path\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(39, \"div\", 25)(40, \"div\", 13)(41, \"div\")(42, \"p\", 26);\n          i0.ɵɵtext(43, \"Expir\\u00E9s\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\", 27);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\", 16);\n          i0.ɵɵtext(47, \"Date d\\u00E9pass\\u00E9e\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 28);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(49, \"svg\", 29);\n          i0.ɵɵelement(50, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(51, \"div\", 31)(52, \"div\", 13)(53, \"div\")(54, \"p\", 32);\n          i0.ɵɵtext(55, \"Groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\", 33);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"p\", 16);\n          i0.ɵɵtext(59, \"Diff\\u00E9rents\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(61, \"svg\", 35);\n          i0.ɵɵelement(62, \"path\", 36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(63, \"div\", 37)(64, \"div\", 38)(65, \"h4\", 39);\n          i0.ɵɵtext(66, \"Progression des projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"span\", 40);\n          i0.ɵɵtext(68);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 41);\n          i0.ɵɵelement(70, \"div\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 43)(72, \"span\");\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\");\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(76, \"div\", 44)(77, \"a\", 45)(78, \"div\", 46);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(79, \"svg\", 47);\n          i0.ɵɵelement(80, \"path\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(81, \"span\");\n          i0.ɵɵtext(82, \"Nouveau projet\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(83, ListProjectComponent_div_83_Template, 2, 1, \"div\", 49);\n          i0.ɵɵtemplate(84, ListProjectComponent_div_84_Template, 10, 0, \"div\", 50);\n          i0.ɵɵtemplate(85, ListProjectComponent_div_85_Template, 13, 0, \"div\", 50);\n          i0.ɵɵtemplate(86, ListProjectComponent_div_86_Template, 15, 2, \"div\", 51);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(21);\n          i0.ɵɵtextInterpolate(ctx.projets.length || 0);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.getActiveProjectsCount());\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.getExpiredProjectsCount());\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.getUniqueGroupsCount());\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getCompletionPercentage(), \"% compl\\u00E9t\\u00E9s\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.getCompletionPercentage(), \"%\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getActiveProjectsCount(), \" actifs\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getExpiredProjectsCount(), \" expir\\u00E9s\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets && ctx.projets.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (!ctx.projets || ctx.projets.length === 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteDialog);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_scaleIn {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n\\n.glass-card[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.project-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.project-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);\\n}\\n\\n\\n\\n.btn-modern[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n\\n\\n.badge-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .badge-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);\\n}\\n\\n\\n\\n.icon-hover[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n\\n.icon-hover[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(5deg);\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity 0.3s;\\n  z-index: 1000;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n\\n\\n.loading-pulse[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out;\\n}\\n\\n\\n\\n.focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #00f7ff;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .project-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n\\n  .btn-modern[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n\\n\\n.floating-actions[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n\\n\\n.file-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateX(4px);\\n  background-color: rgba(79, 95, 173, 0.05);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.05);\\n}\\n\\n\\n\\n.dialog-overlay[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n}\\n\\n.dialog-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_scaleIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n.bg-gradient-modern[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .bg-gradient-modern[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);\\n}\\n\\n\\n\\n.card-hover-effect[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.card-hover-effect[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px) scale(1.02);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .card-hover-effect[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r8", "getFileName", "file_r9", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "ListProjectComponent_div_83_div_1_div_34_div_7_Template", "projet_r5", "fichiers", "length", "ɵɵlistener", "ListProjectComponent_div_83_div_1_Template_button_click_22_listener", "restoredCtx", "ɵɵrestoreView", "_r12", "$implicit", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "_id", "openDeleteDialog", "ListProjectComponent_div_83_div_1_div_34_Template", "ListProjectComponent_div_83_div_1_div_35_Template", "titre", "groupe", "ɵɵpipeBind2", "dateLimite", "ɵɵpureFunction1", "_c1", "description", "_c2", "ɵɵpureFunction0", "_c3", "_c4", "ListProjectComponent_div_83_div_1_Template", "ctx_r0", "projets", "ListProjectComponent_div_86_Template_button_click_11_listener", "_r14", "ctx_r13", "onDeleteCancel", "ListProjectComponent_div_86_Template_button_click_13_listener", "ctx_r15", "onDeleteConfirm", "ctx_r3", "dialogData", "title", "ɵɵtextInterpolate", "message", "ListProjectComponent", "constructor", "projetService", "router", "dialog", "authService", "isLoading", "isAdmin", "showDeleteDialog", "projectIdToDelete", "ngOnInit", "loadProjets", "checkAdminStatus", "getProjets", "subscribe", "next", "error", "err", "alert", "loadProjects", "editProjet", "projetId", "navigate", "viewProjetDetails", "deleteProjet", "confirm", "id", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "getActiveProjectsCount", "now", "Date", "filter", "projet", "dateLimit", "getExpiredProjectsCount", "getUniqueGroupsCount", "uniqueGroups", "Set", "map", "trim", "size", "getCompletionPercentage", "expiredCount", "totalCount", "Math", "round", "getProjectsByGroup", "groupCounts", "for<PERSON>ach", "getRecentProjects", "oneMonthFromNow", "setMonth", "getMonth", "deadline", "getUpcomingDeadlines", "oneWeekFromNow", "setDate", "getDate", "sort", "a", "b", "dateA", "dateB", "getTime", "ɵɵdirectiveInject", "i1", "ProjetService", "i2", "Router", "i3", "MatDialog", "i4", "DataService", "selectors", "viewQuery", "ListProjectComponent_Query", "rf", "ctx", "ListProjectComponent_div_83_Template", "ListProjectComponent_div_84_Template", "ListProjectComponent_div_85_Template", "ListProjectComponent_div_86_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Projet } from 'src/app/models/projet.model';\r\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-list-project',\r\n  templateUrl: './list-project.component.html',\r\n  styleUrls: ['./list-project.component.css'],\r\n})\r\nexport class ListProjectComponent implements OnInit {\r\n  projets: Projet[] = [];\r\n  isLoading = true;\r\n  isAdmin = false;\r\n  showDeleteDialog = false;\r\n  projectIdToDelete: string | null = null;\r\n\r\n  // For the confirmation dialog\r\n  dialogRef?: MatDialogRef<any>;\r\n  dialogData = {\r\n    title: 'Confirmer la suppression',\r\n    message: 'Êtes-vous sûr de vouloir supprimer ce projet?',\r\n  };\r\n\r\n  @ViewChild('confirmDialog') confirmDialog!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private dialog: MatDialog,\r\n    private authService: DataService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadProjets();\r\n    this.checkAdminStatus();\r\n  }\r\n\r\n  loadProjets(): void {\r\n    this.isLoading = true;\r\n    this.projetService.getProjets().subscribe({\r\n      next: (projets) => {\r\n        this.projets = projets;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        alert(\r\n          'Erreur lors du chargement des projets: ' +\r\n            (err.error?.message || err.message || 'Erreur inconnue')\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\r\n  loadProjects(): void {\r\n    this.loadProjets();\r\n  }\r\n\r\n  checkAdminStatus(): void {\r\n    this.isAdmin = this.authService.isAdmin();\r\n  }\r\n\r\n  editProjet(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      return;\r\n    }\r\n    this.router.navigate(['/admin/projects/edit', projetId]);\r\n  }\r\n\r\n  viewProjetDetails(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      return;\r\n    }\r\n    this.router.navigate(['/admin/projects/detail', projetId]);\r\n  }\r\n\r\n  deleteProjet(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      this.projetService.deleteProjet(projetId).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.loadProjets();\r\n        },\r\n        error: (err) => {\r\n          alert(\r\n            'Erreur lors de la suppression du projet: ' +\r\n              (err.error?.message || err.message || 'Erreur inconnue')\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  openDeleteDialog(id: string): void {\r\n    if (!id) {\r\n      return;\r\n    }\r\n    this.showDeleteDialog = true;\r\n    this.projectIdToDelete = id;\r\n  }\r\n\r\n  onDeleteConfirm(): void {\r\n    if (this.projectIdToDelete) {\r\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.loadProjets();\r\n          this.showDeleteDialog = false;\r\n        },\r\n        error: (err) => {\r\n          alert(\r\n            'Erreur lors de la suppression du projet: ' +\r\n              (err.error?.message || err.message || 'Erreur inconnue')\r\n          );\r\n          this.showDeleteDialog = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  onDeleteCancel(): void {\r\n    this.showDeleteDialog = false;\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    if (!filePath) return '';\r\n\r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n\r\n    // Utiliser la route spécifique pour le téléchargement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'Fichier';\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n\r\n    return filePath;\r\n  }\r\n\r\n  getActiveProjectsCount(): number {\r\n    if (!this.projets) return 0;\r\n\r\n    const now = new Date();\r\n    return this.projets.filter(projet => {\r\n      if (!projet.dateLimite) return true; // Considérer comme actif si pas de date limite\r\n      const dateLimit = new Date(projet.dateLimite);\r\n      return dateLimit >= now; // Actif si la date limite n'est pas dépassée\r\n    }).length;\r\n  }\r\n\r\n  getExpiredProjectsCount(): number {\r\n    if (!this.projets) return 0;\r\n\r\n    const now = new Date();\r\n    return this.projets.filter(projet => {\r\n      if (!projet.dateLimite) return false; // Pas expiré si pas de date limite\r\n      const dateLimit = new Date(projet.dateLimite);\r\n      return dateLimit < now; // Expiré si la date limite est dépassée\r\n    }).length;\r\n  }\r\n\r\n  getUniqueGroupsCount(): number {\r\n    if (!this.projets) return 0;\r\n\r\n    const uniqueGroups = new Set(\r\n      this.projets\r\n        .map(projet => projet.groupe)\r\n        .filter(groupe => groupe && groupe.trim() !== '')\r\n    );\r\n\r\n    return uniqueGroups.size;\r\n  }\r\n\r\n  getCompletionPercentage(): number {\r\n    if (!this.projets || this.projets.length === 0) return 0;\r\n\r\n    const expiredCount = this.getExpiredProjectsCount();\r\n    const totalCount = this.projets.length;\r\n\r\n    return Math.round((expiredCount / totalCount) * 100);\r\n  }\r\n\r\n  getProjectsByGroup(): { [key: string]: number } {\r\n    if (!this.projets) return {};\r\n\r\n    const groupCounts: { [key: string]: number } = {};\r\n\r\n    this.projets.forEach(projet => {\r\n      const groupe = projet.groupe || 'Non spécifié';\r\n      groupCounts[groupe] = (groupCounts[groupe] || 0) + 1;\r\n    });\r\n\r\n    return groupCounts;\r\n  }\r\n\r\n  getRecentProjects(): Projet[] {\r\n    if (!this.projets) return [];\r\n\r\n    // Comme nous n'avons pas de dateCreation, on retourne les projets récents basés sur la date limite\r\n    const now = new Date();\r\n    const oneMonthFromNow = new Date();\r\n    oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);\r\n\r\n    return this.projets.filter(projet => {\r\n      if (!projet.dateLimite) return false;\r\n      const deadline = new Date(projet.dateLimite);\r\n      return deadline >= now && deadline <= oneMonthFromNow;\r\n    });\r\n  }\r\n\r\n  getUpcomingDeadlines(): Projet[] {\r\n    if (!this.projets) return [];\r\n\r\n    const now = new Date();\r\n    const oneWeekFromNow = new Date();\r\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\r\n\r\n    return this.projets.filter(projet => {\r\n      if (!projet.dateLimite) return false;\r\n      const deadline = new Date(projet.dateLimite);\r\n      return deadline >= now && deadline <= oneWeekFromNow;\r\n    }).sort((a, b) => {\r\n      const dateA = new Date(a.dateLimite!);\r\n      const dateB = new Date(b.dateLimite!);\r\n      return dateA.getTime() - dateB.getTime();\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec gradient -->\r\n    <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n      <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n        <div class=\"mb-6 lg:mb-0\">\r\n          <div class=\"flex items-center space-x-4 mb-4\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n                Gestion des Projets\r\n              </h1>\r\n              <p class=\"text-text dark:text-dark-text-secondary\">\r\n                Créez, gérez et suivez vos projets académiques\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Statistiques avancées -->\r\n          <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n            <!-- Total projets -->\r\n            <div class=\"bg-gradient-to-br from-primary/10 to-primary/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-primary/20 dark:border-dark-accent-primary/30\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p class=\"text-xs font-medium text-primary dark:text-dark-accent-primary uppercase tracking-wider\">Total</p>\r\n                  <p class=\"text-2xl font-bold text-primary dark:text-dark-accent-primary\">{{ projets.length || 0 }}</p>\r\n                  <p class=\"text-xs text-text dark:text-dark-text-secondary mt-1\">Projets créés</p>\r\n                </div>\r\n                <div class=\"bg-primary/20 dark:bg-dark-accent-primary/30 p-3 rounded-xl\">\r\n                  <svg class=\"w-6 h-6 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Projets actifs -->\r\n            <div class=\"bg-gradient-to-br from-success/10 to-success/5 dark:from-dark-accent-secondary/20 dark:to-dark-accent-secondary/10 rounded-xl p-4 border border-success/20 dark:border-dark-accent-secondary/30\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p class=\"text-xs font-medium text-success dark:text-dark-accent-secondary uppercase tracking-wider\">Actifs</p>\r\n                  <p class=\"text-2xl font-bold text-success dark:text-dark-accent-secondary\">{{ getActiveProjectsCount() }}</p>\r\n                  <p class=\"text-xs text-text dark:text-dark-text-secondary mt-1\">En cours</p>\r\n                </div>\r\n                <div class=\"bg-success/20 dark:bg-dark-accent-secondary/30 p-3 rounded-xl\">\r\n                  <svg class=\"w-6 h-6 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Projets expirés -->\r\n            <div class=\"bg-gradient-to-br from-warning/10 to-warning/5 dark:from-warning/20 dark:to-warning/10 rounded-xl p-4 border border-warning/20 dark:border-warning/30\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p class=\"text-xs font-medium text-warning dark:text-warning uppercase tracking-wider\">Expirés</p>\r\n                  <p class=\"text-2xl font-bold text-warning dark:text-warning\">{{ getExpiredProjectsCount() }}</p>\r\n                  <p class=\"text-xs text-text dark:text-dark-text-secondary mt-1\">Date dépassée</p>\r\n                </div>\r\n                <div class=\"bg-warning/20 dark:bg-warning/30 p-3 rounded-xl\">\r\n                  <svg class=\"w-6 h-6 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Groupes uniques -->\r\n            <div class=\"bg-gradient-to-br from-info/10 to-info/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-info/20 dark:border-dark-accent-primary/30\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p class=\"text-xs font-medium text-info dark:text-dark-accent-primary uppercase tracking-wider\">Groupes</p>\r\n                  <p class=\"text-2xl font-bold text-info dark:text-dark-accent-primary\">{{ getUniqueGroupsCount() }}</p>\r\n                  <p class=\"text-xs text-text dark:text-dark-text-secondary mt-1\">Différents</p>\r\n                </div>\r\n                <div class=\"bg-info/20 dark:bg-dark-accent-primary/30 p-3 rounded-xl\">\r\n                  <svg class=\"w-6 h-6 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Barre de progression globale -->\r\n          <div class=\"mt-6 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4\">\r\n            <div class=\"flex items-center justify-between mb-2\">\r\n              <h4 class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary\">Progression des projets</h4>\r\n              <span class=\"text-xs text-text dark:text-dark-text-secondary\">{{ getCompletionPercentage() }}% complétés</span>\r\n            </div>\r\n            <div class=\"w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-2\">\r\n              <div class=\"bg-gradient-to-r from-success to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary h-2 rounded-full transition-all duration-500\"\r\n                   [style.width.%]=\"getCompletionPercentage()\"></div>\r\n            </div>\r\n            <div class=\"flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2\">\r\n              <span>{{ getActiveProjectsCount() }} actifs</span>\r\n              <span>{{ getExpiredProjectsCount() }} expirés</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton d'ajout moderne -->\r\n        <div class=\"flex flex-col sm:flex-row gap-3\">\r\n          <a routerLink=\"/admin/projects/new\"\r\n             class=\"group px-6 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n            <div class=\"flex items-center justify-center space-x-2\">\r\n              <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n              </svg>\r\n              <span>Nouveau projet</span>\r\n            </div>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Liste des projets en cartes modernes -->\r\n    <div *ngIf=\"!isLoading && projets && projets.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\r\n      <!-- Carte de projet moderne -->\r\n      <div *ngFor=\"let projet of projets\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden\">\r\n\r\n        <!-- Header du projet avec gradient -->\r\n        <div class=\"relative p-6 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10\">\r\n          <div class=\"flex items-start justify-between\">\r\n            <div class=\"flex-1 pr-4\">\r\n              <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2 line-clamp-2\">\r\n                {{ projet.titre }}\r\n              </h3>\r\n              <div class=\"flex items-center space-x-4\">\r\n                <!-- Badge groupe -->\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-primary dark:text-dark-accent-primary\">\r\n                    {{ projet.groupe || \"Tous\" }}\r\n                  </span>\r\n                </div>\r\n                <!-- Date limite -->\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">\r\n                    {{ projet.dateLimite | date : \"dd/MM/yyyy\" }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Actions flottantes -->\r\n            <div class=\"flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-200\">\r\n              <a [routerLink]=\"['/admin/projects/editProjet', projet._id]\"\r\n                 class=\"p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary transition-all duration-200 shadow-lg\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\r\n                </svg>\r\n              </a>\r\n              <button (click)=\"projet._id && openDeleteDialog(projet._id)\"\r\n                      class=\"p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-danger dark:text-danger-dark hover:bg-danger hover:text-white dark:hover:bg-danger-dark transition-all duration-200 shadow-lg\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Contenu du projet -->\r\n        <div class=\"p-6\">\r\n          <!-- Description -->\r\n          <div class=\"mb-6\">\r\n            <div class=\"flex items-center space-x-2 mb-3\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n              <h4 class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider\">\r\n                Description\r\n              </h4>\r\n            </div>\r\n            <p class=\"text-text dark:text-dark-text-secondary text-sm line-clamp-3 leading-relaxed\">\r\n              {{ projet.description || \"Aucune description fournie\" }}\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Section fichiers -->\r\n          <div *ngIf=\"projet.fichiers && projet.fichiers.length > 0\" class=\"mb-6\">\r\n            <div class=\"flex items-center space-x-2 mb-3\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n              <h4 class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider\">\r\n                Fichiers ({{ projet.fichiers.length }})\r\n              </h4>\r\n            </div>\r\n            <div class=\"space-y-2 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4\">\r\n              <div *ngFor=\"let file of projet.fichiers\" class=\"flex items-center justify-between p-2 bg-white dark:bg-dark-bg-secondary rounded-lg shadow-sm\">\r\n                <div class=\"flex items-center space-x-2 flex-1 min-w-0\">\r\n                  <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-1.5 rounded-lg\">\r\n                    <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <span class=\"text-sm text-text-dark dark:text-dark-text-primary truncate\">\r\n                    {{ getFileName(file) }}\r\n                  </span>\r\n                </div>\r\n                <a [href]=\"getFileUrl(file)\" [download]=\"getFileName(file)\"\r\n                   class=\"ml-2 px-3 py-1.5 bg-primary/10 dark:bg-dark-accent-primary/20 text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-lg transition-all duration-200 text-xs font-medium\">\r\n                  <div class=\"flex items-center space-x-1\">\r\n                    <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\r\n                    </svg>\r\n                    <span>Télécharger</span>\r\n                  </div>\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message aucun fichier -->\r\n          <div *ngIf=\"!projet.fichiers || projet.fichiers.length === 0\" class=\"mb-6\">\r\n            <div class=\"flex items-center space-x-2 mb-3\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n              <h4 class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider\">\r\n                Fichiers\r\n              </h4>\r\n            </div>\r\n            <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4 text-center\">\r\n              <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-2\">\r\n                <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                </svg>\r\n              </div>\r\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun fichier joint</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Actions principales -->\r\n          <div class=\"flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100 dark:border-dark-bg-tertiary/50\">\r\n            <a [routerLink]=\"['/admin/projects/details', projet._id]\"\r\n               class=\"flex-1 group px-4 py-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 text-text-dark dark:text-dark-text-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-xl transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n                <span>Voir détails</span>\r\n              </div>\r\n            </a>\r\n            <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{ projetId: projet._id }\"\r\n               class=\"flex-1 group px-4 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\r\n                </svg>\r\n                <span>Voir rendus</span>\r\n              </div>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État de chargement moderne -->\r\n    <div *ngIf=\"isLoading\" class=\"text-center py-16\">\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto\">\r\n        <div class=\"relative\">\r\n          <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary dark:border-dark-accent-primary/20 dark:border-t-dark-accent-primary mx-auto\"></div>\r\n          <div class=\"absolute inset-0 flex items-center justify-center\">\r\n            <div class=\"w-8 h-8 bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full animate-pulse\"></div>\r\n          </div>\r\n        </div>\r\n        <p class=\"mt-6 text-text-dark dark:text-dark-text-primary font-medium\">Chargement des projets...</p>\r\n        <p class=\"mt-2 text-sm text-text dark:text-dark-text-secondary\">Veuillez patienter</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État vide moderne -->\r\n    <div *ngIf=\"!isLoading && (!projets || projets.length === 0)\" class=\"text-center py-16\">\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto\">\r\n        <div class=\"bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6\">\r\n          <svg class=\"h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2\">Aucun projet disponible</h3>\r\n        <p class=\"text-text dark:text-dark-text-secondary mb-6\">Commencez par créer votre premier projet pour organiser vos cours</p>\r\n        <a routerLink=\"/admin/projects/new\"\r\n           class=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n          <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n          </svg>\r\n          Créer un projet\r\n        </a>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Boîte de dialogue de confirmation moderne -->\r\n    <div *ngIf=\"showDeleteDialog\" class=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\">\r\n      <div class=\"bg-white/95 dark:bg-dark-bg-secondary/95 backdrop-blur-sm rounded-2xl shadow-2xl w-full max-w-md p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50 mx-4\">\r\n        <div class=\"text-center mb-6\">\r\n          <div class=\"bg-danger/10 dark:bg-danger-dark/20 rounded-full p-4 w-16 h-16 mx-auto mb-4\">\r\n            <svg class=\"w-8 h-8 text-danger dark:text-danger-dark mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\r\n            </svg>\r\n          </div>\r\n          <h2 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2\">\r\n            {{ dialogData.title }}\r\n          </h2>\r\n          <p class=\"text-text dark:text-dark-text-secondary\">{{ dialogData.message }}</p>\r\n        </div>\r\n\r\n        <div class=\"flex flex-col sm:flex-row gap-3\">\r\n          <button (click)=\"onDeleteCancel()\"\r\n                  class=\"flex-1 px-6 py-3 bg-gray-100 dark:bg-dark-bg-tertiary text-text-dark dark:text-dark-text-primary hover:bg-gray-200 dark:hover:bg-dark-bg-tertiary/80 rounded-xl transition-all duration-200 font-medium\">\r\n            Annuler\r\n          </button>\r\n          <button (click)=\"onDeleteConfirm()\"\r\n                  class=\"flex-1 px-6 py-3 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n            Supprimer\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;;;ICqM5CC,EAAA,CAAAC,cAAA,cAAgJ;IAG1ID,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAA4L;IAC9LH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA0E;IAA1EL,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAC,cAAA,YACmP;IAE/OD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAG,SAAA,gBAAgJ;IAClJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IATxBJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAECX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB,aAAAL,MAAA,CAAAC,WAAA,CAAAC,OAAA;;;;;IArBlCX,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAsG;IAAtGL,EAAA,CAAAC,cAAA,aAAsG;IACpGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAe,UAAA,IAAAC,uDAAA,mBAoBM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IAzBFJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,gBAAAS,SAAA,CAAAC,QAAA,CAAAC,MAAA,OACF;IAGsBnB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAK,SAAA,CAAAC,QAAA,CAAkB;;;;;IAyB5ClB,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAsG;IAAtGL,EAAA,CAAAC,cAAA,aAAsG;IACpGD,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,cAA+E;IAE3ED,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAM,MAAA,2BAAmB;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;;;;;;;;;;;;;;;IArH1FJ,EAAA,CAAAC,cAAA,cAAwP;IAO9OD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAAyC;IAGrCD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAAwV;IAC1VH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAA6E;IAA7EL,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAGTJ,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAE,cAAA,EAA0G;IAA1GF,EAAA,CAAAC,cAAA,eAA0G;IACxGD,EAAA,CAAAG,SAAA,gBAA6H;IAC/HH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAA8D;IAA9DL,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAMbJ,EAAA,CAAAC,cAAA,eAA0F;IAGtFD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAAwM;IAC1MH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAC2N;IAD3NL,EAAA,CAAAC,cAAA,kBAC2N;IADnND,EAAA,CAAAoB,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,WAAA,GAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAP,SAAA,GAAAK,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAX,SAAA,CAAAY,GAAA,IAAcH,OAAA,CAAAI,gBAAA,CAAAb,SAAA,CAAAY,GAAA,CAA4B;IAAA,EAAC;IAE1D7B,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAA8M;IAChNH,EAAA,CAAAI,YAAA,EAAM;IAOdJ,EAAA,CAAAK,eAAA,EAAiB;IAAjBL,EAAA,CAAAC,cAAA,eAAiB;IAIXD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAG,SAAA,gBAA2I;IAC7IH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAsG;IAAtGL,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAINJ,EAAA,CAAAe,UAAA,KAAAgB,iDAAA,kBAgCM;IAGN/B,EAAA,CAAAe,UAAA,KAAAiB,iDAAA,mBAiBM;IAGNhC,EAAA,CAAAC,cAAA,eAA2G;IAIrGD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAG,SAAA,gBAA2I;IAC7IH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,yBAAY;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAG7BJ,EAAA,CAAAC,cAAA,aAC0O;IAEtOD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAG,SAAA,gBAAiN;IACnNH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,mBAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;IAnIxBJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,SAAA,CAAAgB,KAAA,MACF;IAQMjC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,SAAA,CAAAiB,MAAA,gBACF;IAQElC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAmC,WAAA,SAAAlB,SAAA,CAAAmB,UAAA,qBACF;IAODpC,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAqC,eAAA,KAAAC,GAAA,EAAArB,SAAA,CAAAY,GAAA,EAAyD;IA6B5D7B,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,SAAA,CAAAsB,WAAA,sCACF;IAIIvC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAY,UAAA,SAAAK,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,KAAmD;IAmCnDnB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,UAAAK,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,OAAsD;IAqBvDnB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAqC,eAAA,KAAAG,GAAA,EAAAvB,SAAA,CAAAY,GAAA,EAAsD;IAStD7B,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAyC,eAAA,KAAAC,GAAA,EAAyC,gBAAA1C,EAAA,CAAAqC,eAAA,KAAAM,GAAA,EAAA1B,SAAA,CAAAY,GAAA;;;;;IAtIpD7B,EAAA,CAAAC,cAAA,cAA2H;IAEzHD,EAAA,CAAAe,UAAA,IAAA6B,0CAAA,oBA+IM;IACR5C,EAAA,CAAAI,YAAA,EAAM;;;;IAhJoBJ,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAY,UAAA,YAAAiC,MAAA,CAAAC,OAAA,CAAU;;;;;IAmJpC9C,EAAA,CAAAC,cAAA,cAAiD;IAG3CD,EAAA,CAAAG,SAAA,eAAgL;IAChLH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAG,SAAA,eAAiK;IACnKH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,aAAuE;IAAAD,EAAA,CAAAM,MAAA,gCAAyB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACpGJ,EAAA,CAAAC,cAAA,aAAgE;IAAAD,EAAA,CAAAM,MAAA,yBAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IAK1FJ,EAAA,CAAAC,cAAA,cAAwF;IAGlFD,EAAA,CAAAE,cAAA,EAAgI;IAAhIF,EAAA,CAAAC,cAAA,eAAgI;IAC9HD,EAAA,CAAAG,SAAA,gBAAyJ;IAC3JH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA8E;IAA9EL,EAAA,CAAAC,cAAA,cAA8E;IAAAD,EAAA,CAAAM,MAAA,8BAAuB;IAAAN,EAAA,CAAAI,YAAA,EAAK;IAC1GJ,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAM,MAAA,6EAAiE;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAC7HJ,EAAA,CAAAC,cAAA,aACsP;IACpPD,EAAA,CAAAE,cAAA,EAAgF;IAAhFF,EAAA,CAAAC,cAAA,gBAAgF;IAC9ED,EAAA,CAAAG,SAAA,gBAA4G;IAC9GH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,8BACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;IAKRJ,EAAA,CAAAC,cAAA,eAAuH;IAI/GD,EAAA,CAAAE,cAAA,EAAqH;IAArHF,EAAA,CAAAC,cAAA,eAAqH;IACnHD,EAAA,CAAAG,SAAA,gBAA2N;IAC7NH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA8E;IAA9EL,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAAmD;IAAAD,EAAA,CAAAM,MAAA,GAAwB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAGjFJ,EAAA,CAAAC,cAAA,eAA6C;IACnCD,EAAA,CAAAoB,UAAA,mBAAA2B,8DAAA;MAAA/C,EAAA,CAAAuB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAqB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAEhClD,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBACgN;IADxMD,EAAA,CAAAoB,UAAA,mBAAA+B,8DAAA;MAAAnD,EAAA,CAAAuB,aAAA,CAAAyB,IAAA;MAAA,MAAAI,OAAA,GAAApD,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAwB,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAEjCrD,EAAA,CAAAM,MAAA,mBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAbPJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA8C,MAAA,CAAAC,UAAA,CAAAC,KAAA,MACF;IACmDxD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAyD,iBAAA,CAAAH,MAAA,CAAAC,UAAA,CAAAG,OAAA,CAAwB;;;ADjTrF,OAAM,MAAOC,oBAAoB;EAgB/BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,MAAiB,EACjBC,WAAwB;IAHxB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAnBrB,KAAAlB,OAAO,GAAa,EAAE;IACtB,KAAAmB,SAAS,GAAG,IAAI;IAChB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAkB,IAAI;IAIvC,KAAAb,UAAU,GAAG;MACXC,KAAK,EAAE,0BAA0B;MACjCE,OAAO,EAAE;KACV;EASE;EAEHW,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,aAAa,CAACW,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAG5B,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACmB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACX,SAAS,GAAG,KAAK;QACtBY,KAAK,CACH,yCAAyC,IACtCD,GAAG,CAACD,KAAK,EAAEjB,OAAO,IAAIkB,GAAG,CAAClB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;MACH;KACD,CAAC;EACJ;EAEA;EACAoB,YAAYA,CAAA;IACV,IAAI,CAACR,WAAW,EAAE;EACpB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACL,OAAO,GAAG,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;EAC3C;EAEAa,UAAUA,CAACC,QAA4B;IACrC,IAAI,CAACA,QAAQ,EAAE;MACb;;IAEF,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,sBAAsB,EAAED,QAAQ,CAAC,CAAC;EAC1D;EAEAE,iBAAiBA,CAACF,QAA4B;IAC5C,IAAI,CAACA,QAAQ,EAAE;MACb;;IAEF,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,wBAAwB,EAAED,QAAQ,CAAC,CAAC;EAC5D;EAEAG,YAAYA,CAACH,QAA4B;IACvC,IAAI,CAACA,QAAQ,EAAE;MACb;;IAGF,IAAII,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACvB,aAAa,CAACsB,YAAY,CAACH,QAAQ,CAAC,CAACP,SAAS,CAAC;QAClDC,IAAI,EAAEA,CAAA,KAAK;UACTG,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACP,WAAW,EAAE;QACpB,CAAC;QACDK,KAAK,EAAGC,GAAG,IAAI;UACbC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEjB,OAAO,IAAIkB,GAAG,CAAClB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACH;OACD,CAAC;;EAEN;EAEA5B,gBAAgBA,CAACuD,EAAU;IACzB,IAAI,CAACA,EAAE,EAAE;MACP;;IAEF,IAAI,CAAClB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,iBAAiB,GAAGiB,EAAE;EAC7B;EAEAhC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACe,iBAAiB,EAAE;MAC1B,IAAI,CAACP,aAAa,CAACsB,YAAY,CAAC,IAAI,CAACf,iBAAiB,CAAC,CAACK,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTG,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACP,WAAW,EAAE;UAClB,IAAI,CAACH,gBAAgB,GAAG,KAAK;QAC/B,CAAC;QACDQ,KAAK,EAAGC,GAAG,IAAI;UACbC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEjB,OAAO,IAAIkB,GAAG,CAAClB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;UACD,IAAI,CAACS,gBAAgB,GAAG,KAAK;QAC/B;OACD,CAAC;;EAEN;EAEAjB,cAAcA,CAAA;IACZ,IAAI,CAACiB,gBAAgB,GAAG,KAAK;EAC/B;EAEAtD,UAAUA,CAACyE,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACtE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGpB,WAAW,CAAC4F,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEA7E,WAAWA,CAAC4E,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACtE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOmE,QAAQ;EACjB;EAEAM,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC9C,OAAO,EAAE,OAAO,CAAC;IAE3B,MAAM+C,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,OAAO,IAAI,CAAChD,OAAO,CAACiD,MAAM,CAACC,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAAC5D,UAAU,EAAE,OAAO,IAAI,CAAC,CAAC;MACrC,MAAM6D,SAAS,GAAG,IAAIH,IAAI,CAACE,MAAM,CAAC5D,UAAU,CAAC;MAC7C,OAAO6D,SAAS,IAAIJ,GAAG,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC1E,MAAM;EACX;EAEA+E,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACpD,OAAO,EAAE,OAAO,CAAC;IAE3B,MAAM+C,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,OAAO,IAAI,CAAChD,OAAO,CAACiD,MAAM,CAACC,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAAC5D,UAAU,EAAE,OAAO,KAAK,CAAC,CAAC;MACtC,MAAM6D,SAAS,GAAG,IAAIH,IAAI,CAACE,MAAM,CAAC5D,UAAU,CAAC;MAC7C,OAAO6D,SAAS,GAAGJ,GAAG,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC1E,MAAM;EACX;EAEAgF,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrD,OAAO,EAAE,OAAO,CAAC;IAE3B,MAAMsD,YAAY,GAAG,IAAIC,GAAG,CAC1B,IAAI,CAACvD,OAAO,CACTwD,GAAG,CAACN,MAAM,IAAIA,MAAM,CAAC9D,MAAM,CAAC,CAC5B6D,MAAM,CAAC7D,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACqE,IAAI,EAAE,KAAK,EAAE,CAAC,CACpD;IAED,OAAOH,YAAY,CAACI,IAAI;EAC1B;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3D,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAExD,MAAMuF,YAAY,GAAG,IAAI,CAACR,uBAAuB,EAAE;IACnD,MAAMS,UAAU,GAAG,IAAI,CAAC7D,OAAO,CAAC3B,MAAM;IAEtC,OAAOyF,IAAI,CAACC,KAAK,CAAEH,YAAY,GAAGC,UAAU,GAAI,GAAG,CAAC;EACtD;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAChE,OAAO,EAAE,OAAO,EAAE;IAE5B,MAAMiE,WAAW,GAA8B,EAAE;IAEjD,IAAI,CAACjE,OAAO,CAACkE,OAAO,CAAChB,MAAM,IAAG;MAC5B,MAAM9D,MAAM,GAAG8D,MAAM,CAAC9D,MAAM,IAAI,cAAc;MAC9C6E,WAAW,CAAC7E,MAAM,CAAC,GAAG,CAAC6E,WAAW,CAAC7E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACtD,CAAC,CAAC;IAEF,OAAO6E,WAAW;EACpB;EAEAE,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACnE,OAAO,EAAE,OAAO,EAAE;IAE5B;IACA,MAAM+C,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMoB,eAAe,GAAG,IAAIpB,IAAI,EAAE;IAClCoB,eAAe,CAACC,QAAQ,CAACD,eAAe,CAACE,QAAQ,EAAE,GAAG,CAAC,CAAC;IAExD,OAAO,IAAI,CAACtE,OAAO,CAACiD,MAAM,CAACC,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAAC5D,UAAU,EAAE,OAAO,KAAK;MACpC,MAAMiF,QAAQ,GAAG,IAAIvB,IAAI,CAACE,MAAM,CAAC5D,UAAU,CAAC;MAC5C,OAAOiF,QAAQ,IAAIxB,GAAG,IAAIwB,QAAQ,IAAIH,eAAe;IACvD,CAAC,CAAC;EACJ;EAEAI,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACxE,OAAO,EAAE,OAAO,EAAE;IAE5B,MAAM+C,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMyB,cAAc,GAAG,IAAIzB,IAAI,EAAE;IACjCyB,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAEpD,OAAO,IAAI,CAAC3E,OAAO,CAACiD,MAAM,CAACC,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAAC5D,UAAU,EAAE,OAAO,KAAK;MACpC,MAAMiF,QAAQ,GAAG,IAAIvB,IAAI,CAACE,MAAM,CAAC5D,UAAU,CAAC;MAC5C,OAAOiF,QAAQ,IAAIxB,GAAG,IAAIwB,QAAQ,IAAIE,cAAc;IACtD,CAAC,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACf,MAAMC,KAAK,GAAG,IAAI/B,IAAI,CAAC6B,CAAC,CAACvF,UAAW,CAAC;MACrC,MAAM0F,KAAK,GAAG,IAAIhC,IAAI,CAAC8B,CAAC,CAACxF,UAAW,CAAC;MACrC,OAAOyF,KAAK,CAACE,OAAO,EAAE,GAAGD,KAAK,CAACC,OAAO,EAAE;IAC1C,CAAC,CAAC;EACJ;;;uBA3OWpE,oBAAoB,EAAA3D,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,SAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB7E,oBAAoB;MAAA8E,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCZjC5I,EAAA,CAAAC,cAAA,aAAiK;UASnJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAG,SAAA,cAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,UAAK;UAEDD,EAAA,CAAAM,MAAA,6BACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAAmD;UACjDD,EAAA,CAAAM,MAAA,uEACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAAmD;UAKwDD,EAAA,CAAAM,MAAA,aAAK;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC5GJ,EAAA,CAAAC,cAAA,aAAyE;UAAAD,EAAA,CAAAM,MAAA,IAAyB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACtGJ,EAAA,CAAAC,cAAA,aAAgE;UAAAD,EAAA,CAAAM,MAAA,+BAAa;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEnFJ,EAAA,CAAAC,cAAA,eAAyE;UACvED,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,eAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAMZJ,EAAA,CAAAK,eAAA,EAA6M;UAA7ML,EAAA,CAAAC,cAAA,eAA6M;UAGlGD,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC/GJ,EAAA,CAAAC,cAAA,aAA2E;UAAAD,EAAA,CAAAM,MAAA,IAA8B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC7GJ,EAAA,CAAAC,cAAA,aAAgE;UAAAD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAE9EJ,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAE,cAAA,EAAwH;UAAxHF,EAAA,CAAAC,cAAA,eAAwH;UACtHD,EAAA,CAAAG,SAAA,gBAA+H;UACjIH,EAAA,CAAAI,YAAA,EAAM;UAMZJ,EAAA,CAAAK,eAAA,EAAmK;UAAnKL,EAAA,CAAAC,cAAA,eAAmK;UAGtED,EAAA,CAAAM,MAAA,oBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAClGJ,EAAA,CAAAC,cAAA,aAA6D;UAAAD,EAAA,CAAAM,MAAA,IAA+B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAChGJ,EAAA,CAAAC,cAAA,aAAgE;UAAAD,EAAA,CAAAM,MAAA,+BAAa;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEnFJ,EAAA,CAAAC,cAAA,eAA6D;UAC3DD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,eAA0G;UACxGD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UAMZJ,EAAA,CAAAK,eAAA,EAA8L;UAA9LL,EAAA,CAAAC,cAAA,eAA8L;UAGxFD,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC3GJ,EAAA,CAAAC,cAAA,aAAsE;UAAAD,EAAA,CAAAM,MAAA,IAA4B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACtGJ,EAAA,CAAAC,cAAA,aAAgE;UAAAD,EAAA,CAAAM,MAAA,uBAAU;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEhFJ,EAAA,CAAAC,cAAA,eAAsE;UACpED,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAwV;UAC1VH,EAAA,CAAAI,YAAA,EAAM;UAOdJ,EAAA,CAAAK,eAAA,EAAwE;UAAxEL,EAAA,CAAAC,cAAA,eAAwE;UAESD,EAAA,CAAAM,MAAA,+BAAuB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACzGJ,EAAA,CAAAC,cAAA,gBAA8D;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAEjHJ,EAAA,CAAAC,cAAA,eAA0E;UACxED,EAAA,CAAAG,SAAA,eACuD;UACzDH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAuF;UAC/ED,EAAA,CAAAM,MAAA,IAAqC;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAClDJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,IAAuC;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAM1DJ,EAAA,CAAAC,cAAA,eAA6C;UAIvCD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,sBAAc;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAQrCJ,EAAA,CAAAe,UAAA,KAAA+H,oCAAA,kBAkJM;UAGN9I,EAAA,CAAAe,UAAA,KAAAgI,oCAAA,mBAWM;UAGN/I,EAAA,CAAAe,UAAA,KAAAiI,oCAAA,mBAiBM;UAGNhJ,EAAA,CAAAe,UAAA,KAAAkI,oCAAA,mBAyBM;UACRjJ,EAAA,CAAAI,YAAA,EAAM;;;UA9SmFJ,EAAA,CAAAO,SAAA,IAAyB;UAAzBP,EAAA,CAAAyD,iBAAA,CAAAoF,GAAA,CAAA/F,OAAA,CAAA3B,MAAA,MAAyB;UAgBvBnB,EAAA,CAAAO,SAAA,IAA8B;UAA9BP,EAAA,CAAAyD,iBAAA,CAAAoF,GAAA,CAAAjD,sBAAA,GAA8B;UAgB5C5F,EAAA,CAAAO,SAAA,IAA+B;UAA/BP,EAAA,CAAAyD,iBAAA,CAAAoF,GAAA,CAAA3C,uBAAA,GAA+B;UAgBtBlG,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAyD,iBAAA,CAAAoF,GAAA,CAAA1C,oBAAA,GAA4B;UAgBxCnG,EAAA,CAAAO,SAAA,IAA0C;UAA1CP,EAAA,CAAAQ,kBAAA,KAAAqI,GAAA,CAAApC,uBAAA,4BAA0C;UAInGzG,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAkJ,WAAA,UAAAL,GAAA,CAAApC,uBAAA,QAA2C;UAG1CzG,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,kBAAA,KAAAqI,GAAA,CAAAjD,sBAAA,cAAqC;UACrC5F,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,kBAAA,KAAAqI,GAAA,CAAA3C,uBAAA,oBAAuC;UAqBjDlG,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAAY,UAAA,UAAAiI,GAAA,CAAA5E,SAAA,IAAA4E,GAAA,CAAA/F,OAAA,IAAA+F,GAAA,CAAA/F,OAAA,CAAA3B,MAAA,KAAiD;UAqJjDnB,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAY,UAAA,SAAAiI,GAAA,CAAA5E,SAAA,CAAe;UAcfjE,EAAA,CAAAO,SAAA,GAAsD;UAAtDP,EAAA,CAAAY,UAAA,UAAAiI,GAAA,CAAA5E,SAAA,MAAA4E,GAAA,CAAA/F,OAAA,IAAA+F,GAAA,CAAA/F,OAAA,CAAA3B,MAAA,QAAsD;UAoBtDnB,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAY,UAAA,SAAAiI,GAAA,CAAA1E,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}