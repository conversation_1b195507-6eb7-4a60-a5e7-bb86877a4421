{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 17);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 20);\n    i0.ɵɵelement(3, \"path\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"p\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_ng_container_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 57);\n    i0.ɵɵelement(2, \"path\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((fichier_r7 == null ? null : fichier_r7.split(\"/\").pop()) || \"Fichier\");\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_32_ng_container_7_a_1_Template, 5, 2, \"a\", 55);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", fichier_r7);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 51);\n    i0.ɵɵelement(3, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53);\n    i0.ɵɵtemplate(7, ProjectEvaluationComponent_div_15_div_32_ng_container_7_Template, 2, 1, \"ng-container\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Fichiers joints (\", ctx_r3.rendu.fichiers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51__svg_svg_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 107);\n    i0.ɵɵelement(1, \"path\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51__svg_svg_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 108);\n    i0.ɵɵelement(1, \"path\", 109);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_span_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre l'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_span_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"form\", 60);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_15_form_51_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"div\", 25)(3, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 27);\n    i0.ɵɵelement(5, \"path\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h3\", 64);\n    i0.ɵɵtext(7, \"Crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 65)(9, \"div\", 66)(10, \"label\", 67)(11, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 69);\n    i0.ɵɵelement(13, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Structure du code\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 14);\n    i0.ɵɵelement(17, \"input\", 71);\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"span\", 73);\n    i0.ɵɵtext(20, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 66)(22, \"label\", 67)(23, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 69);\n    i0.ɵɵelement(25, \"path\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 14);\n    i0.ɵɵelement(29, \"input\", 74);\n    i0.ɵɵelementStart(30, \"div\", 72)(31, \"span\", 73);\n    i0.ɵɵtext(32, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 66)(34, \"label\", 67)(35, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 69);\n    i0.ɵɵelement(37, \"path\", 75)(38, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"Fonctionnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 14);\n    i0.ɵɵelement(42, \"input\", 77);\n    i0.ɵɵelementStart(43, \"div\", 72)(44, \"span\", 73);\n    i0.ɵɵtext(45, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(46, \"div\", 66)(47, \"label\", 67)(48, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(49, \"svg\", 69);\n    i0.ɵɵelement(50, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 14);\n    i0.ɵɵelement(54, \"input\", 78);\n    i0.ɵɵelementStart(55, \"div\", 72)(56, \"span\", 73);\n    i0.ɵɵtext(57, \"/5\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(58, \"div\", 79)(59, \"div\", 80)(60, \"div\", 19)(61, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(62, \"svg\", 27);\n    i0.ɵɵelement(63, \"path\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(64, \"span\", 81);\n    i0.ɵɵtext(65, \"Score total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 82)(67, \"div\", 83);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 84);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"div\", 85);\n    i0.ɵɵelement(72, \"div\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 87)(74, \"span\");\n    i0.ɵɵtext(75, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\");\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 61)(79, \"div\", 25)(80, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(81, \"svg\", 27);\n    i0.ɵɵelement(82, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(83, \"h3\", 64);\n    i0.ɵɵtext(84, \"Commentaires d\\u00E9taill\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 14);\n    i0.ɵɵelement(86, \"textarea\", 90);\n    i0.ɵɵelementStart(87, \"div\", 91);\n    i0.ɵɵtext(88, \" Minimum 50 caract\\u00E8res \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(90, \"svg\", 93);\n    i0.ɵɵelement(91, \"path\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(92, \"div\", 95)(93, \"p\", 96);\n    i0.ɵɵtext(94, \"Conseils pour une \\u00E9valuation de qualit\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"ul\", 97)(96, \"li\");\n    i0.ɵɵtext(97, \"\\u2022 Mentionnez les aspects techniques r\\u00E9ussis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"li\");\n    i0.ɵɵtext(99, \"\\u2022 Identifiez les points d'am\\u00E9lioration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"li\");\n    i0.ɵɵtext(101, \"\\u2022 Proposez des suggestions constructives\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"li\");\n    i0.ɵɵtext(103, \"\\u2022 \\u00C9valuez la cr\\u00E9ativit\\u00E9 et l'originalit\\u00E9\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(104, \"div\", 98)(105, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_form_51_Template_button_click_105_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.annuler());\n    });\n    i0.ɵɵelementStart(106, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(107, \"svg\", 101);\n    i0.ɵɵelement(108, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(109, \"span\");\n    i0.ɵɵtext(110, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(111, \"button\", 103)(112, \"div\", 100);\n    i0.ɵɵtemplate(113, ProjectEvaluationComponent_div_15_form_51__svg_svg_113_Template, 2, 0, \"svg\", 104);\n    i0.ɵɵtemplate(114, ProjectEvaluationComponent_div_15_form_51__svg_svg_114_Template, 2, 0, \"svg\", 105);\n    i0.ɵɵtemplate(115, ProjectEvaluationComponent_div_15_form_51_span_115_Template, 2, 0, \"span\", 106);\n    i0.ɵɵtemplate(116, ProjectEvaluationComponent_div_15_form_51_span_116_Template, 2, 0, \"span\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(68);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"sur \", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.getScoreTotal() / ctx_r4.getScoreMaximum() * 100, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreMaximum());\n    i0.ɵɵadvance(36);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 107);\n    i0.ɵɵelement(1, \"path\", 125);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 108);\n    i0.ɵɵelement(1, \"path\", 109);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancer l'\\u00E9valuation IA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancement en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 25)(2, \"div\", 112);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 113);\n    i0.ɵɵelement(4, \"path\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 29);\n    i0.ɵɵtext(7, \"\\u00C9valuation automatique par IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 114);\n    i0.ɵɵtext(9, \"Syst\\u00E8me d'intelligence artificielle Mistral 7B\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 115)(11, \"div\", 116)(12, \"div\", 117);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 27);\n    i0.ɵɵelement(14, \"path\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"div\")(16, \"h4\", 118);\n    i0.ɵɵtext(17, \"Comment fonctionne l'\\u00E9valuation IA ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 119);\n    i0.ɵɵtext(19, \"Notre syst\\u00E8me d'IA analysera automatiquement le code soumis selon les crit\\u00E8res suivants :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 120)(21, \"div\", 68);\n    i0.ɵɵelement(22, \"div\", 121);\n    i0.ɵɵelementStart(23, \"span\", 122);\n    i0.ɵɵtext(24, \"Structure et organisation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 68);\n    i0.ɵɵelement(26, \"div\", 121);\n    i0.ɵɵelementStart(27, \"span\", 122);\n    i0.ɵɵtext(28, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 68);\n    i0.ɵɵelement(30, \"div\", 121);\n    i0.ɵɵelementStart(31, \"span\", 122);\n    i0.ɵɵtext(32, \"Fonctionnalit\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 68);\n    i0.ɵɵelement(34, \"div\", 121);\n    i0.ɵɵelementStart(35, \"span\", 122);\n    i0.ɵɵtext(36, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(37, \"div\", 123)(38, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.annuler());\n    });\n    i0.ɵɵelementStart(39, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 101);\n    i0.ɵɵelement(41, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.onSubmit());\n    });\n    i0.ɵɵelementStart(45, \"div\", 100);\n    i0.ɵɵtemplate(46, ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_46_Template, 2, 0, \"svg\", 104);\n    i0.ɵɵtemplate(47, ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_47_Template, 2, 0, \"svg\", 105);\n    i0.ɵɵtemplate(48, ProjectEvaluationComponent_div_15_div_52_div_1_span_48_Template, 2, 0, \"span\", 106);\n    i0.ɵɵtemplate(49, ProjectEvaluationComponent_div_15_div_52_div_1_span_49_Template, 2, 0, \"span\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(44);\n    i0.ɵɵproperty(\"disabled\", ctx_r17.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"div\", 127);\n    i0.ɵɵelement(2, \"div\", 128)(3, \"div\", 129);\n    i0.ɵɵelementStart(4, \"div\", 130);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 131);\n    i0.ɵɵelement(6, \"path\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 132);\n    i0.ɵɵtext(8, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 133);\n    i0.ɵɵtext(10, \"Notre syst\\u00E8me examine le code selon les crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 134)(12, \"div\", 135);\n    i0.ɵɵelement(13, \"div\", 136)(14, \"div\", 137)(15, \"div\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 84);\n    i0.ɵɵtext(17, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_52_div_1_Template, 50, 5, \"div\", 106);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_15_div_52_div_2_Template, 18, 0, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 27);\n    i0.ɵɵelement(5, \"path\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 29);\n    i0.ɵɵtext(7, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"div\", 31)(10, \"div\", 32)(11, \"p\", 33);\n    i0.ɵɵtext(12, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 34);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"p\", 33);\n    i0.ɵɵtext(17, \"\\u00C9tudiant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 34);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 31)(21, \"div\", 36)(22, \"p\", 33);\n    i0.ɵɵtext(23, \"Date de soumission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 34);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 37)(28, \"p\", 33);\n    i0.ɵɵtext(29, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 38);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(32, ProjectEvaluationComponent_div_15_div_32_Template, 8, 2, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 24)(34, \"div\", 40)(35, \"div\", 19)(36, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 27);\n    i0.ɵɵelement(38, \"path\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"h2\", 29);\n    i0.ɵɵtext(40, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 4)(42, \"div\", 42)(43, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.evaluationMode = \"manual\");\n    });\n    i0.ɵɵtext(44, \" Manuel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.evaluationMode = \"ai\");\n    });\n    i0.ɵɵtext(46, \" IA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.toggleEvaluationMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(48, \"svg\", 45);\n    i0.ɵɵelement(49, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" Basculer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(51, ProjectEvaluationComponent_div_15_form_51_Template, 117, 10, \"form\", 47);\n    i0.ɵɵtemplate(52, ProjectEvaluationComponent_div_15_div_52_Template, 3, 2, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.projet.titre);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 12, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassMap(ctx_r2.evaluationMode === \"manual\" ? \"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md\" : \"text-text dark:text-dark-text-secondary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r2.evaluationMode === \"ai\" ? \"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md\" : \"text-text dark:text-dark-text-secondary\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        // Filter out null/undefined files\n        if (this.rendu.fichiers) {\n          this.rendu.fichiers = this.rendu.fichiers.filter(fichier => fichier != null && fichier !== '');\n        }\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    // Confirmer avant d'annuler si des données ont été saisies\n    const formData = this.evaluationForm.value;\n    const hasData = formData.scores?.structure || formData.scores?.pratiques || formData.scores?.fonctionnalite || formData.scores?.originalite || formData.commentaires;\n    if (hasData) {\n      const confirmation = confirm('Êtes-vous sûr de vouloir annuler ? Toutes les données saisies seront perdues.');\n      if (!confirmation) {\n        return;\n      }\n    }\n    console.log('Navigation vers la liste des rendus...');\n    this.router.navigate(['/admin/projects/rendus']);\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 16,\n      vars: 3,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-5xl\", \"mx-auto\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"space-y-8\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-4\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"space-y-8\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-2xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"space-y-4\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-primary-dark/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-1\"], [1, \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"text-lg\"], [1, \"bg-gradient-to-r\", \"from-secondary/5\", \"to-secondary-dark/5\", \"dark:from-dark-accent-secondary/10\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\"], [1, \"bg-gradient-to-r\", \"from-info/5\", \"to-info/10\", \"dark:from-dark-accent-primary/5\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\"], [1, \"bg-gradient-to-r\", \"from-success/5\", \"to-success/10\", \"dark:from-success/10\", \"dark:to-success/5\", \"rounded-xl\", \"p-4\"], [1, \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"class\", \"mt-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-tertiary/50 rounded-xl p-4\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"sm:justify-between\", \"gap-4\", \"mb-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"rounded-xl\", \"p-1\", \"shadow-inner\"], [1, \"px-4\", \"py-2\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"class\", \"space-y-8\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [1, \"mt-6\", \"bg-gradient-to-r\", \"from-gray-50\", \"to-gray-100\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"lg:grid-cols-3\", \"gap-3\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", \"class\", \"flex items-center space-x-2 p-3 bg-white dark:bg-dark-bg-secondary rounded-lg hover:bg-primary/5 dark:hover:bg-dark-accent-primary/10 transition-all duration-200 border border-gray-200 dark:border-dark-bg-tertiary group\", 3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"space-x-2\", \"p-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"rounded-lg\", \"hover:bg-primary/5\", \"dark:hover:bg-dark-accent-primary/10\", \"transition-all\", \"duration-200\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"group\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"space-y-8\", 3, \"formGroup\", \"ngSubmit\"], [1, \"bg-gradient-to-br\", \"from-gray-50/50\", \"to-white/50\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-secondary/30\", \"rounded-2xl\", \"p-6\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-3\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-4\"], [1, \"text-sm\", \"font-medium\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [1, \"mt-8\", \"bg-gradient-to-r\", \"from-primary/5\", \"to-primary-dark/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-6\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-right\"], [1, \"text-3xl\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-3\", \"overflow-hidden\"], [1, \"h-full\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"D\\u00E9crivez les points forts et les axes d'am\\u00E9lioration du projet. Soyez pr\\u00E9cis et constructif dans vos commentaires...\", 1, \"w-full\", \"px-4\", \"py-4\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\", \"resize-none\"], [1, \"absolute\", \"bottom-3\", \"right-3\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"mt-4\", \"flex\", \"items-start\", \"space-x-3\", \"p-4\", \"bg-primary/5\", \"dark:bg-dark-accent-primary/10\", \"rounded-xl\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"font-medium\", \"mb-1\"], [1, \"space-y-1\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\", \"pt-6\"], [\"type\", \"button\", 1, \"w-full\", \"sm:w-auto\", \"group\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"rounded-xl\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"transition-all\", \"duration-200\", \"font-medium\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-gray-300\", \"dark:hover:border-dark-bg-tertiary/60\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"w-full\", \"sm:w-auto\", \"group\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-xl\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-semibold\", \"border-2\", \"border-transparent\", \"hover:border-success/30\", \"dark:hover:border-dark-accent-primary/30\"], [\"class\", \"w-5 h-5 group-hover:scale-110 transition-transform\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-5 h-5 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [4, \"ngIf\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"animate-spin\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [1, \"bg-gradient-to-br\", \"from-gray-50/50\", \"to-white/50\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-secondary/30\", \"rounded-2xl\", \"p-8\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [1, \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-start\", \"space-x-4\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\", \"flex-shrink-0\"], [1, \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-3\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-3\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"font-medium\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\"], [1, \"w-full\", \"sm:w-auto\", \"group\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-xl\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-semibold\", \"border-2\", \"border-transparent\", \"hover:border-secondary/30\", \"dark:hover:border-dark-accent-secondary/30\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-center\", \"py-12\"], [1, \"relative\", \"mb-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-20\", \"w-20\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\", \"mx-auto\"], [1, \"animate-spin\", \"rounded-full\", \"h-20\", \"w-20\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-1/2\", \"transform\", \"-translate-x-1/2\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-primary\", \"dark:text-dark-accent-primary\", \"animate-pulse\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-4\", \"max-w-md\", \"mx-auto\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"mb-2\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n          i0.ɵɵtext(10, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Syst\\u00E8me d'\\u00E9valuation intelligent avec IA int\\u00E9gr\\u00E9e\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(13, ProjectEvaluationComponent_div_13_Template, 6, 0, \"div\", 10);\n          i0.ɵɵtemplate(14, ProjectEvaluationComponent_div_14_Template, 6, 1, \"div\", 11);\n          i0.ɵɵtemplate(15, ProjectEvaluationComponent_div_15_Template, 53, 15, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(79, 95, 173, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(79, 95, 173, 0.6), 0 0 30px rgba(79, 95, 173, 0.4);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_glowDark {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 247, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 247, 255, 0.6), 0 0 30px rgba(0, 247, 255, 0.4);\\n  }\\n}\\n\\n\\n\\n.form-input-enhanced[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.form-input-enhanced[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.btn-modern[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n\\n\\n.glass-card[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.animate-fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n.animate-slide-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out;\\n}\\n\\n\\n\\n.progress-bar-animated[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f5fad, #7826b5);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .progress-bar-animated[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #00f7ff, #9d4edd);\\n  background-size: 200% 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity 0.3s;\\n  z-index: 1000;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .grid-responsive[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .btn-modern[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n\\n\\n.focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #00f7ff;\\n}\\n\\n\\n\\n.icon-hover[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n\\n.icon-hover[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(5deg);\\n}\\n\\n\\n\\n.alert-modern[_ngcontent-%COMP%] {\\n  border-left: 4px solid;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .alert-modern[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "split", "pop", "ɵɵelementContainerStart", "ɵɵtemplate", "ProjectEvaluationComponent_div_15_div_32_ng_container_7_a_1_Template", "ɵɵelementContainerEnd", "ProjectEvaluationComponent_div_15_div_32_ng_container_7_Template", "ɵɵtextInterpolate1", "ctx_r3", "rendu", "fichiers", "length", "ɵɵlistener", "ProjectEvaluationComponent_div_15_form_51_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_15_form_51_Template_button_click_105_listener", "ctx_r16", "annuler", "ProjectEvaluationComponent_div_15_form_51__svg_svg_113_Template", "ProjectEvaluationComponent_div_15_form_51__svg_svg_114_Template", "ProjectEvaluationComponent_div_15_form_51_span_115_Template", "ProjectEvaluationComponent_div_15_form_51_span_116_Template", "ctx_r4", "evaluationForm", "getScoreTotal", "getScoreMaximum", "ɵɵstyleProp", "isSubmitting", "ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_38_listener", "_r24", "ctx_r23", "ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_44_listener", "ctx_r25", "ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_46_Template", "ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_47_Template", "ProjectEvaluationComponent_div_15_div_52_div_1_span_48_Template", "ProjectEvaluationComponent_div_15_div_52_div_1_span_49_Template", "ctx_r17", "ProjectEvaluationComponent_div_15_div_52_div_1_Template", "ProjectEvaluationComponent_div_15_div_52_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_15_div_32_Template", "ProjectEvaluationComponent_div_15_Template_button_click_43_listener", "_r27", "ctx_r26", "evaluationMode", "ProjectEvaluationComponent_div_15_Template_button_click_45_listener", "ctx_r28", "ProjectEvaluationComponent_div_15_Template_button_click_47_listener", "ctx_r29", "toggleEvaluationMode", "ProjectEvaluationComponent_div_15_form_51_Template", "ProjectEvaluationComponent_div_15_div_52_Template", "ctx_r2", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "ɵɵclassMap", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "filter", "<PERSON><PERSON><PERSON>", "err", "console", "log", "valid", "value", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "formData", "hasData", "confirmation", "confirm", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_13_Template", "ProjectEvaluationComponent_div_14_Template", "ProjectEvaluationComponent_div_15_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        // Filter out null/undefined files\r\n        if (this.rendu.fichiers) {\r\n          this.rendu.fichiers = this.rendu.fichiers.filter((fichier: any) => fichier != null && fichier !== '');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    // Confirmer avant d'annuler si des données ont été saisies\r\n    const formData = this.evaluationForm.value;\r\n    const hasData = formData.scores?.structure || formData.scores?.pratiques ||\r\n                   formData.scores?.fonctionnalite || formData.scores?.originalite ||\r\n                   formData.commentaires;\r\n\r\n    if (hasData) {\r\n      const confirmation = confirm('Êtes-vous sûr de vouloir annuler ? Toutes les données saisies seront perdues.');\r\n      if (!confirmation) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    console.log('Navigation vers la liste des rendus...');\r\n    this.router.navigate(['/admin/projects/rendus']);\r\n  }\r\n\r\n\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n    <div class=\"max-w-5xl mx-auto\">\r\n      <!-- Header avec gradient -->\r\n      <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-white mb-2\">Évaluation du projet</h1>\r\n            <p class=\"text-white/80\">Système d'évaluation intelligent avec IA intégrée</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Loading State -->\r\n      <div *ngIf=\"isLoading\" class=\"flex flex-col items-center justify-center py-16\">\r\n        <div class=\"relative\">\r\n          <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30\"></div>\r\n          <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0\"></div>\r\n        </div>\r\n        <p class=\"mt-4 text-text dark:text-dark-text-secondary animate-pulse\">Chargement des données...</p>\r\n      </div>\r\n\r\n      <!-- Error State -->\r\n      <div *ngIf=\"error\" class=\"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <p class=\"font-medium\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Contenu principal -->\r\n      <div *ngIf=\"rendu && !isLoading\" class=\"space-y-8\">\r\n        <!-- Informations sur le rendu -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-6\">\r\n            <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h2 class=\"text-2xl font-bold text-text-dark dark:text-dark-text-primary\">Informations sur le rendu</h2>\r\n          </div>\r\n\r\n          <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            <div class=\"space-y-4\">\r\n              <div class=\"bg-gradient-to-r from-primary/5 to-primary-dark/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-xl p-4\">\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary mb-1\">Projet</p>\r\n                <p class=\"font-semibold text-text-dark dark:text-dark-text-primary text-lg\">{{ rendu.projet.titre }}</p>\r\n              </div>\r\n              <div class=\"bg-gradient-to-r from-secondary/5 to-secondary-dark/5 dark:from-dark-accent-secondary/10 dark:to-dark-accent-primary/10 rounded-xl p-4\">\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary mb-1\">Étudiant</p>\r\n                <p class=\"font-semibold text-text-dark dark:text-dark-text-primary text-lg\">{{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"space-y-4\">\r\n              <div class=\"bg-gradient-to-r from-info/5 to-info/10 dark:from-dark-accent-primary/5 dark:to-dark-accent-primary/10 rounded-xl p-4\">\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary mb-1\">Date de soumission</p>\r\n                <p class=\"font-semibold text-text-dark dark:text-dark-text-primary text-lg\">{{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n              </div>\r\n              <div class=\"bg-gradient-to-r from-success/5 to-success/10 dark:from-success/10 dark:to-success/5 rounded-xl p-4\">\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary mb-1\">Description</p>\r\n                <p class=\"font-semibold text-text-dark dark:text-dark-text-primary\">{{ rendu.description || 'Aucune description' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Fichiers joints -->\r\n          <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-tertiary/50 rounded-xl p-4\">\r\n            <div class=\"flex items-center space-x-2 mb-3\">\r\n              <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"></path>\r\n              </svg>\r\n              <h3 class=\"font-semibold text-text-dark dark:text-dark-text-primary\">Fichiers joints ({{ rendu.fichiers.length }})</h3>\r\n            </div>\r\n            <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\r\n              <ng-container *ngFor=\"let fichier of rendu.fichiers\">\r\n                <a *ngIf=\"fichier\"\r\n                   [href]=\"'http://localhost:3000/' + fichier\"\r\n                   target=\"_blank\"\r\n                   class=\"flex items-center space-x-2 p-3 bg-white dark:bg-dark-bg-secondary rounded-lg hover:bg-primary/5 dark:hover:bg-dark-accent-primary/10 transition-all duration-200 border border-gray-200 dark:border-dark-bg-tertiary group\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary truncate\">{{ fichier?.split('/').pop() || 'Fichier' }}</span>\r\n                </a>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Mode d'évaluation -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\r\n                <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h2 class=\"text-2xl font-bold text-text-dark dark:text-dark-text-primary\">Mode d'évaluation</h2>\r\n            </div>\r\n\r\n            <div class=\"flex items-center space-x-4\">\r\n              <!-- Mode selector -->\r\n              <div class=\"flex items-center bg-gray-100 dark:bg-dark-bg-tertiary rounded-xl p-1 shadow-inner\">\r\n                <button\r\n                  [class]=\"evaluationMode === 'manual' ? 'bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md' : 'text-text dark:text-dark-text-secondary'\"\r\n                  class=\"px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n                  (click)=\"evaluationMode = 'manual'\"\r\n                >\r\n                  Manuel\r\n                </button>\r\n                <button\r\n                  [class]=\"evaluationMode === 'ai' ? 'bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md' : 'text-text dark:text-dark-text-secondary'\"\r\n                  class=\"px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm\"\r\n                  (click)=\"evaluationMode = 'ai'\"\r\n                >\r\n                  IA\r\n                </button>\r\n              </div>\r\n\r\n              <!-- Toggle button -->\r\n              <button (click)=\"toggleEvaluationMode()\" class=\"px-4 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n                <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"></path>\r\n                </svg>\r\n                Basculer\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Formulaire d'évaluation manuelle -->\r\n          <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\" class=\"space-y-8\">\r\n            <!-- Critères d'évaluation -->\r\n            <div class=\"bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-6 border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n              <div class=\"flex items-center space-x-3 mb-6\">\r\n                <div class=\"bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\r\n                  <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">Critères d'évaluation</h3>\r\n              </div>\r\n\r\n              <div formGroupName=\"scores\" class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <!-- Structure du code -->\r\n                <div class=\"group\">\r\n                  <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n                      </svg>\r\n                      <span>Structure du code</span>\r\n                    </div>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"structure\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-4\">\r\n                      <span class=\"text-sm font-medium text-text dark:text-dark-text-secondary\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Bonnes pratiques -->\r\n                <div class=\"group\">\r\n                  <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                      </svg>\r\n                      <span>Bonnes pratiques</span>\r\n                    </div>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"pratiques\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-4\">\r\n                      <span class=\"text-sm font-medium text-text dark:text-dark-text-secondary\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Fonctionnalité -->\r\n                <div class=\"group\">\r\n                  <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n                      </svg>\r\n                      <span>Fonctionnalité</span>\r\n                    </div>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"fonctionnalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-4\">\r\n                      <span class=\"text-sm font-medium text-text dark:text-dark-text-secondary\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Originalité -->\r\n                <div class=\"group\">\r\n                  <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                      </svg>\r\n                      <span>Originalité</span>\r\n                    </div>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"originalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-4\">\r\n                      <span class=\"text-sm font-medium text-text dark:text-dark-text-secondary\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Score total avec barre de progression -->\r\n              <div class=\"mt-8 bg-gradient-to-r from-primary/5 to-primary-dark/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-2xl p-6 border border-primary/20 dark:border-dark-accent-primary/30\">\r\n                <div class=\"flex items-center justify-between mb-4\">\r\n                  <div class=\"flex items-center space-x-3\">\r\n                    <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\r\n                      <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <span class=\"text-lg font-bold text-text-dark dark:text-dark-text-primary\">Score total</span>\r\n                  </div>\r\n                  <div class=\"text-right\">\r\n                    <div class=\"text-3xl font-bold text-primary dark:text-dark-accent-primary\">{{ getScoreTotal() }}</div>\r\n                    <div class=\"text-sm text-text dark:text-dark-text-secondary\">sur {{ getScoreMaximum() }}</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-3 overflow-hidden\">\r\n                  <div\r\n                    class=\"h-full bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full transition-all duration-500 ease-out\"\r\n                    [style.width.%]=\"(getScoreTotal() / getScoreMaximum()) * 100\"\r\n                  ></div>\r\n                </div>\r\n                <div class=\"flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2\">\r\n                  <span>0</span>\r\n                  <span>{{ getScoreMaximum() }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Section commentaires -->\r\n            <div class=\"bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-6 border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n              <div class=\"flex items-center space-x-3 mb-6\">\r\n                <div class=\"bg-gradient-to-r from-info to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary p-2 rounded-lg\">\r\n                  <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">Commentaires détaillés</h3>\r\n              </div>\r\n\r\n              <div class=\"relative\">\r\n                <textarea\r\n                  formControlName=\"commentaires\"\r\n                  rows=\"6\"\r\n                  class=\"w-full px-4 py-4 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary resize-none\"\r\n                  placeholder=\"Décrivez les points forts et les axes d'amélioration du projet. Soyez précis et constructif dans vos commentaires...\"\r\n                ></textarea>\r\n                <div class=\"absolute bottom-3 right-3 text-xs text-text dark:text-dark-text-secondary bg-white/80 dark:bg-dark-bg-secondary/80 px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                  Minimum 50 caractères\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"mt-4 flex items-start space-x-3 p-4 bg-primary/5 dark:bg-dark-accent-primary/10 rounded-xl border border-primary/20 dark:border-dark-accent-primary/30\">\r\n                <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n                <div class=\"text-sm text-text-dark dark:text-dark-text-primary\">\r\n                  <p class=\"font-medium mb-1\">Conseils pour une évaluation de qualité :</p>\r\n                  <ul class=\"space-y-1 text-text dark:text-dark-text-secondary\">\r\n                    <li>• Mentionnez les aspects techniques réussis</li>\r\n                    <li>• Identifiez les points d'amélioration</li>\r\n                    <li>• Proposez des suggestions constructives</li>\r\n                    <li>• Évaluez la créativité et l'originalité</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Boutons d'action -->\r\n            <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center pt-6\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"w-full sm:w-auto group px-6 py-3 bg-gray-100 dark:bg-dark-bg-tertiary text-text-dark dark:text-dark-text-primary rounded-xl hover:bg-gray-200 dark:hover:bg-dark-bg-tertiary/80 transition-all duration-200 font-medium border-2 border-gray-200 dark:border-dark-bg-tertiary hover:border-gray-300 dark:hover:border-dark-bg-tertiary/60\"\r\n              >\r\n                <div class=\"flex items-center justify-center space-x-2\">\r\n                  <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                  <span>Annuler</span>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                class=\"w-full sm:w-auto group px-8 py-3 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-xl hover:scale-105 transition-all duration-200 font-semibold border-2 border-transparent hover:border-success/30 dark:hover:border-dark-accent-primary/30\"\r\n              >\r\n                <div class=\"flex items-center justify-center space-x-2\">\r\n                  <svg *ngIf=\"!isSubmitting\" class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <svg *ngIf=\"isSubmitting\" class=\"w-5 h-5 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\r\n                  </svg>\r\n                  <span *ngIf=\"!isSubmitting\">Soumettre l'évaluation</span>\r\n                  <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          <!-- Évaluation par IA -->\r\n          <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n            <div *ngIf=\"!aiProcessing\">\r\n              <div class=\"flex items-center space-x-3 mb-6\">\r\n                <div class=\"bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary p-3 rounded-xl\">\r\n                  <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <div>\r\n                  <h3 class=\"text-2xl font-bold text-text-dark dark:text-dark-text-primary\">Évaluation automatique par IA</h3>\r\n                  <p class=\"text-text dark:text-dark-text-secondary\">Système d'intelligence artificielle Mistral 7B</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-2xl p-6 mb-8 border border-primary/20 dark:border-dark-accent-primary/30\">\r\n                <div class=\"flex items-start space-x-4\">\r\n                  <div class=\"bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg flex-shrink-0\">\r\n                    <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"font-bold text-text-dark dark:text-dark-text-primary mb-3\">Comment fonctionne l'évaluation IA ?</h4>\r\n                    <p class=\"text-text dark:text-dark-text-secondary mb-4\">Notre système d'IA analysera automatiquement le code soumis selon les critères suivants :</p>\r\n                    <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n                      <div class=\"flex items-center space-x-2\">\r\n                        <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full\"></div>\r\n                        <span class=\"text-sm text-text-dark dark:text-dark-text-primary font-medium\">Structure et organisation</span>\r\n                      </div>\r\n                      <div class=\"flex items-center space-x-2\">\r\n                        <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full\"></div>\r\n                        <span class=\"text-sm text-text-dark dark:text-dark-text-primary font-medium\">Bonnes pratiques</span>\r\n                      </div>\r\n                      <div class=\"flex items-center space-x-2\">\r\n                        <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full\"></div>\r\n                        <span class=\"text-sm text-text-dark dark:text-dark-text-primary font-medium\">Fonctionnalités</span>\r\n                      </div>\r\n                      <div class=\"flex items-center space-x-2\">\r\n                        <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full\"></div>\r\n                        <span class=\"text-sm text-text-dark dark:text-dark-text-primary font-medium\">Originalité</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Boutons d'action IA -->\r\n              <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"annuler()\"\r\n                  class=\"w-full sm:w-auto group px-6 py-3 bg-gray-100 dark:bg-dark-bg-tertiary text-text-dark dark:text-dark-text-primary rounded-xl hover:bg-gray-200 dark:hover:bg-dark-bg-tertiary/80 transition-all duration-200 font-medium border-2 border-gray-200 dark:border-dark-bg-tertiary hover:border-gray-300 dark:hover:border-dark-bg-tertiary/60\"\r\n                >\r\n                  <div class=\"flex items-center justify-center space-x-2\">\r\n                    <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                    </svg>\r\n                    <span>Annuler</span>\r\n                  </div>\r\n                </button>\r\n\r\n                <button\r\n                  (click)=\"onSubmit()\"\r\n                  [disabled]=\"isSubmitting\"\r\n                  class=\"w-full sm:w-auto group px-8 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-xl hover:scale-105 transition-all duration-200 font-semibold border-2 border-transparent hover:border-secondary/30 dark:hover:border-dark-accent-secondary/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\r\n                >\r\n                  <div class=\"flex items-center justify-center space-x-2\">\r\n                    <svg *ngIf=\"!isSubmitting\" class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\r\n                    </svg>\r\n                    <svg *ngIf=\"isSubmitting\" class=\"w-5 h-5 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\r\n                    </svg>\r\n                    <span *ngIf=\"!isSubmitting\">Lancer l'évaluation IA</span>\r\n                    <span *ngIf=\"isSubmitting\">Lancement en cours...</span>\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- État de traitement IA -->\r\n            <div *ngIf=\"aiProcessing\" class=\"text-center py-12\">\r\n              <div class=\"relative mb-8\">\r\n                <div class=\"animate-spin rounded-full h-20 w-20 border-4 border-primary/30 dark:border-dark-accent-primary/30 mx-auto\"></div>\r\n                <div class=\"animate-spin rounded-full h-20 w-20 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-1/2 transform -translate-x-1/2\"></div>\r\n                <div class=\"absolute inset-0 flex items-center justify-center\">\r\n                  <svg class=\"w-8 h-8 text-primary dark:text-dark-accent-primary animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2\">L'IA analyse le projet...</h3>\r\n              <p class=\"text-text dark:text-dark-text-secondary mb-6\">Notre système examine le code selon les critères d'évaluation</p>\r\n              <div class=\"bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-2xl p-4 max-w-md mx-auto border border-primary/20 dark:border-dark-accent-primary/30\">\r\n                <div class=\"flex items-center justify-center space-x-2 mb-2\">\r\n                  <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full animate-bounce\"></div>\r\n                  <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full animate-bounce\" style=\"animation-delay: 0.1s\"></div>\r\n                  <div class=\"w-2 h-2 bg-gradient-to-r from-primary to-secondary dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full animate-bounce\" style=\"animation-delay: 0.2s\"></div>\r\n                </div>\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary\">Cela peut prendre quelques instants</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICkB7DC,EAAA,CAAAC,cAAA,cAA+E;IAE3ED,EAAA,CAAAE,SAAA,cAAqH;IAEvHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAI,MAAA,qCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAIrGH,EAAA,CAAAC,cAAA,cAAyL;IAErLD,EAAA,CAAAK,cAAA,EAA2H;IAA3HL,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,eAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAuB;IAAvBN,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAkD5BV,EAAA,CAAAC,cAAA,YAGuO;IACrOD,EAAA,CAAAK,cAAA,EAAiK;IAAjKL,EAAA,CAAAC,cAAA,cAAiK;IAC/JD,EAAA,CAAAE,SAAA,eAAiN;IACnNF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAsF;IAAtFN,EAAA,CAAAC,cAAA,eAAsF;IAAAD,EAAA,CAAAI,MAAA,GAA4C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IANxIH,EAAA,CAAAW,UAAA,oCAAAC,UAAA,EAAAZ,EAAA,CAAAa,aAAA,CAA2C;IAM0Cb,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAQ,iBAAA,EAAAI,UAAA,kBAAAA,UAAA,CAAAE,KAAA,MAAAC,GAAA,iBAA4C;;;;;IARtIf,EAAA,CAAAgB,uBAAA,GAAqD;IACnDhB,EAAA,CAAAiB,UAAA,IAAAC,oEAAA,gBAQI;IACNlB,EAAA,CAAAmB,qBAAA,EAAe;;;;IATTnB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAW,UAAA,SAAAC,UAAA,CAAa;;;;;IATvBZ,EAAA,CAAAC,cAAA,cAAyL;IAErLD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAqE;IAArEN,EAAA,CAAAC,cAAA,aAAqE;IAAAD,EAAA,CAAAI,MAAA,GAA6C;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEzHH,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAiB,UAAA,IAAAG,gEAAA,2BAUe;IACjBpB,EAAA,CAAAG,YAAA,EAAM;;;;IAdiEH,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAqB,kBAAA,sBAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA,MAA6C;IAGhFzB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,UAAA,YAAAW,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IAoQ/CxB,EAAA,CAAAK,cAAA,EAA4I;IAA5IL,EAAA,CAAAC,cAAA,eAA4I;IAC1ID,EAAA,CAAAE,SAAA,cAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAK,cAAA,EAA6G;IAA7GL,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAAE,SAAA,gBAA6L;IAC/LF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;;IAlNhEH,EAAA,CAAAM,eAAA,EAAiH;IAAjHN,EAAA,CAAAC,cAAA,eAAiH;IAA9ED,EAAA,CAAA0B,UAAA,sBAAAC,4EAAA;MAAA3B,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAA+B,aAAA;MAAA,OAAY/B,EAAA,CAAAgC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAExDjC,EAAA,CAAAC,cAAA,cAAgM;IAG1LD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAsR;IACxRF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAyE;IAAzEN,EAAA,CAAAC,cAAA,aAAyE;IAAAD,EAAA,CAAAI,MAAA,sCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGrGH,EAAA,CAAAC,cAAA,cAA0E;IAKlED,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAA4J;IAC9JF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,yBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGlCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACaD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMzFH,EAAA,CAAAC,cAAA,eAAmB;IAGbD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,eAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACaD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMzFH,EAAA,CAAAC,cAAA,eAAmB;IAGbD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAAqjB;IAEvjBF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,2BAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG/BH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACaD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMzFH,EAAA,CAAAC,cAAA,eAAmB;IAGbD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,wBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG5BH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACaD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAO3FH,EAAA,CAAAC,cAAA,eAAkN;IAI1MD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAAsR;IACxRF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAA2E;IAA3EN,EAAA,CAAAC,cAAA,gBAA2E;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE/FH,EAAA,CAAAC,cAAA,eAAwB;IACqDD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtGH,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAI,MAAA,IAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGlGH,EAAA,CAAAC,cAAA,eAA0F;IACxFD,EAAA,CAAAE,SAAA,eAGO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuF;IAC/ED,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAM1CH,EAAA,CAAAC,cAAA,eAAgM;IAG1LD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAA6K;IAC/KF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAyE;IAAzEN,EAAA,CAAAC,cAAA,cAAyE;IAAAD,EAAA,CAAAI,MAAA,wCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGtGH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,oBAKY;IACZF,EAAA,CAAAC,cAAA,eAAsK;IACpKD,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAoK;IAClKD,EAAA,CAAAK,cAAA,EAA2I;IAA3IL,EAAA,CAAAC,cAAA,eAA2I;IACzID,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAgE;IAAhEN,EAAA,CAAAC,cAAA,eAAgE;IAClCD,EAAA,CAAAI,MAAA,2DAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzEH,EAAA,CAAAC,cAAA,cAA8D;IACxDD,EAAA,CAAAI,MAAA,6DAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,wDAAsC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,WAAI;IAAAD,EAAA,CAAAI,MAAA,sDAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,WAAI;IAAAD,EAAA,CAAAI,MAAA,0EAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAOzDH,EAAA,CAAAC,cAAA,gBAA+E;IAG3ED,EAAA,CAAA0B,UAAA,mBAAAQ,6EAAA;MAAAlC,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAnC,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAG,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnBpC,EAAA,CAAAC,cAAA,iBAAwD;IACtDD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,iBAAsH;IACpHD,EAAA,CAAAE,SAAA,kBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIxBH,EAAA,CAAAC,cAAA,oBAGC;IAEGD,EAAA,CAAAiB,UAAA,MAAAoB,+DAAA,mBAEM;IACNrC,EAAA,CAAAiB,UAAA,MAAAqB,+DAAA,mBAEM;IACNtC,EAAA,CAAAiB,UAAA,MAAAsB,2DAAA,oBAAyD;IACzDvC,EAAA,CAAAiB,UAAA,MAAAuB,2DAAA,oBAAwD;IAC1DxC,EAAA,CAAAG,YAAA,EAAM;;;;IAnNNH,EAAA,CAAAW,UAAA,cAAA8B,MAAA,CAAAC,cAAA,CAA4B;IA+HmD1C,EAAA,CAAAO,SAAA,IAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAiC,MAAA,CAAAE,aAAA,GAAqB;IACnC3C,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAqB,kBAAA,SAAAoB,MAAA,CAAAG,eAAA,OAA2B;IAMxF5C,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAA6C,WAAA,UAAAJ,MAAA,CAAAE,aAAA,KAAAF,MAAA,CAAAG,eAAA,cAA6D;IAKzD5C,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAQ,iBAAA,CAAAiC,MAAA,CAAAG,eAAA,GAAuB;IAgEvB5C,EAAA,CAAAO,SAAA,IAAmB;IAAnBP,EAAA,CAAAW,UAAA,UAAA8B,MAAA,CAAAK,YAAA,CAAmB;IAGnB9C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAA8B,MAAA,CAAAK,YAAA,CAAkB;IAGjB9C,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,UAAA8B,MAAA,CAAAK,YAAA,CAAmB;IACnB9C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAA8B,MAAA,CAAAK,YAAA,CAAkB;;;;;IA0EvB9C,EAAA,CAAAK,cAAA,EAA4I;IAA5IL,EAAA,CAAAC,cAAA,eAA4I;IAC1ID,EAAA,CAAAE,SAAA,gBAA4G;IAC9GF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAK,cAAA,EAA6G;IAA7GL,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAAE,SAAA,gBAA6L;IAC/LF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAzE/DH,EAAA,CAAAC,cAAA,UAA2B;IAGrBD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAK;IAALN,EAAA,CAAAC,cAAA,UAAK;IACuED,EAAA,CAAAI,MAAA,yCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5GH,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAI,MAAA,0DAA8C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIzGH,EAAA,CAAAC,cAAA,gBAA+M;IAGzMD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAK;IAALN,EAAA,CAAAC,cAAA,WAAK;IACmED,EAAA,CAAAI,MAAA,iDAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/GH,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAI,MAAA,2GAAyF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACrJH,EAAA,CAAAC,cAAA,gBAAmD;IAE/CD,EAAA,CAAAE,SAAA,gBAA+I;IAC/IF,EAAA,CAAAC,cAAA,iBAA6E;IAAAD,EAAA,CAAAI,MAAA,iCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE/GH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAE,SAAA,gBAA+I;IAC/IF,EAAA,CAAAC,cAAA,iBAA6E;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEtGH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAE,SAAA,gBAA+I;IAC/IF,EAAA,CAAAC,cAAA,iBAA6E;IAAAD,EAAA,CAAAI,MAAA,4BAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErGH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAE,SAAA,gBAA+I;IAC/IF,EAAA,CAAAC,cAAA,iBAA6E;IAAAD,EAAA,CAAAI,MAAA,wBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAQzGH,EAAA,CAAAC,cAAA,gBAA0E;IAGtED,EAAA,CAAA0B,UAAA,mBAAAqB,iFAAA;MAAA/C,EAAA,CAAA4B,aAAA,CAAAoB,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAiB,OAAA,CAAAb,OAAA,EAAS;IAAA,EAAC;IAGnBpC,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,gBAAsH;IACpHD,EAAA,CAAAE,SAAA,iBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIxBH,EAAA,CAAAC,cAAA,mBAIC;IAHCD,EAAA,CAAA0B,UAAA,mBAAAwB,iFAAA;MAAAlD,EAAA,CAAA4B,aAAA,CAAAoB,IAAA;MAAA,MAAAG,OAAA,GAAAnD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAmB,OAAA,CAAAlB,QAAA,EAAU;IAAA,EAAC;IAIpBjC,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAiB,UAAA,KAAAmC,mEAAA,mBAEM;IACNpD,EAAA,CAAAiB,UAAA,KAAAoC,mEAAA,mBAEM;IACNrD,EAAA,CAAAiB,UAAA,KAAAqC,+DAAA,oBAAyD;IACzDtD,EAAA,CAAAiB,UAAA,KAAAsC,+DAAA,oBAAuD;IACzDvD,EAAA,CAAAG,YAAA,EAAM;;;;IAZNH,EAAA,CAAAO,SAAA,IAAyB;IAAzBP,EAAA,CAAAW,UAAA,aAAA6C,OAAA,CAAAV,YAAA,CAAyB;IAIjB9C,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,UAAA6C,OAAA,CAAAV,YAAA,CAAmB;IAGnB9C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAA6C,OAAA,CAAAV,YAAA,CAAkB;IAGjB9C,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,UAAA6C,OAAA,CAAAV,YAAA,CAAmB;IACnB9C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAA6C,OAAA,CAAAV,YAAA,CAAkB;;;;;IAOjC9C,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,eAA6H;IAE7HF,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAK,cAAA,EAAoI;IAApIL,EAAA,CAAAC,cAAA,eAAoI;IAClID,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAM,eAAA,EAA8E;IAA9EN,EAAA,CAAAC,cAAA,cAA8E;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5GH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAI,MAAA,oFAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzHH,EAAA,CAAAC,cAAA,gBAA2N;IAEvND,EAAA,CAAAE,SAAA,gBAA8J;IAGhKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAI,MAAA,2CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAnGxGH,EAAA,CAAAM,eAAA,EAAgO;IAAhON,EAAA,CAAAC,cAAA,eAAgO;IAC9ND,EAAA,CAAAiB,UAAA,IAAAwC,uDAAA,oBA6EM;IAGNzD,EAAA,CAAAiB,UAAA,IAAAyC,uDAAA,oBAoBM;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IArGEH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,UAAA,UAAAgD,MAAA,CAAAC,YAAA,CAAmB;IAgFnB5D,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,UAAA,SAAAgD,MAAA,CAAAC,YAAA,CAAkB;;;;;;IA/Y9B5D,EAAA,CAAAC,cAAA,cAAmD;IAK3CD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAA0E;IAA1EN,EAAA,CAAAC,cAAA,aAA0E;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAG1GH,EAAA,CAAAC,cAAA,cAAmD;IAGmBD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,aAA4E;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE1GH,EAAA,CAAAC,cAAA,eAAoJ;IAClFD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC5EH,EAAA,CAAAC,cAAA,aAA4E;IAAAD,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGxIH,EAAA,CAAAC,cAAA,eAAuB;IAE6CD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtFH,EAAA,CAAAC,cAAA,aAA4E;IAAAD,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEtIH,EAAA,CAAAC,cAAA,eAAiH;IAC/CD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC/EH,EAAA,CAAAC,cAAA,aAAoE;IAAAD,EAAA,CAAAI,MAAA,IAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAM7HH,EAAA,CAAAiB,UAAA,KAAA4C,iDAAA,kBAoBM;IACR7D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2J;IAInJD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAA0E;IAA1EN,EAAA,CAAAC,cAAA,cAA0E;IAAAD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGlGH,EAAA,CAAAC,cAAA,cAAyC;IAMnCD,EAAA,CAAA0B,UAAA,mBAAAoC,oEAAA;MAAA9D,EAAA,CAAA4B,aAAA,CAAAmC,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAAAgC,OAAA,CAAAC,cAAA,GAA0B,QAAQ;IAAA,EAAC;IAEnCjE,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAA0B,UAAA,mBAAAwC,oEAAA;MAAAlE,EAAA,CAAA4B,aAAA,CAAAmC,IAAA;MAAA,MAAAI,OAAA,GAAAnE,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAAAmC,OAAA,CAAAF,cAAA,GAA0B,IAAI;IAAA,EAAC;IAE/BjE,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,kBAAmQ;IAA3PD,EAAA,CAAA0B,UAAA,mBAAA0C,oEAAA;MAAApE,EAAA,CAAA4B,aAAA,CAAAmC,IAAA;MAAA,MAAAM,OAAA,GAAArE,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAqC,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IACtCtE,EAAA,CAAAK,cAAA,EAAuF;IAAvFL,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAiB,UAAA,KAAAsD,kDAAA,sBAsNO;IAGPvE,EAAA,CAAAiB,UAAA,KAAAuD,iDAAA,kBAsGM;IACRxE,EAAA,CAAAG,YAAA,EAAM;;;;IArZ8EH,EAAA,CAAAO,SAAA,IAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAiE,MAAA,CAAAlD,KAAA,CAAAmD,MAAA,CAAAC,KAAA,CAAwB;IAIxB3E,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAA4E,kBAAA,KAAAH,MAAA,CAAAlD,KAAA,CAAAsD,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAAlD,KAAA,CAAAsD,QAAA,CAAAE,MAAA,KAAoD;IAMpD/E,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAgF,WAAA,SAAAP,MAAA,CAAAlD,KAAA,CAAA0D,cAAA,sBAAoD;IAI5DjF,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAQ,iBAAA,CAAAiE,MAAA,CAAAlD,KAAA,CAAA2D,WAAA,yBAA+C;IAMnHlF,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAW,UAAA,SAAA8D,MAAA,CAAAlD,KAAA,CAAAC,QAAA,IAAAiD,MAAA,CAAAlD,KAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAuC/CzB,EAAA,CAAAO,SAAA,IAA6K;IAA7KP,EAAA,CAAAmF,UAAA,CAAAV,MAAA,CAAAR,cAAA,sJAA6K;IAO7KjE,EAAA,CAAAO,SAAA,GAAyK;IAAzKP,EAAA,CAAAmF,UAAA,CAAAV,MAAA,CAAAR,cAAA,kJAAyK;IAmBrHjE,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAW,UAAA,SAAA8D,MAAA,CAAAR,cAAA,cAAiC;IAyNvFjE,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAW,UAAA,SAAA8D,MAAA,CAAAR,cAAA,UAA6B;;;AD1V7C,OAAM,MAAOmB,0BAA0B;EAUrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAbvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAnE,KAAK,GAAQ,IAAI;IAEjB,KAAAoE,SAAS,GAAY,IAAI;IACzB,KAAA7C,YAAY,GAAY,KAAK;IAC7B,KAAApC,KAAK,GAAW,EAAE;IAClB,KAAAuD,cAAc,GAAoB,QAAQ;IAC1C,KAAAL,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAAClB,cAAc,GAAG,IAAI,CAAC4C,EAAE,CAACM,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACP,EAAE,CAACM,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC/F,UAAU,CAACgG,QAAQ,EAAEhG,UAAU,CAACiG,GAAG,CAAC,CAAC,CAAC,EAAEjG,UAAU,CAACkG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACnG,UAAU,CAACgG,QAAQ,EAAEhG,UAAU,CAACiG,GAAG,CAAC,CAAC,CAAC,EAAEjG,UAAU,CAACkG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACpG,UAAU,CAACgG,QAAQ,EAAEhG,UAAU,CAACiG,GAAG,CAAC,CAAC,CAAC,EAAEjG,UAAU,CAACkG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACrG,UAAU,CAACgG,QAAQ,EAAEhG,UAAU,CAACiG,GAAG,CAAC,CAAC,CAAC,EAAEjG,UAAU,CAACkG,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAEtG,UAAU,CAACgG,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,OAAO,GAAG,IAAI,CAACH,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACpB,KAAK,CAACiB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAAC1C,cAAc,GAAG0C,IAAI;MAC1B,IAAI,CAACjE,cAAc,CAACmE,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC/C,cAAc,GAAG+C,UAAU;QAChC,IAAI,CAACtE,cAAc,CAACmE,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACtB,OAAO,EAAE;MAChB,IAAI,CAACwB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAACxG,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACiF,SAAS,GAAG,KAAK;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC0B,YAAY,CAAC,IAAI,CAACzB,OAAO,CAAC,CAAC0B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAAC/F,KAAK,GAAG+F,IAAI;QACjB;QACA,IAAI,IAAI,CAAC/F,KAAK,CAACC,QAAQ,EAAE;UACvB,IAAI,CAACD,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACC,QAAQ,CAAC+F,MAAM,CAAEC,OAAY,IAAKA,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAK,EAAE,CAAC;;QAEvG,IAAI,CAAC7B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDjF,KAAK,EAAG+G,GAAQ,IAAI;QAClB,IAAI,CAAC/G,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACiF,SAAS,GAAG,KAAK;QACtB+B,OAAO,CAAChH,KAAK,CAAC+G,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAnD,oBAAoBA,CAAA;IAClB,IAAI,CAACL,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACvB,cAAc,CAACmE,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACrC,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E6C,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC9C,cAAc,CAAC;EAC7D;EAEAhC,QAAQA,CAAA;IACNyF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACjF,cAAc,CAACkF,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACjF,cAAc,CAACmF,KAAK,CAAC;IAEtD,IAAI,CAAC/E,YAAY,GAAG,IAAI;IACxB,IAAI,CAACpC,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAACuD,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACvB,cAAc,CAACmE,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAAC1C,YAAY,GAAG,IAAI;;IAG1B,MAAMkE,cAAc,GAAG,IAAI,CAACpF,cAAc,CAACmF,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEG,cAAc,CAAC;IAEvD,IAAI,CAACrC,aAAa,CAACsC,aAAa,CAAC,IAAI,CAACrC,OAAO,EAAEoC,cAAc,CAAC,CAACV,SAAS,CAAC;MACvEC,IAAI,EAAGW,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC/D,cAAc,KAAK,IAAI,IAAI+D,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACpC,MAAM;UAC3C,MAAMsC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC5B,YAAY;UAEvD,IAAI,CAAC3D,cAAc,CAACmE,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEoC,QAAQ,CAACpC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAEgC,QAAQ,CAAChC,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE+B,QAAQ,CAAC/B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE8B,QAAQ,CAAC9B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE8B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACvE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACd,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAACpC,KAAK,GAAG,EAAE;UACf0H,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAACtF,YAAY,GAAG,KAAK;UACzBsF,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;;MAEpD,CAAC;MACD3H,KAAK,EAAG+G,GAAQ,IAAI;QAClB,IAAI,CAAC/G,KAAK,GAAG,yCAAyC,IAAI+G,GAAG,CAAC/G,KAAK,EAAE4H,OAAO,IAAIb,GAAG,CAACa,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAACxF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACc,YAAY,GAAG,KAAK;QACzB8D,OAAO,CAAChH,KAAK,CAAC+G,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEA9E,aAAaA,CAAA;IACX,MAAMkD,MAAM,GAAG,IAAI,CAACnD,cAAc,CAACgE,GAAG,CAAC,QAAQ,CAAC,EAAEmB,KAAK;IACvD,IAAI,CAAChC,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAxD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAR,OAAOA,CAAA;IACL;IACA,MAAMmG,QAAQ,GAAG,IAAI,CAAC7F,cAAc,CAACmF,KAAK;IAC1C,MAAMW,OAAO,GAAGD,QAAQ,CAAC1C,MAAM,EAAEC,SAAS,IAAIyC,QAAQ,CAAC1C,MAAM,EAAEK,SAAS,IACzDqC,QAAQ,CAAC1C,MAAM,EAAEM,cAAc,IAAIoC,QAAQ,CAAC1C,MAAM,EAAEO,WAAW,IAC/DmC,QAAQ,CAAClC,YAAY;IAEpC,IAAImC,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGC,OAAO,CAAC,+EAA+E,CAAC;MAC7G,IAAI,CAACD,YAAY,EAAE;QACjB;;;IAIJf,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,IAAI,CAACnC,MAAM,CAAC6C,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;;;uBAlKWjD,0BAA0B,EAAApF,EAAA,CAAA2I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7I,EAAA,CAAA2I,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/I,EAAA,CAAA2I,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAhJ,EAAA,CAAA2I,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1B9D,0BAA0B;MAAA+D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCzJ,EAAA,CAAAC,cAAA,aAA8F;UAOlFD,EAAA,CAAAK,cAAA,EAAsF;UAAtFL,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAE,SAAA,cAA+H;UACjIF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAM,eAAA,EAAK;UAALN,EAAA,CAAAC,cAAA,UAAK;UAC4CD,EAAA,CAAAI,MAAA,iCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAI,MAAA,6EAAiD;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAMpFH,EAAA,CAAAiB,UAAA,KAAA0I,0CAAA,kBAMM;UAGN3J,EAAA,CAAAiB,UAAA,KAAA2I,0CAAA,kBAOM;UAGN5J,EAAA,CAAAiB,UAAA,KAAA4I,0CAAA,oBAsaI;UACR7J,EAAA,CAAAG,YAAA,EAAM;;;UA1bIH,EAAA,CAAAO,SAAA,IAAe;UAAfP,EAAA,CAAAW,UAAA,SAAA+I,GAAA,CAAA/D,SAAA,CAAe;UASf3F,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAW,UAAA,SAAA+I,GAAA,CAAAhJ,KAAA,CAAW;UAUXV,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAW,UAAA,SAAA+I,GAAA,CAAAnI,KAAA,KAAAmI,GAAA,CAAA/D,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}