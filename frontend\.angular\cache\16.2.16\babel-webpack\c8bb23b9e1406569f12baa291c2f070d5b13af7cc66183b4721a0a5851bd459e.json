{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport class ReunionService {\n  constructor(http, jwtHelper) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n  }\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getAllReunions() {\n    return this.http.get(`${environment.urlBackend}reunions/getall`);\n  }\n  getReunionById(id) {\n    return this.http.get(`${environment.urlBackend}reunions/getone/${id}`);\n  }\n  createReunion(reunion) {\n    return this.http.post(`${environment.urlBackend}reunions/add`, reunion, {\n      headers: this.getUserHeaders()\n    });\n  }\n  updateReunion(id, reunion) {\n    return this.http.put(`${environment.urlBackend}reunions/update/${id}`, reunion, {\n      headers: this.getUserHeaders()\n    });\n  }\n  deleteReunion(id) {\n    return this.http.delete(`${environment.urlBackend}reunions/delete/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  /**\n   * Vérifie l'unicité d'un lien de visioconférence\n   * @param lienVisio Le lien à vérifier\n   * @param excludeReunionId ID de la réunion à exclure (pour la modification)\n   */\n  checkLienVisioUniqueness(lienVisio, excludeReunionId) {\n    const body = {\n      lienVisio,\n      excludeReunionId\n    };\n    return this.http.post(`${environment.urlBackend}reunions/check-lien-visio`, body, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getReunionsByPlanning(planningId) {\n    return this.http.get(`${environment.urlBackend}reunions/planning/${planningId}`);\n  }\n  getProchainesReunions(userId) {\n    return this.http.get(`${environment.urlBackend}reunions/user/${userId}`);\n  }\n  // Méthode pour les admins - récupère toutes les réunions\n  getAllReunionsAdmin() {\n    return this.http.get(`${environment.urlBackend}reunions/admin/all`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  // Méthode pour les admins - suppression forcée\n  forceDeleteReunion(id) {\n    return this.http.delete(`${environment.urlBackend}reunions/admin/force-delete/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  static {\n    this.ɵfac = function ReunionService_Factory(t) {\n      return new (t || ReunionService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReunionService,\n      factory: ReunionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "ReunionService", "constructor", "http", "jwtHelper", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getAllReunions", "get", "urlBackend", "getReunionById", "id", "createReunion", "reunion", "post", "headers", "updateReunion", "put", "deleteReunion", "delete", "checkLienVisioUniqueness", "lienVisio", "excludeReunionId", "body", "getReunionsByPlanning", "planningId", "getProchainesReunions", "userId", "getAllReunionsAdmin", "forceDeleteReunion", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\reunion.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {HttpHeaders,HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Reunion, CreateReunionRequest } from '../models/reunion.model';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ReunionService {\r\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService)\r\n  {}\r\n   private getUserHeaders(): HttpHeaders {\r\n     const token = localStorage.getItem('token');\r\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n       throw new Error('Token invalide ou expiré');\r\n     }\r\n     return new HttpHeaders({\r\n       Authorization: `Bearer ${token || ''}`,\r\n       'Content-Type': 'application/json',\r\n     });\r\n   }\r\n\r\n getAllReunions(): Observable<Reunion[]> {\r\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/getall`);\r\n }\r\n getReunionById(id: string): Observable<Reunion> {\r\n   return this.http.get<Reunion>(`${environment.urlBackend}reunions/getone/${id}`);\r\n }\r\n\r\n createReunion(reunion: CreateReunionRequest): Observable<Reunion> {\r\n   return this.http.post<Reunion>(`${environment.urlBackend}reunions/add`,reunion,{headers: this.getUserHeaders()});\r\n }\r\n\r\n updateReunion(id: string, reunion: Reunion): Observable<Reunion> {\r\n   return this.http.put<Reunion>(`${environment.urlBackend}reunions/update/${id}`,reunion,{headers: this.getUserHeaders()});\r\n }\r\n deleteReunion(id: string): Observable<void> {\r\n   return this.http.delete<void>(`${environment.urlBackend}reunions/delete/${id}`,{headers: this.getUserHeaders()});\r\n }\r\n\r\n /**\r\n  * Vérifie l'unicité d'un lien de visioconférence\r\n  * @param lienVisio Le lien à vérifier\r\n  * @param excludeReunionId ID de la réunion à exclure (pour la modification)\r\n  */\r\n checkLienVisioUniqueness(lienVisio: string, excludeReunionId?: string): Observable<any> {\r\n   const body = { lienVisio, excludeReunionId };\r\n   return this.http.post(`${environment.urlBackend}reunions/check-lien-visio`, body, {headers: this.getUserHeaders()});\r\n }\r\n\r\n getReunionsByPlanning(planningId: string): Observable<Reunion[]> {\r\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/planning/${planningId}`);\r\n }\r\n\r\n getProchainesReunions(userId: string): Observable<Reunion[]> {\r\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/user/${userId}`);\r\n }\r\n\r\n // Méthode pour les admins - récupère toutes les réunions\r\n getAllReunionsAdmin(): Observable<any> {\r\n   return this.http.get<any>(`${environment.urlBackend}reunions/admin/all`, {headers: this.getUserHeaders()});\r\n }\r\n\r\n // Méthode pour les admins - suppression forcée\r\n forceDeleteReunion(id: string): Observable<any> {\r\n   return this.http.delete<any>(`${environment.urlBackend}reunions/admin/force-delete/${id}`, {headers: this.getUserHeaders()});\r\n }\r\n\r\n}"], "mappings": "AACA,SAAQA,WAAW,QAAmB,sBAAsB;AAE5D,SAASC,WAAW,QAAQ,8BAA8B;;;;AAO1D,OAAM,MAAOC,cAAc;EACzBC,YAAoBC,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EACtD;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIX,WAAW,CAAC;MACrBY,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEFM,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAAY,GAAGb,WAAW,CAACc,UAAU,iBAAiB,CAAC;EAC7E;EACAC,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACb,IAAI,CAACU,GAAG,CAAU,GAAGb,WAAW,CAACc,UAAU,mBAAmBE,EAAE,EAAE,CAAC;EACjF;EAEAC,aAAaA,CAACC,OAA6B;IACzC,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAU,GAAGnB,WAAW,CAACc,UAAU,cAAc,EAACI,OAAO,EAAC;MAACE,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAClH;EAEAgB,aAAaA,CAACL,EAAU,EAAEE,OAAgB;IACxC,OAAO,IAAI,CAACf,IAAI,CAACmB,GAAG,CAAU,GAAGtB,WAAW,CAACc,UAAU,mBAAmBE,EAAE,EAAE,EAACE,OAAO,EAAC;MAACE,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAC1H;EACAkB,aAAaA,CAACP,EAAU;IACtB,OAAO,IAAI,CAACb,IAAI,CAACqB,MAAM,CAAO,GAAGxB,WAAW,CAACc,UAAU,mBAAmBE,EAAE,EAAE,EAAC;MAACI,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAClH;EAEA;;;;;EAKAoB,wBAAwBA,CAACC,SAAiB,EAAEC,gBAAyB;IACnE,MAAMC,IAAI,GAAG;MAAEF,SAAS;MAAEC;IAAgB,CAAE;IAC5C,OAAO,IAAI,CAACxB,IAAI,CAACgB,IAAI,CAAC,GAAGnB,WAAW,CAACc,UAAU,2BAA2B,EAAEc,IAAI,EAAE;MAACR,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EACrH;EAEAwB,qBAAqBA,CAACC,UAAkB;IACtC,OAAO,IAAI,CAAC3B,IAAI,CAACU,GAAG,CAAY,GAAGb,WAAW,CAACc,UAAU,qBAAqBgB,UAAU,EAAE,CAAC;EAC7F;EAEAC,qBAAqBA,CAACC,MAAc;IAClC,OAAO,IAAI,CAAC7B,IAAI,CAACU,GAAG,CAAY,GAAGb,WAAW,CAACc,UAAU,iBAAiBkB,MAAM,EAAE,CAAC;EACrF;EAEA;EACAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC9B,IAAI,CAACU,GAAG,CAAM,GAAGb,WAAW,CAACc,UAAU,oBAAoB,EAAE;MAACM,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAC5G;EAEA;EACA6B,kBAAkBA,CAAClB,EAAU;IAC3B,OAAO,IAAI,CAACb,IAAI,CAACqB,MAAM,CAAM,GAAGxB,WAAW,CAACc,UAAU,+BAA+BE,EAAE,EAAE,EAAE;MAACI,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAC9H;;;uBA1DYJ,cAAc,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAdvC,cAAc;MAAAwC,OAAA,EAAdxC,cAAc,CAAAyC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}