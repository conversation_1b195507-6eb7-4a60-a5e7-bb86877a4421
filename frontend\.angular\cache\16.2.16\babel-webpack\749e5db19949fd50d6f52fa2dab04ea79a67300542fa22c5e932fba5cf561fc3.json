{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42);\n    i0.ɵɵelement(3, \"i\", 43)(4, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"p\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction LoginComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 41)(2, \"div\", 48);\n    i0.ɵɵelement(3, \"i\", 49)(4, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"p\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\nfunction LoginComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \" Sign In \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 55);\n    i0.ɵɵelement(2, \"circle\", 56)(3, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"shake-animation\": a0\n  };\n};\nexport class LoginComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.message = '';\n    this.error = '';\n    this.isLoading = false;\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', Validators.required]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.invalid) return;\n    // Reset previous error messages\n    this.error = '';\n    this.message = '';\n    this.isLoading = true;\n    this.authService.login(this.loginForm.value).subscribe({\n      next: res => {\n        this.isLoading = false;\n        localStorage.setItem('token', res.token);\n        localStorage.setItem('user', JSON.stringify(res.user));\n        this.router.navigate(['/']);\n      },\n      error: err => {\n        this.isLoading = false;\n        // Handle authentication errors\n        if (err.status === 401) {\n          this.error = 'Email ou mot de passe incorrect. Veuillez réessayer.';\n        } else if (err.status === 403) {\n          this.error = \"Votre compte n'est pas vérifié. Veuillez vérifier votre email.\";\n        } else {\n          this.error = err.error?.message || 'Une erreur est survenue lors de la connexion. Veuillez réessayer.';\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 63,\n      vars: 11,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngClass\", \"ngSubmit\"], [1, \"group\"], [\"for\", \"email\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"required\", \"\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [\"id\", \"password\", \"type\", \"password\", \"formControlName\", \"password\", \"required\", \"\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/forgot-password\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [\"routerLink\", \"/signup\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1.5\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-2\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵtext(23, \" Welcome Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 13);\n          i0.ɵɵtext(25, \" Sign in to access your workspace \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵtext(31, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"div\", 21);\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, LoginComponent_div_36_Template, 3, 0, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 16)(38, \"label\", 24);\n          i0.ɵɵelement(39, \"i\", 25);\n          i0.ɵɵtext(40, \" Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 19);\n          i0.ɵɵelement(42, \"input\", 26);\n          i0.ɵɵelementStart(43, \"div\", 21);\n          i0.ɵɵelement(44, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, LoginComponent_div_45_Template, 3, 0, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, LoginComponent_div_46_Template, 8, 1, \"div\", 27);\n          i0.ɵɵtemplate(47, LoginComponent_div_47_Template, 8, 1, \"div\", 28);\n          i0.ɵɵelementStart(48, \"button\", 29);\n          i0.ɵɵelement(49, \"div\", 30)(50, \"div\", 31);\n          i0.ɵɵelementStart(51, \"span\", 32);\n          i0.ɵɵtemplate(52, LoginComponent_span_52_Template, 3, 0, \"span\", 33);\n          i0.ɵɵtemplate(53, LoginComponent_span_53_Template, 5, 0, \"span\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 35)(55, \"div\");\n          i0.ɵɵtext(56, \" Forgot your password? \");\n          i0.ɵɵelementStart(57, \"a\", 36);\n          i0.ɵɵtext(58, \" Reset it \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\");\n          i0.ɵɵtext(60, \" Don't have an account? \");\n          i0.ɵɵelementStart(61, \"a\", 37);\n          i0.ɵɵtext(62, \" Sign up \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm)(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.error));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"@keyframes _ngcontent-%COMP%_shake {\\n        0%,\\n        100% {\\n          transform: translateX(0);\\n        }\\n        10%,\\n        30%,\\n        50%,\\n        70%,\\n        90% {\\n          transform: translateX(-5px);\\n        }\\n        20%,\\n        40%,\\n        60%,\\n        80% {\\n          transform: translateX(5px);\\n        }\\n      }\\n      .shake-animation[_ngcontent-%COMP%] {\\n        animation: _ngcontent-%COMP%_shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;\\n      }\\n    \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxvZ2luLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO01BQ007UUFDRTs7VUFFRSx3QkFBd0I7UUFDMUI7UUFDQTs7Ozs7VUFLRSwyQkFBMkI7UUFDN0I7UUFDQTs7OztVQUlFLDBCQUEwQjtRQUM1QjtNQUNGO01BQ0E7UUFDRSwrREFBK0Q7TUFDakUiLCJmaWxlIjoibG9naW4uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgICBAa2V5ZnJhbWVzIHNoYWtlIHtcbiAgICAgICAgMCUsXG4gICAgICAgIDEwMCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcbiAgICAgICAgfVxuICAgICAgICAxMCUsXG4gICAgICAgIDMwJSxcbiAgICAgICAgNTAlLFxuICAgICAgICA3MCUsXG4gICAgICAgIDkwJSB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01cHgpO1xuICAgICAgICB9XG4gICAgICAgIDIwJSxcbiAgICAgICAgNDAlLFxuICAgICAgICA2MCUsXG4gICAgICAgIDgwJSB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDVweCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIC5zaGFrZS1hbmltYXRpb24ge1xuICAgICAgICBhbmltYXRpb246IHNoYWtlIDAuNnMgY3ViaWMtYmV6aWVyKDAuMzYsIDAuMDcsIDAuMTksIDAuOTcpIGJvdGg7XG4gICAgICB9XG4gICAgIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbG9naW4vbG9naW4uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7TUFDTTtRQUNFOztVQUVFLHdCQUF3QjtRQUMxQjtRQUNBOzs7OztVQUtFLDJCQUEyQjtRQUM3QjtRQUNBOzs7O1VBSUUsMEJBQTBCO1FBQzVCO01BQ0Y7TUFDQTtRQUNFLCtEQUErRDtNQUNqRTs7QUFFTix3OEJBQXc4QiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgICAgQGtleWZyYW1lcyBzaGFrZSB7XG4gICAgICAgIDAlLFxuICAgICAgICAxMDAlIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XG4gICAgICAgIH1cbiAgICAgICAgMTAlLFxuICAgICAgICAzMCUsXG4gICAgICAgIDUwJSxcbiAgICAgICAgNzAlLFxuICAgICAgICA5MCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNXB4KTtcbiAgICAgICAgfVxuICAgICAgICAyMCUsXG4gICAgICAgIDQwJSxcbiAgICAgICAgNjAlLFxuICAgICAgICA4MCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg1cHgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAuc2hha2UtYW5pbWF0aW9uIHtcbiAgICAgICAgYW5pbWF0aW9uOiBzaGFrZSAwLjZzIGN1YmljLWJlemllcigwLjM2LCAwLjA3LCAwLjE5LCAwLjk3KSBib3RoO1xuICAgICAgfVxuICAgICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "error", "ctx_r3", "message", "ɵɵnamespaceSVG", "LoginComponent", "constructor", "fb", "authService", "router", "isLoading", "loginForm", "group", "email", "required", "password", "onSubmit", "invalid", "login", "value", "subscribe", "next", "res", "localStorage", "setItem", "token", "JSON", "stringify", "user", "navigate", "err", "status", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "LoginComponent_div_36_Template", "LoginComponent_div_45_Template", "LoginComponent_div_46_Template", "LoginComponent_div_47_Template", "LoginComponent_span_52_Template", "LoginComponent_span_53_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "tmp_2_0", "get", "touched", "tmp_3_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styles: [\r\n    `\r\n      @keyframes shake {\r\n        0%,\r\n        100% {\r\n          transform: translateX(0);\r\n        }\r\n        10%,\r\n        30%,\r\n        50%,\r\n        70%,\r\n        90% {\r\n          transform: translateX(-5px);\r\n        }\r\n        20%,\r\n        40%,\r\n        60%,\r\n        80% {\r\n          transform: translateX(5px);\r\n        }\r\n      }\r\n      .shake-animation {\r\n        animation: shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;\r\n      }\r\n    `,\r\n  ],\r\n})\r\nexport class LoginComponent {\r\n  loginForm: FormGroup;\r\n  message = '';\r\n  error = '';\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.loginForm.invalid) return;\r\n\r\n    // Reset previous error messages\r\n    this.error = '';\r\n    this.message = '';\r\n    this.isLoading = true;\r\n\r\n    this.authService.login(this.loginForm.value).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        localStorage.setItem('token', res.token);\r\n        localStorage.setItem('user', JSON.stringify(res.user));\r\n        this.router.navigate(['/']);\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        // Handle authentication errors\r\n        if (err.status === 401) {\r\n          this.error = 'Email ou mot de passe incorrect. Veuillez réessayer.';\r\n        } else if (err.status === 403) {\r\n          this.error =\r\n            \"Votre compte n'est pas vérifié. Veuillez vérifier votre email.\";\r\n        } else {\r\n          this.error =\r\n            err.error?.message ||\r\n            'Une erreur est survenue lors de la connexion. Veuillez réessayer.';\r\n        }\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div\r\n  class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] flex items-center justify-center p-4 relative futuristic-layout\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          Welcome Back\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          Sign in to access your workspace\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form -->\r\n      <div class=\"p-6\">\r\n        <form\r\n          [formGroup]=\"loginForm\"\r\n          (ngSubmit)=\"onSubmit()\"\r\n          class=\"space-y-5\"\r\n          [ngClass]=\"{ 'shake-animation': error }\"\r\n        >\r\n          <!-- Email -->\r\n          <div class=\"group\">\r\n            <label\r\n              for=\"email\"\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\r\n              Email\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                id=\"email\"\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                required\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"<EMAIL>\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"\r\n                loginForm.get('email')?.invalid &&\r\n                loginForm.get('email')?.touched\r\n              \"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              Email is required\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Password -->\r\n          <div class=\"group\">\r\n            <label\r\n              for=\"password\"\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-lock mr-1.5 text-xs\"></i>\r\n              Password\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                id=\"password\"\r\n                type=\"password\"\r\n                formControlName=\"password\"\r\n                required\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"••••••••\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"\r\n                loginForm.get('password')?.invalid &&\r\n                loginForm.get('password')?.touched\r\n              \"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              Password is required\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n            [disabled]=\"loginForm.invalid || isLoading\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <span *ngIf=\"!isLoading\" class=\"flex items-center\">\r\n                <i class=\"fas fa-sign-in-alt mr-2\"></i>\r\n                Sign In\r\n              </span>\r\n              <span *ngIf=\"isLoading\" class=\"flex items-center justify-center\">\r\n                <svg\r\n                  class=\"animate-spin mr-2 h-4 w-4 text-white\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <circle\r\n                    class=\"opacity-25\"\r\n                    cx=\"12\"\r\n                    cy=\"12\"\r\n                    r=\"10\"\r\n                    stroke=\"currentColor\"\r\n                    stroke-width=\"4\"\r\n                  ></circle>\r\n                  <path\r\n                    class=\"opacity-75\"\r\n                    fill=\"currentColor\"\r\n                    d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                  ></path>\r\n                </svg>\r\n                Signing in...\r\n              </span>\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Footer Links -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div>\r\n              Forgot your password?\r\n              <a\r\n                routerLink=\"/forgot-password\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium\"\r\n              >\r\n                Reset it\r\n              </a>\r\n            </div>\r\n            <div>\r\n              Don't have an account?\r\n              <a\r\n                routerLink=\"/signup\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium\"\r\n              >\r\n                Sign up\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICuFvDC,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IA6BNJ,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAIRJ,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;;;IAoBFV,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAW,cAAA,EAKC;IALDX,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,iBAOU;IAMZF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;;;AD9LrB,OAAM,MAAOQ,cAAc;EAMzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAN,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IACV,KAAAS,SAAS,GAAG,KAAK;IAOf,IAAI,CAACC,SAAS,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACqB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACsB,QAAQ;KACnC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,SAAS,CAACM,OAAO,EAAE;IAE5B;IACA,IAAI,CAAChB,KAAK,GAAG,EAAE;IACf,IAAI,CAACE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACO,SAAS,GAAG,IAAI;IAErB,IAAI,CAACF,WAAW,CAACU,KAAK,CAAC,IAAI,CAACP,SAAS,CAACQ,KAAK,CAAC,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtBa,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,GAAG,CAACG,KAAK,CAAC;QACxCF,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,IAAI,CAAC,CAAC;QACtD,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7B,CAAC;MACD5B,KAAK,EAAG6B,GAAG,IAAI;QACb,IAAI,CAACpB,SAAS,GAAG,KAAK;QACtB;QACA,IAAIoB,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAAC9B,KAAK,GAAG,sDAAsD;SACpE,MAAM,IAAI6B,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAAC9B,KAAK,GACR,gEAAgE;SACnE,MAAM;UACL,IAAI,CAACA,KAAK,GACR6B,GAAG,CAAC7B,KAAK,EAAEE,OAAO,IAClB,mEAAmE;;MAEzE;KACD,CAAC;EACJ;;;uBA/CWE,cAAc,EAAAZ,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3C,EAAA,CAAAuC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdjC,cAAc;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnC3BpD,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAI,YAAA,EAAM;UAIVJ,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAG,MAAA,0CACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAINJ,EAAA,CAAAC,cAAA,eAAiB;UAGbD,EAAA,CAAAsD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAKvBvB,EAAA,CAAAC,cAAA,eAAmB;UAKfD,EAAA,CAAAE,SAAA,aAA8C;UAC9CF,EAAA,CAAAG,MAAA,eACF;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAOE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAwD,UAAA,KAAAC,8BAAA,kBASM;UACRzD,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAmB;UAKfD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAG,MAAA,kBACF;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAOE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAwD,UAAA,KAAAE,8BAAA,kBASM;UACR1D,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAwD,UAAA,KAAAG,8BAAA,kBAoBM;UAGN3D,EAAA,CAAAwD,UAAA,KAAAI,8BAAA,kBAoBM;UAGN5D,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAwD,UAAA,KAAAK,+BAAA,mBAGO;UACP7D,EAAA,CAAAwD,UAAA,KAAAM,+BAAA,mBAsBO;UACT9D,EAAA,CAAAI,YAAA,EAAO;UAITJ,EAAA,CAAAC,cAAA,eAEC;UAEGD,EAAA,CAAAG,MAAA,+BACA;UAAAH,EAAA,CAAAC,cAAA,aAGC;UACCD,EAAA,CAAAG,MAAA,kBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,WAAK;UACHD,EAAA,CAAAG,MAAA,gCACA;UAAAH,EAAA,CAAAC,cAAA,aAGC;UACCD,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;UAhMRJ,EAAA,CAAAK,SAAA,IAAuB;UAAvBL,EAAA,CAAA+D,UAAA,cAAAV,GAAA,CAAAnC,SAAA,CAAuB,YAAAlB,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAZ,GAAA,CAAA7C,KAAA;UAgClBR,EAAA,CAAAK,SAAA,GAGD;UAHCL,EAAA,CAAA+D,UAAA,WAAAG,OAAA,GAAAb,GAAA,CAAAnC,SAAA,CAAAiD,GAAA,4BAAAD,OAAA,CAAA1C,OAAA,OAAA0C,OAAA,GAAAb,GAAA,CAAAnC,SAAA,CAAAiD,GAAA,4BAAAD,OAAA,CAAAE,OAAA,EAGD;UAmCCpE,EAAA,CAAAK,SAAA,GAGD;UAHCL,EAAA,CAAA+D,UAAA,WAAAM,OAAA,GAAAhB,GAAA,CAAAnC,SAAA,CAAAiD,GAAA,+BAAAE,OAAA,CAAA7C,OAAA,OAAA6C,OAAA,GAAAhB,GAAA,CAAAnC,SAAA,CAAAiD,GAAA,+BAAAE,OAAA,CAAAD,OAAA,EAGD;UAUDpE,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA+D,UAAA,SAAAV,GAAA,CAAA7C,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA+D,UAAA,SAAAV,GAAA,CAAA3C,OAAA,CAAa;UAyBdV,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAA+D,UAAA,aAAAV,GAAA,CAAAnC,SAAA,CAAAM,OAAA,IAAA6B,GAAA,CAAApC,SAAA,CAA2C;UAWlCjB,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAA+D,UAAA,UAAAV,GAAA,CAAApC,SAAA,CAAgB;UAIhBjB,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA+D,UAAA,SAAAV,GAAA,CAAApC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}