{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil, take } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authuser.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/user-status.service\";\nimport * as i4 from \"src/app/services/message.service\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"src/app/services/data.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nfunction FrontLayoutComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 64);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 67)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Accueil\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FrontLayoutComponent_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 70)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Projects\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 71);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 72)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Plannings\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 73);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 74)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"R\\u00E9unions\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 75);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 76)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Messages\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 77);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 78)(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Equipes\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 79);\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 81)(5, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 104)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"i\", 105);\n    i0.ɵɵelementStart(3, \"div\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.unreadNotificationsCount > 0 ? ctx_r15.unreadNotificationsCount > 99 ? \"99+\" : ctx_r15.unreadNotificationsCount : \"0\", \" \");\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_i_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 107);\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_i_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 108);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\nfunction FrontLayoutComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 83);\n    i0.ɵɵtemplate(2, FrontLayoutComponent_ng_container_47_a_2_Template, 5, 1, \"a\", 84);\n    i0.ɵɵelementStart(3, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_47_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.toggleDarkMode());\n    });\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵelement(5, \"div\", 87)(6, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 89);\n    i0.ɵɵelementStart(8, \"div\", 90);\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵtemplate(10, FrontLayoutComponent_ng_container_47_i_10_Template, 1, 0, \"i\", 91);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, FrontLayoutComponent_ng_container_47_i_12_Template, 1, 0, \"i\", 92);\n    i0.ɵɵpipe(13, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_47_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.logout());\n    });\n    i0.ɵɵelementStart(15, \"div\", 94);\n    i0.ɵɵelement(16, \"div\", 95)(17, \"div\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"div\", 97)(19, \"i\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 99)(21, \"span\", 100);\n    i0.ɵɵtext(22, \"Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 101);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 102);\n    i0.ɵɵelement(26, \"div\", 89)(27, \"img\", 103);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.authService.userLoggedIn());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, i0.ɵɵpipeBind1(9, 6, ctx_r7.isDarkMode$)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(11, 8, ctx_r7.isDarkMode$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 10, ctx_r7.isDarkMode$));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r7.imageProfile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FrontLayoutComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"a\", 110);\n    i0.ɵɵelement(2, \"div\", 111)(3, \"div\", 112);\n    i0.ɵɵelementStart(4, \"span\", 113);\n    i0.ɵɵelement(5, \"i\", 114);\n    i0.ɵɵtext(6, \" Connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 115);\n    i0.ɵɵelement(8, \"div\", 116)(9, \"div\", 117);\n    i0.ɵɵelementStart(10, \"span\", 113);\n    i0.ɵɵelement(11, \"i\", 118);\n    i0.ɵɵtext(12, \" Inscription \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_69_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r21.badge > 99 ? \"99+\" : item_r21.badge, \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nfunction FrontLayoutComponent_a_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 119);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_69_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵelementStart(2, \"div\", 120)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\")(5, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FrontLayoutComponent_a_69_div_8_Template, 2, 1, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"routerLink\", item_r21.route);\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(7, _c2, item_r21.route === \"/\" ? true : false));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"\", item_r21.icon, \" h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r21.text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r21.badge && item_r21.badge > 0);\n  }\n}\nfunction FrontLayoutComponent_a_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 123);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_70_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵelementStart(2, \"div\", 120)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 124)(5, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 125);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 65);\n    i0.ɵɵelementStart(3, \"div\", 66)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 126)(6, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 127);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 80);\n    i0.ɵɵelementStart(11, \"div\", 66)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 128)(14, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Inscription\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_ng_container_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 129);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_73_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 65);\n    i0.ɵɵelementStart(3, \"div\", 66)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 130)(6, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Mon Profil\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 131);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_73_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      ctx_r33.logout();\n      return i0.ɵɵresetView(ctx_r33.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 132);\n    i0.ɵɵelementStart(11, \"div\", 66)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 133)(14, \"div\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 136)(2, \"div\", 30)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 137)(5, \"div\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 139);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.messageFromRedirect, \" \");\n  }\n}\nconst _c3 = function () {\n  return {\n    route: \"/\",\n    icon: \"fas fa-home\",\n    text: \"Accueil\"\n  };\n};\nconst _c4 = function () {\n  return {\n    route: \"/projects\",\n    icon: \"fas fa-rocket\",\n    text: \"Projects\"\n  };\n};\nconst _c5 = function () {\n  return {\n    route: \"/plannings\",\n    icon: \"far fa-calendar-check\",\n    text: \"Plannings\"\n  };\n};\nconst _c6 = function () {\n  return {\n    route: \"/reunions\",\n    icon: \"fas fa-users-cog\",\n    text: \"R\\u00E9unions\"\n  };\n};\nconst _c7 = function () {\n  return {\n    route: \"/messages\",\n    icon: \"far fa-comment-dots\",\n    text: \"Messages\"\n  };\n};\nconst _c8 = function () {\n  return {\n    route: \"/equipes\",\n    icon: \"fas fa-users\",\n    text: \"Equipes\"\n  };\n};\nconst _c9 = function (a3) {\n  return {\n    route: \"/notifications\",\n    icon: \"far fa-bell\",\n    text: \"Notifications\",\n    badge: a3\n  };\n};\nconst _c10 = function (a0, a1, a2, a3, a4, a5, a6) {\n  return [a0, a1, a2, a3, a4, a5, a6];\n};\nexport class FrontLayoutComponent {\n  constructor(authService, route, router, statusService, MessageService, themeService, dataService, cdr) {\n    this.authService = authService;\n    this.route = route;\n    this.router = router;\n    this.statusService = statusService;\n    this.MessageService = MessageService;\n    this.themeService = themeService;\n    this.dataService = dataService;\n    this.cdr = cdr;\n    this.sidebarOpen = false;\n    this.profileMenuOpen = false;\n    this.currentUser = null;\n    this.messageFromRedirect = '';\n    this.unreadNotificationsCount = 0;\n    this.isMobileView = false;\n    this.username = '';\n    this.imageProfile = '';\n    this.destroy$ = new Subject();\n    this.MOBILE_BREAKPOINT = 768;\n    this.subscriptions = [];\n    this.checkViewport();\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  loadUserProfile() {\n    // Try to get user from both services for maximum reliability\n    const authUser = this.authService.getCurrentUser();\n    const dataUser = this.dataService.currentUserValue;\n    console.log('Auth User:', authUser);\n    console.log('Data User:', dataUser);\n    // Prefer dataUser if available, otherwise use authUser\n    const user = dataUser || authUser;\n    if (user) {\n      this.updateProfileDisplay(user);\n      // Forcer une synchronisation complète des données utilisateur\n      if (this.authService.userLoggedIn()) {\n        this.dataService.syncCurrentUser().subscribe({\n          next: updatedUser => {\n            console.log('User profile synced:', updatedUser);\n            this.updateProfileDisplay(updatedUser);\n          },\n          error: err => {\n            console.error('Failed to sync user profile:', err);\n          }\n        });\n      }\n    } else {\n      // Default values if no user is found\n      this.username = '';\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n    console.log('Front layout - Image profile loaded:', this.imageProfile);\n    // Sync user data between services if needed\n    if (authUser && !dataUser) {\n      this.dataService.updateCurrentUser(authUser);\n    } else if (!authUser && dataUser) {\n      this.authService.setCurrentUser(dataUser);\n    }\n  }\n  ngOnInit() {\n    this.subscribeToQueryParams();\n    this.subscribeToCurrentUser();\n    this.subscribeToRouterEvents();\n    this.setupNotificationSystem();\n  }\n  checkViewport() {\n    this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;\n    if (!this.isMobileView) {\n      this.sidebarOpen = false;\n    }\n  }\n  setupNotificationSystem() {\n    console.log('🔔 LAYOUT: Setting up notification system...');\n    // Approche 1: Subscription normale\n    const countSubscription = this.MessageService.notificationCount$.pipe(takeUntil(this.destroy$)).subscribe(count => {\n      console.log('🔔 LAYOUT: Notification count updated via subscription:', count);\n      this.unreadNotificationsCount = count;\n      this.cdr.detectChanges();\n    });\n    this.subscriptions.push(countSubscription);\n    // Approche 2: Événement personnalisé (solution de secours)\n    const handleNotificationCountChange = event => {\n      const count = event.detail.count;\n      console.log('🔔 LAYOUT: Notification count updated via custom event:', count);\n      this.unreadNotificationsCount = count;\n      this.cdr.detectChanges();\n    };\n    window.addEventListener('notificationCountChanged', handleNotificationCountChange);\n    // Nettoyer l'événement à la destruction\n    this.subscriptions.push({\n      unsubscribe: () => window.removeEventListener('notificationCountChanged', handleNotificationCountChange)\n    });\n    // Forcer une première lecture du compteur\n    setTimeout(() => {\n      this.MessageService.notificationCount$.pipe(take(1)).subscribe(currentCount => {\n        console.log('🔔 LAYOUT: Initial count read:', currentCount);\n        this.unreadNotificationsCount = currentCount;\n        this.cdr.detectChanges();\n      });\n    }, 100);\n    // Charger les notifications initiales si l'utilisateur est connecté\n    if (this.authService.userLoggedIn()) {\n      this.MessageService.getNotifications(true).subscribe();\n    }\n  }\n  subscribeToCurrentUser() {\n    // S'abonner aux changements d'image de profil via AuthUserService\n    const authProfileSub = this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n      this.updateProfileDisplay(user);\n    });\n    // S'abonner aux changements d'image de profil via DataService\n    const dataProfileSub = this.dataService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      if (user) {\n        this.currentUser = user;\n        this.updateProfileDisplay(user);\n      }\n    });\n    this.subscriptions.push(authProfileSub, dataProfileSub);\n  }\n  updateProfileDisplay(user) {\n    if (user) {\n      this.username = user.fullName || user.username || '';\n      // Vérification plus robuste pour l'image de profil\n      let imageFound = false;\n      // Vérifier profileImage en premier\n      if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '' && user.profileImage !== 'undefined') {\n        this.imageProfile = user.profileImage;\n        imageFound = true;\n        console.log('Using profileImage:', this.imageProfile);\n      }\n      // Ensuite vérifier image si profileImage n'est pas valide\n      if (!imageFound && user.image && user.image !== 'null' && user.image.trim() !== '' && user.image !== 'undefined') {\n        this.imageProfile = user.image;\n        imageFound = true;\n        console.log('Using image:', this.imageProfile);\n      }\n      // Vérifier si l'image est une URL relative au backend\n      if (imageFound && !this.imageProfile.startsWith('http') && !this.imageProfile.startsWith('assets/')) {\n        // Si c'est une URL relative au backend, ajouter le préfixe du backend\n        if (this.imageProfile.startsWith('/')) {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}${this.imageProfile}`;\n        } else {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}/${this.imageProfile}`;\n        }\n        console.log('Converted to absolute URL:', this.imageProfile);\n      }\n      // Si aucune image valide n'est trouvée, utiliser l'image par défaut\n      if (!imageFound) {\n        this.imageProfile = 'assets/images/default-profile.png';\n        console.log('Using default image');\n      }\n      console.log('Front layout - Image profile updated:', this.imageProfile);\n    }\n  }\n  subscribeToQueryParams() {\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.messageFromRedirect = params['message'] || '';\n    });\n  }\n  subscribeToRouterEvents() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(() => {\n      this.sidebarOpen = false;\n      this.profileMenuOpen = false;\n    });\n  }\n  toggleSidebar() {\n    this.sidebarOpen = !this.sidebarOpen;\n  }\n  toggleProfileMenu() {\n    this.profileMenuOpen = !this.profileMenuOpen;\n  }\n  toggleDarkMode() {\n    this.themeService.toggleDarkMode();\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.profileMenuOpen = false;\n        this.sidebarOpen = false;\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function FrontLayoutComponent_Factory(t) {\n      return new (t || FrontLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthuserService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UserStatusService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ThemeService), i0.ɵɵdirectiveInject(i6.DataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FrontLayoutComponent,\n      selectors: [[\"app-front-layout\"]],\n      hostBindings: function FrontLayoutComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function FrontLayoutComponent_resize_HostBindingHandler() {\n            return ctx.checkViewport();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 82,\n      vars: 40,\n      consts: [[1, \"flex\", \"h-screen\", \"main-grid-container\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"futuristic-layout\"], [1, \"background-grid\"], [1, \"hidden\", \"md:flex\", \"md:flex-shrink-0\"], [1, \"flex\", \"flex-col\", \"w-64\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-r\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-16\", \"px-4\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"-top-6\", \"-left-6\", \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"absolute\", \"-bottom-6\", \"-right-6\", \"w-12\", \"h-12\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"relative\", \"z-10\"], [1, \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"transform\", \"rotate-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"flex-col\", \"flex-grow\", \"px-4\", \"py-4\"], [1, \"flex-1\", \"space-y-2\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 3, \"routerLinkActiveOptions\", 4, \"ngIf\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/equipes\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [1, \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"my-2\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", \"class\", \"sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all\", 4, \"ngIf\"], [1, \"fixed\", \"top-0\", \"left-0\", \"right-0\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"z-50\", \"backdrop-blur-sm\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/10\", \"dark:via-[#6d78c9]/5\", \"to-transparent\"], [1, \"absolute\", \"top-0\", \"right-1/3\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/5\", \"dark:via-[#6d78c9]/3\", \"to-transparent\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\"], [\"aria-label\", \"Toggle menu\", 1, \"md:hidden\", \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"px-3\", \"rounded-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"transition-colors\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-md\", \"blur-md\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h16\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-2\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", 1, \"flex-shrink-0\", \"flex\", \"items-center\", \"group\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"ml-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hidden\", \"md:block\"], [4, \"ngIf\", \"ngIfElse\"], [\"authButtons\", \"\"], [1, \"fixed\", \"inset-0\", \"z-40\", \"md:hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-black/30\", \"dark:bg-black/50\", \"backdrop-blur-sm\"], [1, \"fixed\", \"inset-y-0\", \"left-0\", \"max-w-xs\", \"w-full\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-lg\", \"dark:shadow-[0_0_30px_rgba(0,0,0,0.3)]\", \"transform\", \"transition-transform\", \"duration-300\", \"ease-in-out\", \"border-r\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", 3, \"click\"], [1, \"flex\", \"flex-col\", \"h-full\", \"relative\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"flex\", \"items-center\", \"justify-between\", \"px-4\", \"py-3\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"aria-label\", \"Close menu\", 1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"p-2\", \"rounded-full\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-full\", \"blur-md\"], [1, \"fas\", \"fa-times\", \"relative\", \"z-10\"], [1, \"flex-1\", \"px-2\", \"py-4\", \"space-y-1\", \"overflow-y-auto\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\", \"z-10\"], [\"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\", \"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/admin/dashboard\", \"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"flex-1\", \"overflow-y-auto\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"pt-16\", \"pb-6\", \"relative\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [\"class\", \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4\", 4, \"ngIf\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"routerLinkActiveOptions\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-rocket\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-calendar-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users-cog\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-comment-dots\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/equipes\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", 1, \"sidebar-nav-link\", \"dashboard-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"transition-all\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#7826b5]/20\", \"dark:bg-[#9d4edd]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"ml-4\", \"flex\", \"items-center\", \"md:ml-6\"], [\"routerLink\", \"/notifications\", \"class\", \"flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]\", \"aria-label\", \"Notifications\", 4, \"ngIf\"], [\"aria-label\", \"Toggle dark mode\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-9\", \"w-9\", \"rounded-xl\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"border-2\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"opacity-100\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]/0\", \"via-[#4f5fad]/30\", \"to-[#4f5fad]/0\", \"dark:from-[#6d78c9]/0\", \"dark:via-[#6d78c9]/30\", \"dark:to-[#6d78c9]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"relative\", \"z-10\", \"transition-all\", \"duration-500\", \"ease-in-out\", 3, \"ngClass\"], [\"class\", \"far fa-moon text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"class\", \"far fa-sun text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"aria-label\", \"Logout\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border\", \"border-[#ff6b69]/20\", \"dark:border-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#ff6b69]/0\", \"via-[#ff6b69]/30\", \"to-[#ff6b69]/0\", \"dark:from-[#ff8785]/0\", \"dark:via-[#ff8785]/30\", \"dark:to-[#ff8785]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff8785]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"fas\", \"fa-sign-out-alt\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-3\", \"py-2\", \"rounded-lg\", \"transition-all\", \"group\"], [1, \"sr-only\"], [1, \"hidden\", \"md:inline-block\", \"mr-2\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"overflow-hidden\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group-hover:border-[#3d4a85]\", \"dark:group-hover:border-[#4f5fad]\", \"transition-colors\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"routerLink\", \"/notifications\", \"aria-label\", \"Notifications\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-10\", \"px-4\", \"rounded-xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"transition-all\", \"duration-300\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\"], [1, \"far\", \"fa-bell\", \"text-lg\", \"transition-transform\", \"mr-2\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-6\", \"min-w-6\", \"px-1.5\", \"rounded-md\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"font-bold\", \"text-xs\", \"shadow-md\", \"animate-pulse\"], [1, \"far\", \"fa-moon\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"far\", \"fa-sun\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"space-x-4\"], [\"routerLink\", \"/login\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"font-medium\", \"py-2\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-2\"], [\"routerLink\", \"/signup\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [\"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"w-full\"], [\"class\", \"ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse\", 4, \"ngIf\"], [1, \"ml-auto\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"text-xs\", \"rounded-full\", \"h-5\", \"min-w-5\", \"px-1\", \"flex\", \"items-center\", \"justify-center\", \"shadow-md\", \"animate-pulse\"], [\"routerLink\", \"/admin/dashboard\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"mt-2\", 3, \"click\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-colors\"], [\"routerLink\", \"/login\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/signup\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user-plus\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"cursor-pointer\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-3\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\", \"mt-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#ff8c00]\", \"dark:border-[#ff6b00]\", \"rounded-lg\", \"p-4\", \"mb-6\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"backdrop-blur-sm\"], [1, \"fas\", \"fa-info-circle\", \"text-[#ff8c00]\", \"dark:text-[#ff6b00]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff8c00]/30\", \"dark:bg-[#ff6b00]/30\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"animate-pulse\"], [1, \"ml-3\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"]],\n      template: function FrontLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 9);\n          i0.ɵɵelement(11, \"path\", 10)(12, \"path\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(13, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 13);\n          i0.ɵɵtext(15, \"DevBridge\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"nav\", 15);\n          i0.ɵɵtemplate(18, FrontLayoutComponent_a_18_Template, 8, 2, \"a\", 16);\n          i0.ɵɵtemplate(19, FrontLayoutComponent_a_19_Template, 8, 0, \"a\", 17);\n          i0.ɵɵtemplate(20, FrontLayoutComponent_a_20_Template, 8, 0, \"a\", 18);\n          i0.ɵɵtemplate(21, FrontLayoutComponent_a_21_Template, 8, 0, \"a\", 19);\n          i0.ɵɵtemplate(22, FrontLayoutComponent_a_22_Template, 8, 0, \"a\", 20);\n          i0.ɵɵtemplate(23, FrontLayoutComponent_a_23_Template, 8, 0, \"a\", 21);\n          i0.ɵɵelement(24, \"div\", 22);\n          i0.ɵɵtemplate(25, FrontLayoutComponent_a_25_Template, 8, 0, \"a\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"header\", 24)(27, \"div\", 25)(28, \"div\", 26);\n          i0.ɵɵelement(29, \"div\", 27)(30, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 29)(32, \"div\", 30)(33, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_33_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(34, \"div\", 32);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(35, \"svg\", 33);\n          i0.ɵɵelement(36, \"path\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(37, \"span\", 35);\n          i0.ɵɵtext(38, \"Menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"a\", 36)(40, \"div\", 8)(41, \"h1\", 37);\n          i0.ɵɵtext(42, \" DevBridge \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 39);\n          i0.ɵɵtext(45, \"Project Management Suite\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 30);\n          i0.ɵɵtemplate(47, FrontLayoutComponent_ng_container_47_Template, 28, 14, \"ng-container\", 40);\n          i0.ɵɵtemplate(48, FrontLayoutComponent_ng_template_48_Template, 13, 0, \"ng-template\", null, 41, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 42);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_50_listener() {\n            return ctx.sidebarOpen && ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(51, \"div\", 43);\n          i0.ɵɵelementStart(52, \"div\", 44);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_52_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(53, \"div\", 45)(54, \"div\", 26);\n          i0.ɵɵelement(55, \"div\", 27)(56, \"div\", 28)(57, \"div\", 46)(58, \"div\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 48)(60, \"div\")(61, \"h3\", 49);\n          i0.ɵɵtext(62, \" DevBridge \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\", 50);\n          i0.ɵɵtext(64, \" Project Management Suite \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_65_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(66, \"div\", 52)(67, \"i\", 53);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"nav\", 54);\n          i0.ɵɵtemplate(69, FrontLayoutComponent_a_69_Template, 9, 9, \"a\", 55);\n          i0.ɵɵtemplate(70, FrontLayoutComponent_a_70_Template, 8, 0, \"a\", 56);\n          i0.ɵɵelement(71, \"div\", 22);\n          i0.ɵɵtemplate(72, FrontLayoutComponent_ng_container_72_Template, 17, 0, \"ng-container\", 57);\n          i0.ɵɵtemplate(73, FrontLayoutComponent_ng_container_73_Template, 17, 0, \"ng-container\", 57);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 58)(75, \"main\", 59)(76, \"div\", 26);\n          i0.ɵɵelement(77, \"div\", 60)(78, \"div\", 61);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, FrontLayoutComponent_div_79_Template, 8, 1, \"div\", 62);\n          i0.ɵɵelementStart(80, \"div\", 63);\n          i0.ɵɵelement(81, \"router-outlet\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r8 = i0.ɵɵreference(49);\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 22, ctx.isDarkMode$));\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn())(\"ngIfElse\", _r8);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"hidden\", !ctx.sidebarOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"translate-x-0\", ctx.sidebarOpen)(\"-translate-x-full\", !ctx.sidebarOpen);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction7(32, _c10, i0.ɵɵpureFunction0(24, _c3), i0.ɵɵpureFunction0(25, _c4), i0.ɵɵpureFunction0(26, _c5), i0.ɵɵpureFunction0(27, _c6), i0.ɵɵpureFunction0(28, _c7), i0.ɵɵpureFunction0(29, _c8), i0.ɵɵpureFunction1(30, _c9, ctx.unreadNotificationsCount)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i7.AsyncPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n  min-height: 100vh;\\n  position: relative;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\\n  background-color: #f0f4f8;\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-layout[_ngcontent-%COMP%] {\\n  background-color: #121212;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\\n  background-color: #f0f4f8;\\n}\\n\\n\\n\\n.futuristic-header[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.futuristic-logo[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-subtitle[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n}\\n\\n\\n\\n.futuristic-nav-link[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  padding: 0.75rem 1rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 0;\\n  height: 2px;\\n  background: linear-gradient(\\n    90deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  transition: width var(--transition-fast);\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--text-light);\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]:hover::before {\\n  width: 80%;\\n}\\n\\n.futuristic-nav-link-active[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.futuristic-nav-link-active[_ngcontent-%COMP%]::before {\\n  width: 80%;\\n}\\n\\n\\n\\n.futuristic-profile-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: transparent;\\n  border: none;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-profile-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n}\\n\\n.futuristic-username[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n}\\n\\n\\n\\n.futuristic-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: 100%;\\n  margin-top: 0.5rem;\\n  width: 12rem;\\n  background-color: var(--medium-bg);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: var(--border-radius-md);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  z-index: 50;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-out;\\n}\\n\\n.futuristic-dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  transition: all var(--transition-fast);\\n  cursor: pointer;\\n}\\n\\n.futuristic-dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n}\\n\\n\\n\\n.futuristic-login-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  color: var(--accent-color);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.futuristic-login-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.futuristic-register-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  color: var(--text-light);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n}\\n\\n.futuristic-register-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-sidebar[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-right: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n.futuristic-sidebar-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n}\\n\\n.futuristic-close-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-close-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: rotate(90deg);\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--accent-color) transparent;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n}\\n\\n.futuristic-sidebar-link-active[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.15);\\n  color: var(--accent-color);\\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n.futuristic-sidebar-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: var(--text-dim);\\n  transition: color var(--transition-fast);\\n}\\n\\n.futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  width: 1.5rem;\\n  font-size: 1.25rem;\\n  color: var(--text-dim);\\n  transition: color var(--transition-fast);\\n  text-align: center;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.futuristic-separator[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: linear-gradient(\\n    to right,\\n    transparent,\\n    rgba(0, 247, 255, 0.2),\\n    transparent\\n  );\\n}\\n\\n\\n\\n.futuristic-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  color: var(--text-light);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: 9999px;\\n  padding: 2px 8px;\\n  min-width: 1.5rem;\\n  text-align: center;\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-main-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n.futuristic-status-message[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-left: 4px solid var(--accent-color);\\n  border-radius: var(--border-radius-md);\\n  padding: 1rem;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.futuristic-status-icon[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n  font-size: 1.25rem;\\n}\\n\\n.futuristic-status-text[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.main-content-grid[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.main-content-grid[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 0.375rem 0 0 0.375rem;\\n  border: 2px solid rgba(79, 95, 173, 0.1);\\n  pointer-events: none;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  border-color: rgba(109, 120, 201, 0.1);\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.7;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  filter: blur(8px);\\n  transform: scale(1.5);\\n  opacity: 0.5;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n}\\n\\n\\n\\n.dashboard-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n  box-shadow: 0 0 15px rgba(157, 78, 221, 0.7);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .dashboard-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n}\\n\\n.dashboard-link.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "Subject", "filter", "takeUntil", "take", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r15", "unreadNotificationsCount", "ɵɵelementContainerStart", "ɵɵtemplate", "FrontLayoutComponent_ng_container_47_a_2_Template", "ɵɵlistener", "FrontLayoutComponent_ng_container_47_Template_button_click_3_listener", "ɵɵrestoreView", "_r19", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "toggleDarkMode", "FrontLayoutComponent_ng_container_47_i_10_Template", "FrontLayoutComponent_ng_container_47_i_12_Template", "FrontLayoutComponent_ng_container_47_Template_button_click_14_listener", "ctx_r20", "logout", "ɵɵelementContainerEnd", "ctx_r7", "authService", "userLoggedIn", "ɵɵpureFunction1", "_c1", "ɵɵpipeBind1", "isDarkMode$", "username", "imageProfile", "ɵɵsanitizeUrl", "item_r21", "badge", "FrontLayoutComponent_a_69_Template_a_click_0_listener", "_r25", "ctx_r24", "toggleSidebar", "FrontLayoutComponent_a_69_div_8_Template", "ɵɵpropertyInterpolate", "route", "_c2", "ɵɵclassMapInterpolate1", "icon", "ɵɵtextInterpolate", "text", "FrontLayoutComponent_a_70_Template_a_click_0_listener", "_r27", "ctx_r26", "FrontLayoutComponent_ng_container_72_Template_a_click_1_listener", "_r29", "ctx_r28", "FrontLayoutComponent_ng_container_72_Template_a_click_9_listener", "ctx_r30", "FrontLayoutComponent_ng_container_73_Template_a_click_1_listener", "_r32", "ctx_r31", "FrontLayoutComponent_ng_container_73_Template_a_click_9_listener", "ctx_r33", "ctx_r14", "messageFromRedirect", "FrontLayoutComponent", "constructor", "router", "statusService", "MessageService", "themeService", "dataService", "cdr", "sidebarOpen", "profileMenuOpen", "currentUser", "isMobile<PERSON>iew", "destroy$", "MOBILE_BREAKPOINT", "subscriptions", "checkViewport", "loadUserProfile", "darkMode$", "authUser", "getCurrentUser", "dataUser", "currentUserValue", "console", "log", "user", "updateProfileDisplay", "syncCurrentUser", "subscribe", "next", "updatedUser", "error", "err", "updateCurrentUser", "setCurrentUser", "ngOnInit", "subscribeToQueryParams", "subscribeToCurrentUser", "subscribeToRouterEvents", "setupNotificationSystem", "window", "innerWidth", "countSubscription", "notificationCount$", "pipe", "count", "detectChanges", "push", "handleNotificationCountChange", "event", "detail", "addEventListener", "unsubscribe", "removeEventListener", "setTimeout", "currentCount", "getNotifications", "authProfileSub", "currentUser$", "dataProfileSub", "fullName", "imageFound", "profileImage", "trim", "image", "startsWith", "urlBackend", "replace", "queryParams", "params", "events", "toggleProfileMenu", "navigate", "clearAuthData", "ngOnDestroy", "for<PERSON>ach", "sub", "complete", "ɵɵdirectiveInject", "i1", "AuthuserService", "i2", "ActivatedRoute", "Router", "i3", "UserStatusService", "i4", "i5", "ThemeService", "i6", "DataService", "ChangeDetectorRef", "selectors", "hostBindings", "FrontLayoutComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "FrontLayoutComponent_a_18_Template", "FrontLayoutComponent_a_19_Template", "FrontLayoutComponent_a_20_Template", "FrontLayoutComponent_a_21_Template", "FrontLayoutComponent_a_22_Template", "FrontLayoutComponent_a_23_Template", "FrontLayoutComponent_a_25_Template", "FrontLayoutComponent_Template_button_click_33_listener", "FrontLayoutComponent_ng_container_47_Template", "FrontLayoutComponent_ng_template_48_Template", "ɵɵtemplateRefExtractor", "FrontLayoutComponent_Template_div_click_50_listener", "FrontLayoutComponent_Template_div_click_52_listener", "$event", "stopPropagation", "FrontLayoutComponent_Template_button_click_65_listener", "FrontLayoutComponent_a_69_Template", "FrontLayoutComponent_a_70_Template", "FrontLayoutComponent_ng_container_72_Template", "FrontLayoutComponent_ng_container_73_Template", "FrontLayoutComponent_div_79_Template", "ɵɵclassProp", "role", "_r8", "ɵɵpureFunction7", "_c10", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\front-layout\\front-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\front-layout\\front-layout.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  On<PERSON>ni<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HostListener,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router, NavigationEnd } from '@angular/router';\r\nimport { Subject, Subscription, Observable } from 'rxjs';\r\nimport { filter, takeUntil, distinctUntilChanged, take } from 'rxjs/operators';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { UserStatusService } from 'src/app/services/user-status.service';\r\nimport { MessageService } from 'src/app/services/message.service';\r\nimport { User } from 'src/app/models/user.model';\r\nimport { ThemeService } from '@app/services/theme.service';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { environment } from 'src/environments/environment';\r\n@Component({\r\n  selector: 'app-front-layout',\r\n  templateUrl: './front-layout.component.html',\r\n  styleUrls: ['./front-layout.component.css'],\r\n})\r\nexport class FrontLayoutComponent implements OnInit, OnD<PERSON><PERSON> {\r\n  sidebarOpen = false;\r\n  profileMenuOpen = false;\r\n  currentUser: User | null = null;\r\n  messageFromRedirect: string = '';\r\n  unreadNotificationsCount = 0;\r\n  isMobileView = false;\r\n  username: string = '';\r\n  imageProfile: string = '';\r\n  isDarkMode$: Observable<boolean>;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  private readonly MOBILE_BREAKPOINT = 768;\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    public authService: AuthuserService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    public statusService: UserStatusService,\r\n    private MessageService: MessageService,\r\n    private themeService: ThemeService,\r\n    private dataService: DataService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.checkViewport();\r\n    this.loadUserProfile();\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n  }\r\n\r\n  private loadUserProfile(): void {\r\n    // Try to get user from both services for maximum reliability\r\n    const authUser = this.authService.getCurrentUser();\r\n    const dataUser = this.dataService.currentUserValue;\r\n\r\n    console.log('Auth User:', authUser);\r\n    console.log('Data User:', dataUser);\r\n\r\n    // Prefer dataUser if available, otherwise use authUser\r\n    const user = dataUser || authUser;\r\n\r\n    if (user) {\r\n      this.updateProfileDisplay(user);\r\n\r\n      // Forcer une synchronisation complète des données utilisateur\r\n      if (this.authService.userLoggedIn()) {\r\n        this.dataService.syncCurrentUser().subscribe({\r\n          next: (updatedUser) => {\r\n            console.log('User profile synced:', updatedUser);\r\n            this.updateProfileDisplay(updatedUser);\r\n          },\r\n          error: (err) => {\r\n            console.error('Failed to sync user profile:', err);\r\n          },\r\n        });\r\n      }\r\n    } else {\r\n      // Default values if no user is found\r\n      this.username = '';\r\n      this.imageProfile = 'assets/images/default-profile.png';\r\n    }\r\n\r\n    console.log('Front layout - Image profile loaded:', this.imageProfile);\r\n\r\n    // Sync user data between services if needed\r\n    if (authUser && !dataUser) {\r\n      this.dataService.updateCurrentUser(authUser);\r\n    } else if (!authUser && dataUser) {\r\n      this.authService.setCurrentUser(dataUser);\r\n    }\r\n  }\r\n  ngOnInit(): void {\r\n    this.subscribeToQueryParams();\r\n    this.subscribeToCurrentUser();\r\n    this.subscribeToRouterEvents();\r\n    this.setupNotificationSystem();\r\n  }\r\n  @HostListener('window:resize')\r\n  private checkViewport(): void {\r\n    this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;\r\n    if (!this.isMobileView) {\r\n      this.sidebarOpen = false;\r\n    }\r\n  }\r\n  private setupNotificationSystem(): void {\r\n    console.log('🔔 LAYOUT: Setting up notification system...');\r\n\r\n    // Approche 1: Subscription normale\r\n    const countSubscription = this.MessageService.notificationCount$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((count) => {\r\n        console.log(\r\n          '🔔 LAYOUT: Notification count updated via subscription:',\r\n          count\r\n        );\r\n        this.unreadNotificationsCount = count;\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    this.subscriptions.push(countSubscription);\r\n\r\n    // Approche 2: Événement personnalisé (solution de secours)\r\n    const handleNotificationCountChange = (event: any) => {\r\n      const count = event.detail.count;\r\n      console.log(\r\n        '🔔 LAYOUT: Notification count updated via custom event:',\r\n        count\r\n      );\r\n      this.unreadNotificationsCount = count;\r\n      this.cdr.detectChanges();\r\n    };\r\n\r\n    window.addEventListener(\r\n      'notificationCountChanged',\r\n      handleNotificationCountChange\r\n    );\r\n\r\n    // Nettoyer l'événement à la destruction\r\n    this.subscriptions.push({\r\n      unsubscribe: () =>\r\n        window.removeEventListener(\r\n          'notificationCountChanged',\r\n          handleNotificationCountChange\r\n        ),\r\n    } as Subscription);\r\n\r\n    // Forcer une première lecture du compteur\r\n    setTimeout(() => {\r\n      this.MessageService.notificationCount$\r\n        .pipe(take(1))\r\n        .subscribe((currentCount) => {\r\n          console.log('🔔 LAYOUT: Initial count read:', currentCount);\r\n          this.unreadNotificationsCount = currentCount;\r\n          this.cdr.detectChanges();\r\n        });\r\n    }, 100);\r\n\r\n    // Charger les notifications initiales si l'utilisateur est connecté\r\n    if (this.authService.userLoggedIn()) {\r\n      this.MessageService.getNotifications(true).subscribe();\r\n    }\r\n  }\r\n  private subscribeToCurrentUser(): void {\r\n    // S'abonner aux changements d'image de profil via AuthUserService\r\n    const authProfileSub = this.authService.currentUser$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((user) => {\r\n        this.currentUser = user;\r\n        this.updateProfileDisplay(user);\r\n      });\r\n\r\n    // S'abonner aux changements d'image de profil via DataService\r\n    const dataProfileSub = this.dataService.currentUser$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((user) => {\r\n        if (user) {\r\n          this.currentUser = user;\r\n          this.updateProfileDisplay(user);\r\n        }\r\n      });\r\n\r\n    this.subscriptions.push(authProfileSub, dataProfileSub);\r\n  }\r\n\r\n  private updateProfileDisplay(user: any): void {\r\n    if (user) {\r\n      this.username = user.fullName || user.username || '';\r\n\r\n      // Vérification plus robuste pour l'image de profil\r\n      let imageFound = false;\r\n\r\n      // Vérifier profileImage en premier\r\n      if (\r\n        user.profileImage &&\r\n        user.profileImage !== 'null' &&\r\n        user.profileImage.trim() !== '' &&\r\n        user.profileImage !== 'undefined'\r\n      ) {\r\n        this.imageProfile = user.profileImage;\r\n        imageFound = true;\r\n        console.log('Using profileImage:', this.imageProfile);\r\n      }\r\n\r\n      // Ensuite vérifier image si profileImage n'est pas valide\r\n      if (\r\n        !imageFound &&\r\n        user.image &&\r\n        user.image !== 'null' &&\r\n        user.image.trim() !== '' &&\r\n        user.image !== 'undefined'\r\n      ) {\r\n        this.imageProfile = user.image;\r\n        imageFound = true;\r\n        console.log('Using image:', this.imageProfile);\r\n      }\r\n\r\n      // Vérifier si l'image est une URL relative au backend\r\n      if (\r\n        imageFound &&\r\n        !this.imageProfile.startsWith('http') &&\r\n        !this.imageProfile.startsWith('assets/')\r\n      ) {\r\n        // Si c'est une URL relative au backend, ajouter le préfixe du backend\r\n        if (this.imageProfile.startsWith('/')) {\r\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}${\r\n            this.imageProfile\r\n          }`;\r\n        } else {\r\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}/${\r\n            this.imageProfile\r\n          }`;\r\n        }\r\n        console.log('Converted to absolute URL:', this.imageProfile);\r\n      }\r\n\r\n      // Si aucune image valide n'est trouvée, utiliser l'image par défaut\r\n      if (!imageFound) {\r\n        this.imageProfile = 'assets/images/default-profile.png';\r\n        console.log('Using default image');\r\n      }\r\n\r\n      console.log('Front layout - Image profile updated:', this.imageProfile);\r\n    }\r\n  }\r\n  private subscribeToQueryParams(): void {\r\n    this.route.queryParams\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((params) => {\r\n        this.messageFromRedirect = params['message'] || '';\r\n      });\r\n  }\r\n  private subscribeToRouterEvents(): void {\r\n    this.router.events\r\n      .pipe(\r\n        filter((event) => event instanceof NavigationEnd),\r\n        takeUntil(this.destroy$)\r\n      )\r\n      .subscribe(() => {\r\n        this.sidebarOpen = false;\r\n        this.profileMenuOpen = false;\r\n      });\r\n  }\r\n  toggleSidebar(): void {\r\n    this.sidebarOpen = !this.sidebarOpen;\r\n  }\r\n  toggleProfileMenu(): void {\r\n    this.profileMenuOpen = !this.profileMenuOpen;\r\n  }\r\n\r\n  toggleDarkMode(): void {\r\n    this.themeService.toggleDarkMode();\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout().subscribe({\r\n      next: () => {\r\n        this.profileMenuOpen = false;\r\n        this.sidebarOpen = false;\r\n        this.currentUser = null;\r\n        // Reset image to default\r\n        this.imageProfile = 'assets/images/default-profile.png';\r\n        // Clear data in both services\r\n        this.dataService.updateCurrentUser({});\r\n        this.router.navigate(['/login']);\r\n      },\r\n      error: (err) => {\r\n        console.error('Logout error:', err);\r\n        this.authService.clearAuthData();\r\n        this.currentUser = null;\r\n        // Reset image to default\r\n        this.imageProfile = 'assets/images/default-profile.png';\r\n        // Clear data in both services\r\n        this.dataService.updateCurrentUser({});\r\n        this.router.navigate(['/login']);\r\n      },\r\n    });\r\n  }\r\n  ngOnDestroy(): void {\r\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n}\r\n", "<div\r\n  class=\"flex h-screen main-grid-container text-[#6d6870] dark:text-[#a0a0a0] futuristic-layout\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background Grid -->\r\n  <div class=\"background-grid\"></div>\r\n\r\n  <!-- Sidebar -->\r\n  <div class=\"hidden md:flex md:flex-shrink-0\">\r\n    <div\r\n      class=\"flex flex-col w-64 bg-white dark:bg-[#1e1e1e] border-r border-[#edf1f4] dark:border-[#2a2a2a] backdrop-blur-sm\"\r\n    >\r\n      <div\r\n        class=\"flex items-center justify-center h-16 px-4 relative overflow-hidden\"\r\n      >\r\n        <!-- Decorative elements -->\r\n        <div\r\n          class=\"absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-transparent rounded-full\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute -bottom-6 -right-6 w-12 h-12 bg-gradient-to-tl from-[#4f5fad]/20 to-transparent rounded-full\"\r\n        ></div>\r\n\r\n        <div class=\"flex items-center relative z-10\">\r\n          <div class=\"relative\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-8 w-8 text-[#4f5fad] dark:text-[#6d78c9] transform rotate-12\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\r\n              />\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n              />\r\n            </svg>\r\n            <!-- Glow effect -->\r\n            <div\r\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n            ></div>\r\n          </div>\r\n          <span\r\n            class=\"ml-2 text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n            >DevBridge</span\r\n          >\r\n        </div>\r\n      </div>\r\n      <div class=\"flex flex-col flex-grow px-4 py-4\">\r\n        <nav class=\"flex-1 space-y-2\">\r\n          <!-- Navigation Items - Réorganisés avec Accueil en premier -->\r\n\r\n          <!-- Accueil button -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            [routerLinkActiveOptions]=\"{ exact: true }\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-home h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Accueil</span>\r\n            </div>\r\n          </a>\r\n\r\n          <!-- Projects -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/projects\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-rocket h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Projects</span>\r\n            </div>\r\n          </a>\r\n\r\n          <!-- Plannings -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/plannings\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"far fa-calendar-check h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Plannings</span>\r\n            </div>\r\n          </a>\r\n\r\n          <!-- Réunions -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/reunions\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-users-cog h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Réunions</span>\r\n            </div>\r\n          </a>\r\n\r\n          <!-- Messages -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/messages\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"far fa-comment-dots h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Messages</span>\r\n            </div>\r\n          </a>\r\n          <!-- Equipes -->\r\n          <a\r\n            *ngIf=\"authService.userLoggedIn()\"\r\n            routerLink=\"/equipes\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\r\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-users h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Equipes</span>\r\n            </div>\r\n          </a>\r\n          <!-- Séparateur -->\r\n          <div\r\n            class=\"border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2\"\r\n          ></div>\r\n\r\n          <!-- Go to Dashboard button - uniquement pour admin et teacher -->\r\n          <a\r\n            *ngIf=\"\r\n              authService.userLoggedIn() &&\r\n              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')\r\n            \"\r\n            routerLink=\"/admin/dashboard\"\r\n            routerLinkActive=\"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\"\r\n            class=\"sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-all group-hover:scale-110\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span class=\"relative\">Go to Dashboard</span>\r\n            </div>\r\n          </a>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- La grille est maintenant gérée par la classe background-grid -->\r\n  <!-- Header -->\r\n  <header\r\n    class=\"fixed top-0 left-0 right-0 bg-white dark:bg-[#1e1e1e] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] z-50 backdrop-blur-sm border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\r\n  >\r\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n      <!-- Decorative elements -->\r\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        <div\r\n          class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\r\n        ></div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center justify-between h-16 relative z-10\">\r\n        <!-- Logo and main nav (left side) -->\r\n        <div class=\"flex items-center\">\r\n          <!-- Mobile menu button -->\r\n          <button\r\n            (click)=\"toggleSidebar()\"\r\n            class=\"md:hidden flex items-center justify-center h-8 px-3 rounded-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] focus:outline-none transition-colors relative group overflow-hidden\"\r\n            aria-label=\"Toggle menu\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-md blur-md\"\r\n            ></div>\r\n            <svg\r\n              class=\"h-5 w-5 relative z-10\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M4 6h16M4 12h16M4 18h16\"\r\n              />\r\n            </svg>\r\n            <span\r\n              class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-2 relative z-10\"\r\n              >Menu</span\r\n            >\r\n          </button>\r\n\r\n          <!-- Logo -->\r\n          <a routerLink=\"/\" class=\"flex-shrink-0 flex items-center group\">\r\n            <div class=\"relative\">\r\n              <h1\r\n                class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-105 transition-transform\"\r\n              >\r\n                DevBridge\r\n              </h1>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 blur-xl rounded-full transform scale-150 -z-10 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n              ></div>\r\n            </div>\r\n            <span\r\n              class=\"ml-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] hidden md:block\"\r\n              >Project Management Suite</span\r\n            >\r\n          </a>\r\n\r\n          <!-- Navigation - Removed and moved to sidebar -->\r\n        </div>\r\n\r\n        <!-- Right side -->\r\n        <div class=\"flex items-center\">\r\n          <ng-container *ngIf=\"authService.userLoggedIn(); else authButtons\">\r\n            <div class=\"ml-4 flex items-center md:ml-6\">\r\n              <!-- Bouton de notification simplifié -->\r\n              <a\r\n                *ngIf=\"authService.userLoggedIn()\"\r\n                routerLink=\"/notifications\"\r\n                class=\"flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]\"\r\n                aria-label=\"Notifications\"\r\n              >\r\n                <!-- Contenu du bouton avec icône et compteur -->\r\n                <div class=\"flex items-center\">\r\n                  <i class=\"far fa-bell text-lg transition-transform mr-2\"></i>\r\n\r\n                  <!-- Compteur de notifications avec couleur orange fluo -->\r\n                  <div\r\n                    class=\"flex items-center justify-center h-6 min-w-6 px-1.5 rounded-md bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white font-bold text-xs shadow-md animate-pulse\"\r\n                  >\r\n                    {{\r\n                      unreadNotificationsCount > 0\r\n                        ? unreadNotificationsCount > 99\r\n                          ? \"99+\"\r\n                          : unreadNotificationsCount\r\n                        : \"0\"\r\n                    }}\r\n                  </div>\r\n                </div>\r\n              </a>\r\n\r\n              <!-- Dark Mode Toggle Button -->\r\n              <button\r\n                (click)=\"toggleDarkMode()\"\r\n                class=\"flex items-center justify-center h-9 w-9 rounded-xl bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] mr-3 transition-all duration-300 relative overflow-hidden group\"\r\n                aria-label=\"Toggle dark mode\"\r\n              >\r\n                <!-- Modern animated border with glow -->\r\n                <div class=\"absolute inset-0 rounded-xl overflow-hidden\">\r\n                  <div\r\n                    class=\"absolute inset-0 rounded-xl border-2 border-[#4f5fad]/30 dark:border-[#6d78c9]/30 opacity-100\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute -inset-1 bg-gradient-to-r from-[#4f5fad]/0 via-[#4f5fad]/30 to-[#4f5fad]/0 dark:from-[#6d78c9]/0 dark:via-[#6d78c9]/30 dark:to-[#6d78c9]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine\"\r\n                  ></div>\r\n                </div>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\r\n                ></div>\r\n                <div\r\n                  class=\"relative z-10 transition-all duration-500 ease-in-out\"\r\n                  [ngClass]=\"{ 'rotate-180': isDarkMode$ | async }\"\r\n                >\r\n                  <i\r\n                    *ngIf=\"!(isDarkMode$ | async)\"\r\n                    class=\"far fa-moon text-lg group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                  <i\r\n                    *ngIf=\"isDarkMode$ | async\"\r\n                    class=\"far fa-sun text-lg group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                </div>\r\n              </button>\r\n\r\n              <!-- Logout Button -->\r\n              <button\r\n                (click)=\"logout()\"\r\n                class=\"flex items-center justify-center h-8 w-8 rounded-full bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#ff6b69] dark:text-[#ff8785] mr-3 transition-all duration-300 relative overflow-hidden group\"\r\n                aria-label=\"Logout\"\r\n              >\r\n                <!-- Animated border -->\r\n                <div class=\"absolute inset-0 rounded-full overflow-hidden\">\r\n                  <div\r\n                    class=\"absolute inset-0 rounded-full border border-[#ff6b69]/20 dark:border-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute -inset-1 bg-gradient-to-r from-[#ff6b69]/0 via-[#ff6b69]/30 to-[#ff6b69]/0 dark:from-[#ff8785]/0 dark:via-[#ff8785]/30 dark:to-[#ff8785]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine\"\r\n                  ></div>\r\n                </div>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/10 dark:bg-[#ff8785]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\r\n                ></div>\r\n                <i\r\n                  class=\"fas fa-sign-out-alt relative z-10 group-hover:scale-110 transition-transform\"\r\n                ></i>\r\n              </button>\r\n\r\n              <!-- Profile Button -->\r\n              <a\r\n                routerLink=\"/profile\"\r\n                class=\"flex items-center bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] px-3 py-2 rounded-lg transition-all group\"\r\n              >\r\n                <span class=\"sr-only\">Profile</span>\r\n                <span\r\n                  class=\"hidden md:inline-block mr-2 text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-105 transition-transform\"\r\n                >\r\n                  {{ username }}\r\n                </span>\r\n                <div\r\n                  class=\"h-8 w-8 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9] flex items-center justify-center relative group-hover:border-[#3d4a85] dark:group-hover:border-[#4f5fad] transition-colors\"\r\n                >\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\r\n                  ></div>\r\n                  <img\r\n                    [src]=\"imageProfile\"\r\n                    alt=\"Profile\"\r\n                    class=\"h-full w-full object-cover\"\r\n                  />\r\n                </div>\r\n              </a>\r\n            </div>\r\n          </ng-container>\r\n\r\n          <!-- Auth Buttons for non-logged in users -->\r\n          <ng-template #authButtons>\r\n            <div class=\"flex space-x-4\">\r\n              <a\r\n                routerLink=\"/login\"\r\n                class=\"inline-flex items-center relative overflow-hidden group\"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\r\n                ></div>\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n                ></div>\r\n                <span\r\n                  class=\"relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all\"\r\n                >\r\n                  <i class=\"fas fa-sign-in-alt mr-2\"></i>\r\n                  Connexion\r\n                </span>\r\n              </a>\r\n\r\n              <a\r\n                routerLink=\"/signup\"\r\n                class=\"inline-flex items-center relative overflow-hidden group\"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg transition-transform duration-300 group-hover:scale-105\"\r\n                ></div>\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n                ></div>\r\n                <span\r\n                  class=\"relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all\"\r\n                >\r\n                  <i class=\"fas fa-user-plus mr-2\"></i>\r\n                  Inscription\r\n                </span>\r\n              </a>\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- Barre latérale mobile -->\r\n  <div\r\n    class=\"fixed inset-0 z-40 md:hidden\"\r\n    [class.hidden]=\"!sidebarOpen\"\r\n    (click)=\"sidebarOpen && toggleSidebar()\"\r\n  >\r\n    <!-- Overlay backdrop with blur effect -->\r\n    <div\r\n      class=\"absolute inset-0 bg-black/30 dark:bg-black/50 backdrop-blur-sm\"\r\n    ></div>\r\n\r\n    <!-- Contenu de la barre latérale -->\r\n    <div\r\n      class=\"fixed inset-y-0 left-0 max-w-xs w-full bg-white dark:bg-[#1e1e1e] shadow-lg dark:shadow-[0_0_30px_rgba(0,0,0,0.3)] transform transition-transform duration-300 ease-in-out border-r border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\r\n      [class.translate-x-0]=\"sidebarOpen\"\r\n      [class.-translate-x-full]=\"!sidebarOpen\"\r\n      (click)=\"$event.stopPropagation()\"\r\n    >\r\n      <div class=\"flex flex-col h-full relative\">\r\n        <!-- Decorative elements -->\r\n        <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          <div\r\n            class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-[10%] left-[5%] w-32 h-32 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute bottom-[10%] right-[5%] w-40 h-40 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl\"\r\n          ></div>\r\n        </div>\r\n\r\n        <!-- En-tête de la barre latérale -->\r\n        <div\r\n          class=\"flex items-center justify-between px-4 py-3 border-b border-[#edf1f4] dark:border-[#2a2a2a] relative z-10\"\r\n        >\r\n          <div>\r\n            <h3\r\n              class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n            >\r\n              DevBridge\r\n            </h3>\r\n            <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n              Project Management Suite\r\n            </p>\r\n          </div>\r\n          <button\r\n            (click)=\"toggleSidebar()\"\r\n            class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] p-2 rounded-full hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors relative group\"\r\n            aria-label=\"Close menu\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md\"\r\n            ></div>\r\n            <i class=\"fas fa-times relative z-10\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Navigation de la barre latérale -->\r\n        <nav\r\n          class=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto bg-white dark:bg-[#1e1e1e] relative z-10\"\r\n        >\r\n          <!-- Navigation Items - Réorganisés avec Accueil en premier -->\r\n          <a\r\n            *ngFor=\"\r\n              let item of [\r\n                {\r\n                  route: '/',\r\n                  icon: 'fas fa-home',\r\n                  text: 'Accueil'\r\n                },\r\n                {\r\n                  route: '/projects',\r\n                  icon: 'fas fa-rocket',\r\n                  text: 'Projects'\r\n                },\r\n                {\r\n                  route: '/plannings',\r\n                  icon: 'far fa-calendar-check',\r\n                  text: 'Plannings'\r\n                },\r\n                {\r\n                  route: '/reunions',\r\n                  icon: 'fas fa-users-cog',\r\n                  text: 'Réunions'\r\n                },\r\n                {\r\n                  route: '/messages',\r\n                  icon: 'far fa-comment-dots',\r\n                  text: 'Messages'\r\n                },\r\n                {\r\n                  route: '/equipes',\r\n                  icon: 'fas fa-users',\r\n                  text: 'Equipes'\r\n                },\r\n                {\r\n                  route: '/notifications',\r\n                  icon: 'far fa-bell',\r\n                  text: 'Notifications',\r\n                  badge: unreadNotificationsCount\r\n                }\r\n              ]\r\n            \"\r\n            routerLink=\"{{ item.route }}\"\r\n            (click)=\"toggleSidebar()\"\r\n            class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\r\n            routerLinkActive=\"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\"\r\n            [routerLinkActiveOptions]=\"{\r\n              exact: item.route === '/' ? true : false\r\n            }\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center w-full\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"{{\r\n                    item.icon\r\n                  }} h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span>{{ item.text }}</span>\r\n              <div\r\n                *ngIf=\"item.badge && item.badge > 0\"\r\n                class=\"ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse\"\r\n              >\r\n                {{ item.badge > 99 ? \"99+\" : item.badge }}\r\n              </div>\r\n            </div>\r\n          </a>\r\n\r\n          <!-- Go to Dashboard button - uniquement pour admin et teacher -->\r\n          <a\r\n            *ngIf=\"\r\n              authService.userLoggedIn() &&\r\n              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')\r\n            \"\r\n            routerLink=\"/admin/dashboard\"\r\n            (click)=\"toggleSidebar()\"\r\n            class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2\"\r\n          >\r\n            <!-- Hover effect -->\r\n            <span\r\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n            ></span>\r\n\r\n            <div class=\"relative z-10 flex items-center w-full\">\r\n              <div class=\"relative\">\r\n                <i\r\n                  class=\"fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-colors\"\r\n                ></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span>Go to Dashboard</span>\r\n            </div>\r\n          </a>\r\n\r\n          <div\r\n            class=\"border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2\"\r\n          ></div>\r\n\r\n          <ng-container *ngIf=\"!authService.userLoggedIn()\">\r\n            <a\r\n              routerLink=\"/login\"\r\n              (click)=\"toggleSidebar()\"\r\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\r\n            >\r\n              <!-- Hover effect -->\r\n              <span\r\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n              ></span>\r\n\r\n              <div class=\"relative z-10 flex items-center\">\r\n                <div class=\"relative\">\r\n                  <i\r\n                    class=\"fas fa-sign-in-alt mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                <span>Connexion</span>\r\n              </div>\r\n            </a>\r\n\r\n            <a\r\n              routerLink=\"/signup\"\r\n              (click)=\"toggleSidebar()\"\r\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\r\n            >\r\n              <!-- Hover effect -->\r\n              <span\r\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n              ></span>\r\n\r\n              <div class=\"relative z-10 flex items-center\">\r\n                <div class=\"relative\">\r\n                  <i\r\n                    class=\"fas fa-user-plus mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                <span>Inscription</span>\r\n              </div>\r\n            </a>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"authService.userLoggedIn()\">\r\n            <a\r\n              routerLink=\"/profile\"\r\n              (click)=\"toggleSidebar()\"\r\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\r\n            >\r\n              <!-- Hover effect -->\r\n              <span\r\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n              ></span>\r\n\r\n              <div class=\"relative z-10 flex items-center\">\r\n                <div class=\"relative\">\r\n                  <i\r\n                    class=\"fas fa-user mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                <span>Mon Profil</span>\r\n              </div>\r\n            </a>\r\n\r\n            <a\r\n              (click)=\"logout(); toggleSidebar()\"\r\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden cursor-pointer\"\r\n            >\r\n              <!-- Hover effect -->\r\n              <span\r\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] opacity-0 group-hover:opacity-100 transition-opacity\"\r\n              ></span>\r\n\r\n              <div class=\"relative z-10 flex items-center\">\r\n                <div class=\"relative\">\r\n                  <i\r\n                    class=\"fas fa-sign-out-alt mr-3 text-[#ff6b69] dark:text-[#ff8785] group-hover:scale-110 transition-transform\"\r\n                  ></i>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                <span>Déconnexion</span>\r\n              </div>\r\n            </a>\r\n          </ng-container>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"flex-1 flex flex-col overflow-hidden\">\r\n    <!-- Contenu principal -->\r\n    <main\r\n      class=\"flex-1 overflow-y-auto bg-[#edf1f4] dark:bg-[#121212] pt-16 pb-6 relative\"\r\n    >\r\n      <!-- Background decorative elements -->\r\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        <div\r\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- Message de statut -->\r\n      <div\r\n        *ngIf=\"messageFromRedirect\"\r\n        class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4\"\r\n      >\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] border-l-4 border-[#ff8c00] dark:border-[#ff6b00] rounded-lg p-4 mb-6 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] backdrop-blur-sm\"\r\n        >\r\n          <div class=\"flex items-center\">\r\n            <div class=\"relative\">\r\n              <i\r\n                class=\"fas fa-info-circle text-[#ff8c00] dark:text-[#ff6b00] text-lg mr-3\"\r\n              ></i>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#ff8c00]/30 dark:bg-[#ff6b00]/30 blur-xl rounded-full transform scale-150 -z-10 animate-pulse\"\r\n              ></div>\r\n            </div>\r\n            <p\r\n              class=\"ml-3 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\"\r\n            >\r\n              {{ messageFromRedirect }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Router outlet -->\r\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <router-outlet></router-outlet>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAOA,SAAiCA,aAAa,QAAQ,iBAAiB;AACvE,SAASC,OAAO,QAAkC,MAAM;AACxD,SAASC,MAAM,EAAEC,SAAS,EAAwBC,IAAI,QAAQ,gBAAgB;AAO9E,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;IC6ChDC,EAAA,CAAAC,cAAA,YAMC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;IAlBvCH,EAAA,CAAAK,UAAA,4BAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAA2C;;;;;IAuB7CP,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,oBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAI1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IASzCH,EAAA,CAAAC,cAAA,YAQC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAkF7CH,EAAA,CAAAC,cAAA,aAKC;IAGGD,EAAA,CAAAE,SAAA,aAA6D;IAG7DF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,GAOF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAPJH,EAAA,CAAAQ,SAAA,GAOF;IAPER,EAAA,CAAAS,kBAAA,MAAAC,OAAA,CAAAC,wBAAA,OAAAD,OAAA,CAAAC,wBAAA,gBAAAD,OAAA,CAAAC,wBAAA,YAOF;;;;;IA2BAX,EAAA,CAAAE,SAAA,aAGK;;;;;IACLF,EAAA,CAAAE,SAAA,aAGK;;;;;;;;;;;IA1DbF,EAAA,CAAAY,uBAAA,GAAmE;IACjEZ,EAAA,CAAAC,cAAA,cAA4C;IAE1CD,EAAA,CAAAa,UAAA,IAAAC,iDAAA,gBAuBI;IAGJd,EAAA,CAAAC,cAAA,iBAIC;IAHCD,EAAA,CAAAe,UAAA,mBAAAC,sEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAK1BtB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAGC;;IACCD,EAAA,CAAAa,UAAA,KAAAU,kDAAA,gBAGK;;IACLvB,EAAA,CAAAa,UAAA,KAAAW,kDAAA,gBAGK;;IACPxB,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAe,UAAA,mBAAAU,uEAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAK,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAKlB3B,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAE,SAAA,eAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAE,SAAA,eAEO;IAITF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,aAGC;IACuBD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAMTF,EAAA,CAAAG,YAAA,EAAM;IAGZH,EAAA,CAAA4B,qBAAA,EAAe;;;;IA5GR5B,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAK,UAAA,SAAAwB,MAAA,CAAAC,WAAA,CAAAC,YAAA,GAAgC;IA6C/B/B,EAAA,CAAAQ,SAAA,GAAiD;IAAjDR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAjC,EAAA,CAAAkC,WAAA,OAAAL,MAAA,CAAAM,WAAA,GAAiD;IAG9CnC,EAAA,CAAAQ,SAAA,GAA4B;IAA5BR,EAAA,CAAAK,UAAA,UAAAL,EAAA,CAAAkC,WAAA,QAAAL,MAAA,CAAAM,WAAA,EAA4B;IAI5BnC,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAAkC,WAAA,SAAAL,MAAA,CAAAM,WAAA,EAAyB;IAuC5BnC,EAAA,CAAAQ,SAAA,IACF;IADER,EAAA,CAAAS,kBAAA,MAAAoB,MAAA,CAAAO,QAAA,MACF;IASIpC,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAK,UAAA,QAAAwB,MAAA,CAAAQ,YAAA,EAAArC,EAAA,CAAAsC,aAAA,CAAoB;;;;;IAW5BtC,EAAA,CAAAC,cAAA,eAA4B;IAKxBD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IA8ITH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAA8B,QAAA,CAAAC,KAAA,gBAAAD,QAAA,CAAAC,KAAA,MACF;;;;;;;;;;;IAxEJxC,EAAA,CAAAC,cAAA,aAgDC;IANCD,EAAA,CAAAe,UAAA,mBAAA0B,sDAAA;MAAAzC,EAAA,CAAAiB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAQzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,QAIK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAa,UAAA,IAAAgC,wCAAA,mBAKM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IAhCNH,EAAA,CAAA8C,qBAAA,eAAAP,QAAA,CAAAQ,KAAA,CAA6B;IAI7B/C,EAAA,CAAAK,UAAA,4BAAAL,EAAA,CAAAgC,eAAA,IAAAgB,GAAA,EAAAT,QAAA,CAAAQ,KAAA,yBAEE;IAUI/C,EAAA,CAAAQ,SAAA,GAEgI;IAFhIR,EAAA,CAAAiD,sBAAA,KAAAV,QAAA,CAAAW,IAAA,kIAEgI;IAO9HlD,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAmD,iBAAA,CAAAZ,QAAA,CAAAa,IAAA,CAAe;IAElBpD,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAK,UAAA,SAAAkC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,KAAkC;;;;;;IASzCxC,EAAA,CAAAC,cAAA,aAQC;IAFCD,EAAA,CAAAe,UAAA,mBAAAsC,sDAAA;MAAArD,EAAA,CAAAiB,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAkC,OAAA,CAAAX,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAQhCH,EAAA,CAAAY,uBAAA,GAAkD;IAChDZ,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAAyC,iEAAA;MAAAxD,EAAA,CAAAiB,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAqC,OAAA,CAAAd,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI1BH,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAA4C,iEAAA;MAAA3D,EAAA,CAAAiB,aAAA,CAAAwC,IAAA;MAAA,MAAAG,OAAA,GAAA5D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAuC,OAAA,CAAAhB,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,gBAEQ;IAERF,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAE,SAAA,cAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG9BH,EAAA,CAAA4B,qBAAA,EAAe;;;;;;IAEf5B,EAAA,CAAAY,uBAAA,GAAiD;IAC/CZ,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAA8C,iEAAA;MAAA7D,EAAA,CAAAiB,aAAA,CAAA6C,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA0C,OAAA,CAAAnB,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI3BH,EAAA,CAAAC,cAAA,aAGC;IAFCD,EAAA,CAAAe,UAAA,mBAAAiD,iEAAA;MAAAhE,EAAA,CAAAiB,aAAA,CAAA6C,IAAA;MAAA,MAAAG,OAAA,GAAAjE,EAAA,CAAAoB,aAAA;MAAS6C,OAAA,CAAAtC,MAAA,EAAQ;MAAA,OAAE3B,EAAA,CAAAqB,WAAA,CAAA4C,OAAA,CAAArB,aAAA,EAAe;IAAA,EAAC;IAInC5C,EAAA,CAAAE,SAAA,iBAEQ;IAERF,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAE,SAAA,cAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,wBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG9BH,EAAA,CAAA4B,qBAAA,EAAe;;;;;IAuBnB5B,EAAA,CAAAC,cAAA,eAGC;IAMOD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAyD,OAAA,CAAAC,mBAAA,MACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADzwBZ,OAAM,MAAOC,oBAAoB;EAe/BC,YACSvC,WAA4B,EAC3BiB,KAAqB,EACrBuB,MAAc,EACfC,aAAgC,EAC/BC,cAA8B,EAC9BC,YAA0B,EAC1BC,WAAwB,EACxBC,GAAsB;IAPvB,KAAA7C,WAAW,GAAXA,WAAW;IACV,KAAAiB,KAAK,GAALA,KAAK;IACL,KAAAuB,MAAM,GAANA,MAAM;IACP,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,GAAG,GAAHA,GAAG;IAtBb,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAX,mBAAmB,GAAW,EAAE;IAChC,KAAAxD,wBAAwB,GAAG,CAAC;IAC5B,KAAAoE,YAAY,GAAG,KAAK;IACpB,KAAA3C,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IAGjB,KAAA2C,QAAQ,GAAG,IAAIrF,OAAO,EAAQ;IACrB,KAAAsF,iBAAiB,GAAG,GAAG;IAChC,KAAAC,aAAa,GAAmB,EAAE;IAYxC,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACjD,WAAW,GAAG,IAAI,CAACsC,YAAY,CAACY,SAAS;EAChD;EAEQD,eAAeA,CAAA;IACrB;IACA,MAAME,QAAQ,GAAG,IAAI,CAACxD,WAAW,CAACyD,cAAc,EAAE;IAClD,MAAMC,QAAQ,GAAG,IAAI,CAACd,WAAW,CAACe,gBAAgB;IAElDC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEL,QAAQ,CAAC;IACnCI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,QAAQ,CAAC;IAEnC;IACA,MAAMI,IAAI,GAAGJ,QAAQ,IAAIF,QAAQ;IAEjC,IAAIM,IAAI,EAAE;MACR,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;MAE/B;MACA,IAAI,IAAI,CAAC9D,WAAW,CAACC,YAAY,EAAE,EAAE;QACnC,IAAI,CAAC2C,WAAW,CAACoB,eAAe,EAAE,CAACC,SAAS,CAAC;UAC3CC,IAAI,EAAGC,WAAW,IAAI;YACpBP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,WAAW,CAAC;YAChD,IAAI,CAACJ,oBAAoB,CAACI,WAAW,CAAC;UACxC,CAAC;UACDC,KAAK,EAAGC,GAAG,IAAI;YACbT,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;UACpD;SACD,CAAC;;KAEL,MAAM;MACL;MACA,IAAI,CAAC/D,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACC,YAAY,GAAG,mCAAmC;;IAGzDqD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACtD,YAAY,CAAC;IAEtE;IACA,IAAIiD,QAAQ,IAAI,CAACE,QAAQ,EAAE;MACzB,IAAI,CAACd,WAAW,CAAC0B,iBAAiB,CAACd,QAAQ,CAAC;KAC7C,MAAM,IAAI,CAACA,QAAQ,IAAIE,QAAQ,EAAE;MAChC,IAAI,CAAC1D,WAAW,CAACuE,cAAc,CAACb,QAAQ,CAAC;;EAE7C;EACAc,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEQvB,aAAaA,CAAA;IACnB,IAAI,CAACJ,YAAY,GAAG4B,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC3B,iBAAiB;IAC9D,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;MACtB,IAAI,CAACH,WAAW,GAAG,KAAK;;EAE5B;EACQ8B,uBAAuBA,CAAA;IAC7BhB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAE3D;IACA,MAAMkB,iBAAiB,GAAG,IAAI,CAACrC,cAAc,CAACsC,kBAAkB,CAC7DC,IAAI,CAAClH,SAAS,CAAC,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEiB,KAAK,IAAI;MACnBtB,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDqB,KAAK,CACN;MACD,IAAI,CAACrG,wBAAwB,GAAGqG,KAAK;MACrC,IAAI,CAACrC,GAAG,CAACsC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAAC/B,aAAa,CAACgC,IAAI,CAACL,iBAAiB,CAAC;IAE1C;IACA,MAAMM,6BAA6B,GAAIC,KAAU,IAAI;MACnD,MAAMJ,KAAK,GAAGI,KAAK,CAACC,MAAM,CAACL,KAAK;MAChCtB,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDqB,KAAK,CACN;MACD,IAAI,CAACrG,wBAAwB,GAAGqG,KAAK;MACrC,IAAI,CAACrC,GAAG,CAACsC,aAAa,EAAE;IAC1B,CAAC;IAEDN,MAAM,CAACW,gBAAgB,CACrB,0BAA0B,EAC1BH,6BAA6B,CAC9B;IAED;IACA,IAAI,CAACjC,aAAa,CAACgC,IAAI,CAAC;MACtBK,WAAW,EAAEA,CAAA,KACXZ,MAAM,CAACa,mBAAmB,CACxB,0BAA0B,EAC1BL,6BAA6B;KAElB,CAAC;IAElB;IACAM,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,cAAc,CAACsC,kBAAkB,CACnCC,IAAI,CAACjH,IAAI,CAAC,CAAC,CAAC,CAAC,CACbiG,SAAS,CAAE2B,YAAY,IAAI;QAC1BhC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE+B,YAAY,CAAC;QAC3D,IAAI,CAAC/G,wBAAwB,GAAG+G,YAAY;QAC5C,IAAI,CAAC/C,GAAG,CAACsC,aAAa,EAAE;MAC1B,CAAC,CAAC;IACN,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,IAAI,CAACnF,WAAW,CAACC,YAAY,EAAE,EAAE;MACnC,IAAI,CAACyC,cAAc,CAACmD,gBAAgB,CAAC,IAAI,CAAC,CAAC5B,SAAS,EAAE;;EAE1D;EACQS,sBAAsBA,CAAA;IAC5B;IACA,MAAMoB,cAAc,GAAG,IAAI,CAAC9F,WAAW,CAAC+F,YAAY,CACjDd,IAAI,CAAClH,SAAS,CAAC,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEH,IAAI,IAAI;MAClB,IAAI,CAACd,WAAW,GAAGc,IAAI;MACvB,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;IACjC,CAAC,CAAC;IAEJ;IACA,MAAMkC,cAAc,GAAG,IAAI,CAACpD,WAAW,CAACmD,YAAY,CACjDd,IAAI,CAAClH,SAAS,CAAC,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEH,IAAI,IAAI;MAClB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACd,WAAW,GAAGc,IAAI;QACvB,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;;IAEnC,CAAC,CAAC;IAEJ,IAAI,CAACV,aAAa,CAACgC,IAAI,CAACU,cAAc,EAAEE,cAAc,CAAC;EACzD;EAEQjC,oBAAoBA,CAACD,IAAS;IACpC,IAAIA,IAAI,EAAE;MACR,IAAI,CAACxD,QAAQ,GAAGwD,IAAI,CAACmC,QAAQ,IAAInC,IAAI,CAACxD,QAAQ,IAAI,EAAE;MAEpD;MACA,IAAI4F,UAAU,GAAG,KAAK;MAEtB;MACA,IACEpC,IAAI,CAACqC,YAAY,IACjBrC,IAAI,CAACqC,YAAY,KAAK,MAAM,IAC5BrC,IAAI,CAACqC,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,IAC/BtC,IAAI,CAACqC,YAAY,KAAK,WAAW,EACjC;QACA,IAAI,CAAC5F,YAAY,GAAGuD,IAAI,CAACqC,YAAY;QACrCD,UAAU,GAAG,IAAI;QACjBtC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACtD,YAAY,CAAC;;MAGvD;MACA,IACE,CAAC2F,UAAU,IACXpC,IAAI,CAACuC,KAAK,IACVvC,IAAI,CAACuC,KAAK,KAAK,MAAM,IACrBvC,IAAI,CAACuC,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,IACxBtC,IAAI,CAACuC,KAAK,KAAK,WAAW,EAC1B;QACA,IAAI,CAAC9F,YAAY,GAAGuD,IAAI,CAACuC,KAAK;QAC9BH,UAAU,GAAG,IAAI;QACjBtC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACtD,YAAY,CAAC;;MAGhD;MACA,IACE2F,UAAU,IACV,CAAC,IAAI,CAAC3F,YAAY,CAAC+F,UAAU,CAAC,MAAM,CAAC,IACrC,CAAC,IAAI,CAAC/F,YAAY,CAAC+F,UAAU,CAAC,SAAS,CAAC,EACxC;QACA;QACA,IAAI,IAAI,CAAC/F,YAAY,CAAC+F,UAAU,CAAC,GAAG,CAAC,EAAE;UACrC,IAAI,CAAC/F,YAAY,GAAG,GAAGtC,WAAW,CAACsI,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAC9D,IAAI,CAACjG,YACP,EAAE;SACH,MAAM;UACL,IAAI,CAACA,YAAY,GAAG,GAAGtC,WAAW,CAACsI,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC9D,IAAI,CAACjG,YACP,EAAE;;QAEJqD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACtD,YAAY,CAAC;;MAG9D;MACA,IAAI,CAAC2F,UAAU,EAAE;QACf,IAAI,CAAC3F,YAAY,GAAG,mCAAmC;QACvDqD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;MAGpCD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACtD,YAAY,CAAC;;EAE3E;EACQkE,sBAAsBA,CAAA;IAC5B,IAAI,CAACxD,KAAK,CAACwF,WAAW,CACnBxB,IAAI,CAAClH,SAAS,CAAC,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEyC,MAAM,IAAI;MACpB,IAAI,CAACrE,mBAAmB,GAAGqE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;IACpD,CAAC,CAAC;EACN;EACQ/B,uBAAuBA,CAAA;IAC7B,IAAI,CAACnC,MAAM,CAACmE,MAAM,CACf1B,IAAI,CACHnH,MAAM,CAAEwH,KAAK,IAAKA,KAAK,YAAY1H,aAAa,CAAC,EACjDG,SAAS,CAAC,IAAI,CAACmF,QAAQ,CAAC,CACzB,CACAe,SAAS,CAAC,MAAK;MACd,IAAI,CAACnB,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC9B,CAAC,CAAC;EACN;EACAjC,aAAaA,CAAA;IACX,IAAI,CAACgC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EACA8D,iBAAiBA,CAAA;IACf,IAAI,CAAC7D,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAvD,cAAcA,CAAA;IACZ,IAAI,CAACmD,YAAY,CAACnD,cAAc,EAAE;EACpC;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACG,WAAW,CAACH,MAAM,EAAE,CAACoE,SAAS,CAAC;MAClCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnB,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACD,WAAW,GAAG,KAAK;QACxB,IAAI,CAACE,WAAW,GAAG,IAAI;QACvB;QACA,IAAI,CAACzC,YAAY,GAAG,mCAAmC;QACvD;QACA,IAAI,CAACqC,WAAW,CAAC0B,iBAAiB,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC9B,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDzC,KAAK,EAAGC,GAAG,IAAI;QACbT,OAAO,CAACQ,KAAK,CAAC,eAAe,EAAEC,GAAG,CAAC;QACnC,IAAI,CAACrE,WAAW,CAAC8G,aAAa,EAAE;QAChC,IAAI,CAAC9D,WAAW,GAAG,IAAI;QACvB;QACA,IAAI,CAACzC,YAAY,GAAG,mCAAmC;QACvD;QACA,IAAI,CAACqC,WAAW,CAAC0B,iBAAiB,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC9B,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;EACAE,WAAWA,CAAA;IACT;IACA,IAAI,CAAC3D,aAAa,CAAC4D,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACxB,WAAW,EAAE,CAAC;IACtD,IAAI,CAACvC,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACgE,QAAQ,EAAE;EAC1B;;;uBA1RW5E,oBAAoB,EAAApE,EAAA,CAAAiJ,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAnJ,EAAA,CAAAiJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArJ,EAAA,CAAAiJ,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAtJ,EAAA,CAAAiJ,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAAxJ,EAAA,CAAAiJ,iBAAA,CAAAQ,EAAA,CAAAjF,cAAA,GAAAxE,EAAA,CAAAiJ,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA3J,EAAA,CAAAiJ,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAAiJ,iBAAA,CAAAjJ,EAAA,CAAA8J,iBAAA;IAAA;EAAA;;;YAApB1F,oBAAoB;MAAA2F,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAApBC,GAAA,CAAAhF,aAAA,EAAe;UAAA,UAAAnF,EAAA,CAAAoK,eAAA;;;;;;;;UCtB5BpK,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAE,SAAA,aAAmC;UAGnCF,EAAA,CAAAC,cAAA,aAA6C;UAQvCD,EAAA,CAAAE,SAAA,aAEO;UAKPF,EAAA,CAAAC,cAAA,aAA6C;UAEzCD,EAAA,CAAAqK,cAAA,EAMC;UANDrK,EAAA,CAAAC,cAAA,cAMC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UAOJF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAsK,eAAA,EAEC;UAFDtK,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EACX;UAGLH,EAAA,CAAAC,cAAA,eAA+C;UAK3CD,EAAA,CAAAa,UAAA,KAAA0J,kCAAA,gBAwBI;UAGJvK,EAAA,CAAAa,UAAA,KAAA2J,kCAAA,gBAuBI;UAGJxK,EAAA,CAAAa,UAAA,KAAA4J,kCAAA,gBAuBI;UAGJzK,EAAA,CAAAa,UAAA,KAAA6J,kCAAA,gBAuBI;UAGJ1K,EAAA,CAAAa,UAAA,KAAA8J,kCAAA,gBAuBI;UAEJ3K,EAAA,CAAAa,UAAA,KAAA+J,kCAAA,gBAuBI;UAEJ5K,EAAA,CAAAE,SAAA,eAEO;UAGPF,EAAA,CAAAa,UAAA,KAAAgK,kCAAA,gBA0BI;UACN7K,EAAA,CAAAG,YAAA,EAAM;UAOZH,EAAA,CAAAC,cAAA,kBAEC;UAIKD,EAAA,CAAAE,SAAA,eAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAkE;UAK5DD,EAAA,CAAAe,UAAA,mBAAA+J,uDAAA;YAAA,OAASX,GAAA,CAAAvH,aAAA,EAAe;UAAA,EAAC;UAIzB5C,EAAA,CAAAE,SAAA,eAEO;UACPF,EAAA,CAAAqK,cAAA,EAKC;UALDrK,EAAA,CAAAC,cAAA,eAKC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UACJF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAsK,eAAA,EAEG;UAFHtK,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EACN;UAIHH,EAAA,CAAAC,cAAA,aAAgE;UAK1DD,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,gCAAwB;UAAAJ,EAAA,CAAAG,YAAA,EAC1B;UAOLH,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAa,UAAA,KAAAkK,6CAAA,6BAgHe;UAGf/K,EAAA,CAAAa,UAAA,KAAAmK,4CAAA,kCAAAhL,EAAA,CAAAiL,sBAAA,CAsCc;UAChBjL,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAe,UAAA,mBAAAmK,oDAAA;YAAA,OAAAf,GAAA,CAAAvF,WAAA,IAAwBuF,GAAA,CAAAvH,aAAA,EAAe;UAAA,EAAC;UAGxC5C,EAAA,CAAAE,SAAA,eAEO;UAGPF,EAAA,CAAAC,cAAA,eAKC;UADCD,EAAA,CAAAe,UAAA,mBAAAoK,oDAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAElCrL,EAAA,CAAAC,cAAA,eAA2C;UAGvCD,EAAA,CAAAE,SAAA,eAEO;UAUTF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAEC;UAKKD,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,kCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,kBAIC;UAHCD,EAAA,CAAAe,UAAA,mBAAAuK,uDAAA;YAAA,OAASnB,GAAA,CAAAvH,aAAA,EAAe;UAAA,EAAC;UAIzB5C,EAAA,CAAAE,SAAA,eAEO;UAETF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAEC;UAECD,EAAA,CAAAa,UAAA,KAAA0K,kCAAA,gBA0EI;UAGJvL,EAAA,CAAAa,UAAA,KAAA2K,kCAAA,gBA0BI;UAEJxL,EAAA,CAAAE,SAAA,eAEO;UAEPF,EAAA,CAAAa,UAAA,KAAA4K,6CAAA,4BAgDe;UAEfzL,EAAA,CAAAa,UAAA,KAAA6K,6CAAA,4BA+Ce;UACjB1L,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,eAAkD;UAO5CD,EAAA,CAAAE,SAAA,eAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAa,UAAA,KAAA8K,oCAAA,kBAwBM;UAGN3L,EAAA,CAAAC,cAAA,eAAkE;UAChED,EAAA,CAAAE,SAAA,qBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM;;;;UAryBVH,EAAA,CAAA4L,WAAA,SAAA5L,EAAA,CAAAkC,WAAA,QAAAiI,GAAA,CAAAhI,WAAA,EAAkC;UA4DvBnC,EAAA,CAAAQ,SAAA,IAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UA2BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UAyBhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UA8BhC/B,EAAA,CAAAQ,SAAA,GAGD;UAHCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,QAAAoI,GAAA,CAAArF,WAAA,kBAAAqF,GAAA,CAAArF,WAAA,CAAA+G,IAAA,kBAAA1B,GAAA,CAAArF,WAAA,kBAAAqF,GAAA,CAAArF,WAAA,CAAA+G,IAAA,iBAGD;UAmGa7L,EAAA,CAAAQ,SAAA,IAAkC;UAAlCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAkC,aAAA+J,GAAA;UAkKvD9L,EAAA,CAAAQ,SAAA,GAA6B;UAA7BR,EAAA,CAAA4L,WAAA,YAAAzB,GAAA,CAAAvF,WAAA,CAA6B;UAW3B5E,EAAA,CAAAQ,SAAA,GAAmC;UAAnCR,EAAA,CAAA4L,WAAA,kBAAAzB,GAAA,CAAAvF,WAAA,CAAmC,uBAAAuF,GAAA,CAAAvF,WAAA;UAuDhC5E,EAAA,CAAAQ,SAAA,IAmCG;UAnCHR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA+L,eAAA,KAAAC,IAAA,EAAAhM,EAAA,CAAAM,eAAA,KAAA2L,GAAA,GAAAjM,EAAA,CAAAM,eAAA,KAAA4L,GAAA,GAAAlM,EAAA,CAAAM,eAAA,KAAA6L,GAAA,GAAAnM,EAAA,CAAAM,eAAA,KAAA8L,GAAA,GAAApM,EAAA,CAAAM,eAAA,KAAA+L,GAAA,GAAArM,EAAA,CAAAM,eAAA,KAAAgM,GAAA,GAAAtM,EAAA,CAAAgC,eAAA,KAAAuK,GAAA,EAAApC,GAAA,CAAAxJ,wBAAA,GAmCG;UAwCCX,EAAA,CAAAQ,SAAA,GAGD;UAHCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,QAAAoI,GAAA,CAAArF,WAAA,kBAAAqF,GAAA,CAAArF,WAAA,CAAA+G,IAAA,kBAAA1B,GAAA,CAAArF,WAAA,kBAAAqF,GAAA,CAAArF,WAAA,CAAA+G,IAAA,iBAGD;UA4Ba7L,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAAK,UAAA,UAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAiC;UAkDjC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAArI,WAAA,CAAAC,YAAA,GAAgC;UAuEhD/B,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAK,UAAA,SAAA8J,GAAA,CAAAhG,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}