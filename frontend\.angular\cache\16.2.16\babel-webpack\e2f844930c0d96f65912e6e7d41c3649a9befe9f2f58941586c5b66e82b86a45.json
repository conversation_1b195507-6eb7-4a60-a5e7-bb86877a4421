{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\", 34)(2, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r10.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 26);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 31);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"span\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 43);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 40);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      ctx_r26.selectedNotifications.clear();\n      ctx_r26.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r26.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 53);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 92);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      ctx_r43.getNotificationAttachments(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.joinConversation(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template, 1, 0, \"i\", 95);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template, 1, 0, \"i\", 96);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loading);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      ctx_r51.markAsRead(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66)(2, \"div\", 67)(3, \"label\", 34)(4, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.toggleSelection(notification_r34.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 68);\n    i0.ɵɵelement(7, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 70)(9, \"div\", 71)(10, \"div\", 72)(11, \"div\", 73)(12, \"span\", 74);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 75);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 76)(18, \"span\", 77);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 78);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 81);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 82);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 3, 3, \"button\", 83);\n    i0.ɵɵelementStart(26, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      ctx_r56.openNotificationDetails(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 86);\n    i0.ɵɵelementStart(29, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      ctx_r57.deleteNotification(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r34.isRead)(\"futuristic-notification-read\", notification_r34.isRead)(\"futuristic-notification-selected\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r34.senderId == null ? null : notification_r34.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r34.senderId == null ? null : notification_r34.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r34.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r34.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.type === \"NEW_MESSAGE\" || notification_r34.type === \"GROUP_INVITE\" || notification_r34.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"div\", 102);\n    i0.ɵɵelementStart(2, \"p\", 103);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62, 63);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const _r31 = i0.ɵɵreference(1);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onScroll(_r31));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 64);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const attachment_r61 = i0.ɵɵnextContext().$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r61.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r63.getFileIcon(attachment_r61.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r64.formatFileSize(attachment_r61.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 108);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 109);\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"div\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 112)(7, \"span\", 113);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 115)(11, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.downloadAttachment(attachment_r61));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = ctx.$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r61.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r60.getFileTypeLabel(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r61.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"strong\");\n    i0.ɵɵtext(2, \"Message original :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r74.currentNotification.message == null ? null : ctx_r74.currentNotification.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_36_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2, \"Lu le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r75.currentNotification.readAt, \"medium\"));\n  }\n}\nfunction NotificationListComponent_div_36_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"span\", 138);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵtext(3, \" Note : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 150);\n    i0.ɵɵtext(5, \" Ouvrir les d\\u00E9tails ne marque pas automatiquement comme lu \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const attachment_r81 = i0.ɵɵnextContext().$implicit;\n      const ctx_r85 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r85.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r81.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r83 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r83.getFileIcon(attachment_r81.type));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r84.formatFileSize(attachment_r81.size));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_div_37_div_5_div_1_Template, 2, 1, \"div\", 154);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_div_37_div_5_div_2_Template, 2, 2, \"div\", 155);\n    i0.ɵɵelementStart(3, \"div\", 156)(4, \"div\", 157);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 158)(7, \"span\", 159);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_36_div_37_div_5_span_9_Template, 2, 1, \"span\", 160);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 161)(11, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r91.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r93.downloadAttachment(attachment_r81));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = ctx.$implicit;\n    const ctx_r80 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r81.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r80.getFileTypeLabel(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r81.size);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 124)(1, \"h4\", 125);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 151);\n    i0.ɵɵtemplate(5, NotificationListComponent_div_36_div_37_div_5_Template, 15, 5, \"div\", 152);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pi\\u00E8ces jointes (\", ctx_r77.currentAttachments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r77.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 170);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r97);\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      ctx_r96.joinConversation(ctx_r96.currentNotification);\n      return i0.ɵɵresetView(ctx_r96.closeNotificationDetailsModal());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_button_39_i_1_Template, 1, 0, \"i\", 168);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_button_39_i_2_Template, 1, 0, \"i\", 169);\n    i0.ɵɵtext(3, \" Rejoindre la conversation \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.loading);\n  }\n}\nfunction NotificationListComponent_div_36_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r98.markAsRead(ctx_r98.currentNotification.id));\n    });\n    i0.ɵɵelement(1, \"i\", 173);\n    i0.ɵɵtext(2, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 124)(2, \"h4\", 125);\n    i0.ɵɵelement(3, \"i\", 126);\n    i0.ɵɵtext(4, \" Exp\\u00E9diteur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 127);\n    i0.ɵɵelement(6, \"img\", 128);\n    i0.ɵɵelementStart(7, \"div\", 129)(8, \"span\", 130);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 131);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 124)(14, \"h4\", 125);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵtext(16, \" Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 133);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, NotificationListComponent_div_36_div_19_Template, 4, 1, \"div\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 124)(21, \"h4\", 125);\n    i0.ɵɵelement(22, \"i\", 135);\n    i0.ɵɵtext(23, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 136)(25, \"div\", 137)(26, \"span\", 138);\n    i0.ɵɵtext(27, \"Type :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 139);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 137)(31, \"span\", 138);\n    i0.ɵɵtext(32, \"Statut :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 139);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, NotificationListComponent_div_36_div_35_Template, 6, 4, \"div\", 140);\n    i0.ɵɵtemplate(36, NotificationListComponent_div_36_div_36_Template, 6, 0, \"div\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, NotificationListComponent_div_36_div_37_Template, 6, 2, \"div\", 142);\n    i0.ɵɵelementStart(38, \"div\", 143);\n    i0.ɵɵtemplate(39, NotificationListComponent_div_36_button_39_Template, 4, 3, \"button\", 144);\n    i0.ɵɵtemplate(40, NotificationListComponent_div_36_button_40_Template, 3, 0, \"button\", 145);\n    i0.ɵɵelementStart(41, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      ctx_r100.deleteNotification(ctx_r100.currentNotification.id);\n      return i0.ɵɵresetView(ctx_r100.closeNotificationDetailsModal());\n    });\n    i0.ɵɵelement(42, \"i\", 146);\n    i0.ɵɵtext(43, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.username) || \"Syst\\u00E8me\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 16, ctx_r9.currentNotification.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.content, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.message == null ? null : ctx_r9.currentNotification.message.content);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r9.currentNotification.type);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"text-green-500\", ctx_r9.currentNotification.isRead)(\"text-orange-500\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.isRead ? \"Lu\" : \"Non lu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.readAt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentAttachments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.type === \"NEW_MESSAGE\" || ctx_r9.currentNotification.type === \"GROUP_INVITE\" || ctx_r9.currentNotification.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n  }\n}\nexport class NotificationListComponent {\n  constructor(messageService, themeService, router) {\n    this.messageService = messageService;\n    this.themeService = themeService;\n    this.router = router;\n    this.loading = true;\n    this.loadingMore = false;\n    this.hasMoreNotifications = true;\n    this.error = null;\n    this.showOnlyUnread = false;\n    this.isSoundMuted = false;\n    // Propriétés pour la sélection multiple\n    this.selectedNotifications = new Set();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n    // Propriétés pour le modal des pièces jointes\n    this.showAttachmentsModal = false;\n    this.loadingAttachments = false;\n    this.currentAttachments = [];\n    // Propriétés pour le modal des détails de notification\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.destroy$ = new Subject();\n    this.scrollPosition$ = new BehaviorSubject(0);\n    this.notifications$ = this.messageService.notifications$;\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n    this.unreadCount$ = this.messageService.notificationCount$;\n    this.isDarkMode$ = this.themeService.darkMode$;\n    // Vérifier l'état du son\n    this.isSoundMuted = this.messageService.isMuted();\n  }\n  /**\n   * Rejoint une conversation ou un groupe à partir d'une notification\n   * @param notification Notification contenant les informations de la conversation ou du groupe\n   */\n  joinConversation(notification) {\n    // Marquer la notification comme lue d'abord\n    this.markAsRead(notification.id);\n    // Extraire les informations pertinentes de la notification\n    const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n    const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n    // Déterminer où rediriger l'utilisateur\n    if (conversationId) {\n      this.router.navigate(['/messages/conversations/chat', conversationId]);\n    } else if (groupId) {\n      this.router.navigate(['/messages/group', groupId]);\n    } else if (notification.senderId && notification.senderId.id) {\n      this.loading = true;\n      this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n        next: conversation => {\n          this.loading = false;\n          if (conversation && conversation.id) {\n            this.router.navigate(['/messages/conversations/chat', conversation.id]);\n          } else {\n            this.router.navigate(['/messages']);\n          }\n        },\n        error: error => {\n          this.loading = false;\n          this.error = error;\n          this.router.navigate(['/messages']);\n        }\n      });\n    } else {\n      this.router.navigate(['/messages']);\n    }\n  }\n  onScroll(target) {\n    if (!target) return;\n    const scrollPosition = target.scrollTop;\n    const scrollHeight = target.scrollHeight;\n    const clientHeight = target.clientHeight;\n    // Si on est proche du bas (à 200px du bas)\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\n      this.scrollPosition$.next(scrollPosition);\n    }\n  }\n  ngOnInit() {\n    // Charger la préférence de son depuis le localStorage\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n    if (savedMutePreference !== null) {\n      this.isSoundMuted = savedMutePreference === 'true';\n      this.messageService.setMuted(this.isSoundMuted);\n    }\n    this.loadNotifications();\n    this.setupSubscriptions();\n    this.setupInfiniteScroll();\n    this.filterDeletedNotifications();\n  }\n  /**\n   * Filtre les notifications supprimées lors du chargement initial\n   */\n  filterDeletedNotifications() {\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    if (deletedNotificationIds.size > 0) {\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        this.messageService.notifications.next(filteredNotifications);\n        const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        this.updateNotificationCache(filteredNotifications);\n      });\n    }\n  }\n  setupInfiniteScroll() {\n    // Configurer le chargement des anciennes notifications lors du défilement\n    this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n    // Attendre 200ms après le dernier événement de défilement\n    distinctUntilChanged(),\n    // Ne déclencher que si la position de défilement a changé\n    filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n    ).subscribe(() => {\n      this.loadMoreNotifications();\n    });\n  }\n  loadNotifications() {\n    this.loading = true;\n    this.loadingMore = false;\n    this.error = null;\n    this.hasMoreNotifications = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n      if (deletedNotificationIds.size > 0) {\n        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        this.messageService.notifications.next(notifications);\n        const unreadCount = notifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        this.loading = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        this.error = err;\n        this.loading = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  loadMoreNotifications() {\n    if (this.loadingMore || !this.hasMoreNotifications) return;\n    this.loadingMore = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n      if (deletedNotificationIds.size > 0) {\n        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n          const allNotifications = [...existingNotifications, ...notifications];\n          this.messageService.notifications.next(allNotifications);\n          const unreadCount = allNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.updateNotificationCache(allNotifications);\n        });\n        this.loadingMore = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        this.loadingMore = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  setupSubscriptions() {\n    this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.log('Notification stream error:', error);\n      return of(null);\n    })).subscribe();\n    this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.log('Notifications read stream error:', error);\n      return of(null);\n    })).subscribe();\n  }\n  markAsRead(notificationId) {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const notification = notifications.find(n => n.id === notificationId);\n      if (notification) {\n        if (notification.isRead) return;\n        const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        this.updateUIWithNotifications(updatedNotifications);\n        this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            const revertedNotifications = notifications.map(n => n.id === notificationId ? {\n              ...n,\n              isRead: false,\n              readAt: undefined\n            } : n);\n            this.messageService.notifications.next(revertedNotifications);\n            const revertedUnreadCount = revertedNotifications.filter(n => !n.isRead).length;\n            this.messageService.notificationCount.next(revertedUnreadCount);\n          }\n        });\n      } else {\n        this.loadNotifications();\n      }\n    });\n  }\n  /**\n   * Met à jour l'interface utilisateur avec les nouvelles notifications\n   * @param notifications Notifications à afficher\n   */\n  updateUIWithNotifications(notifications) {\n    // Mettre à jour l'interface utilisateur immédiatement\n    this.messageService.notifications.next(notifications);\n    // Mettre à jour le compteur de notifications non lues\n    const unreadCount = notifications.filter(n => !n.isRead).length;\n    this.messageService.notificationCount.next(unreadCount);\n    // Mettre à jour le cache de notifications dans le service\n    this.updateNotificationCache(notifications);\n  }\n  /**\n   * Met à jour le cache de notifications dans le service\n   * @param notifications Notifications à mettre à jour\n   */\n  updateNotificationCache(notifications) {\n    notifications.forEach(notification => {\n      this.messageService.updateNotificationCache?.(notification);\n    });\n  }\n  /**\n   * Réinitialise la sélection des notifications\n   */\n  resetSelection() {\n    this.selectedNotifications.clear();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n  }\n  markAllAsRead() {\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n      if (unreadIds.length === 0) return;\n      const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n      if (validIds.length !== unreadIds.length) {\n        this.error = new Error('Invalid notification IDs');\n        return;\n      }\n      const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n        ...n,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : n);\n      this.updateUIWithNotifications(updatedNotifications);\n      this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('mark')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n        }\n      });\n    });\n  }\n  hasNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n  }\n  hasUnreadNotifications() {\n    return this.unreadCount$.pipe(map(count => count > 0));\n  }\n  /**\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\n   */\n  toggleUnreadFilter() {\n    this.showOnlyUnread = !this.showOnlyUnread;\n    if (this.showOnlyUnread) {\n      this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n    } else {\n      this.filteredNotifications$ = this.notifications$;\n    }\n  }\n  /**\n   * Active/désactive le son des notifications\n   */\n  toggleSound() {\n    this.isSoundMuted = !this.isSoundMuted;\n    this.messageService.setMuted(this.isSoundMuted);\n    if (!this.isSoundMuted) {\n      setTimeout(() => {\n        this.messageService.playNotificationSound();\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n        }, 1000);\n      }, 100);\n    }\n    localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n  }\n  /**\n   * Récupère les pièces jointes d'une notification et ouvre le modal\n   * @param notificationId ID de la notification\n   */\n  getNotificationAttachments(notificationId) {\n    if (!notificationId) return;\n    this.currentAttachments = [];\n    this.loadingAttachments = true;\n    this.showAttachmentsModal = true;\n    let notification;\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      notification = notifications.find(n => n.id === notificationId);\n    });\n    if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n      this.loadingAttachments = false;\n      this.currentAttachments = notification.message.attachments.map(attachment => ({\n        id: '',\n        url: attachment.url || '',\n        type: this.convertAttachmentTypeToMessageType(attachment.type),\n        name: attachment.name || '',\n        size: attachment.size || 0,\n        duration: 0\n      }));\n      return;\n    }\n    this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: attachments => {\n        this.loadingAttachments = false;\n        this.currentAttachments = attachments;\n      },\n      error: err => {\n        this.loadingAttachments = false;\n      }\n    });\n  }\n  /**\n   * Ferme le modal des pièces jointes\n   */\n  closeAttachmentsModal() {\n    this.showAttachmentsModal = false;\n  }\n  /**\n   * Ouvre le modal des détails de notification\n   * @param notification Notification à afficher\n   */\n  openNotificationDetails(notification) {\n    this.currentNotification = notification;\n    this.showNotificationDetailsModal = true;\n    if (notification.message?.attachments?.length) {\n      this.getNotificationAttachmentsForModal(notification.id);\n    }\n  }\n  /**\n   * Ferme le modal des détails de notification\n   */\n  closeNotificationDetailsModal() {\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.currentAttachments = [];\n  }\n  /**\n   * Récupère les pièces jointes d'une notification pour le modal de détails\n   */\n  getNotificationAttachmentsForModal(notificationId) {\n    this.currentAttachments = [];\n    if (this.currentNotification?.message?.attachments?.length) {\n      this.currentAttachments = this.currentNotification.message.attachments.map(attachment => ({\n        id: '',\n        url: attachment.url || '',\n        type: this.convertAttachmentTypeToMessageType(attachment.type),\n        name: attachment.name || '',\n        size: attachment.size || 0,\n        duration: 0\n      }));\n    }\n  }\n  /**\n   * Convertit AttachmentType en MessageType\n   */\n  convertAttachmentTypeToMessageType(type) {\n    switch (type) {\n      case 'IMAGE':\n        return MessageType.IMAGE;\n      case 'VIDEO':\n        return MessageType.VIDEO;\n      case 'AUDIO':\n        return MessageType.AUDIO;\n      case 'FILE':\n        return MessageType.FILE;\n      default:\n        return MessageType.FILE;\n    }\n  }\n  /**\n   * Vérifie si un type de fichier est une image\n   */\n  isImage(type) {\n    return type?.startsWith('image/') || false;\n  }\n  /**\n   * Obtient l'icône FontAwesome correspondant au type de fichier\n   * @param type Type MIME du fichier\n   * @returns Classe CSS de l'icône\n   */\n  getFileIcon(type) {\n    if (!type) return 'fas fa-file';\n    if (type.startsWith('image/')) return 'fas fa-file-image';\n    if (type.startsWith('video/')) return 'fas fa-file-video';\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\n    if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n    if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n    if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n    return 'fas fa-file';\n  }\n  /**\n   * Obtient le libellé du type de fichier\n   * @param type Type MIME du fichier\n   * @returns Libellé du type de fichier\n   */\n  getFileTypeLabel(type) {\n    if (!type) return 'Fichier';\n    if (type.startsWith('image/')) return 'Image';\n    if (type.startsWith('video/')) return 'Vidéo';\n    if (type.startsWith('audio/')) return 'Audio';\n    if (type.startsWith('text/')) return 'Texte';\n    if (type.includes('pdf')) return 'PDF';\n    if (type.includes('word') || type.includes('document')) return 'Document';\n    if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n    return 'Fichier';\n  }\n  /**\n   * Formate la taille du fichier en unités lisibles\n   * @param size Taille en octets\n   * @returns Taille formatée (ex: \"1.5 MB\")\n   */\n  formatFileSize(size) {\n    if (!size) return '';\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let i = 0;\n    let formattedSize = size;\n    while (formattedSize >= 1024 && i < units.length - 1) {\n      formattedSize /= 1024;\n      i++;\n    }\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\n  }\n  /**\n   * Ouvre une pièce jointe dans un nouvel onglet\n   * @param url URL de la pièce jointe\n   */\n  openAttachment(url) {\n    if (!url) return;\n    window.open(url, '_blank');\n  }\n  /**\n   * Télécharge une pièce jointe\n   * @param attachment Pièce jointe à télécharger\n   */\n  downloadAttachment(attachment) {\n    if (!attachment?.url) return;\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'attachment';\n    link.target = '_blank';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  acceptFriendRequest(notification) {\n    this.markAsRead(notification.id);\n  }\n  /**\n   * Supprime une notification et la stocke dans le localStorage\n   * @param notificationId ID de la notification à supprimer\n   */\n  deleteNotification(notificationId) {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    deletedNotificationIds.add(notificationId);\n    this.saveDeletedNotificationIds(deletedNotificationIds);\n    this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        if (result && result.success) {\n          if (this.error && this.error.message.includes('suppression')) {\n            this.error = null;\n          }\n        }\n      },\n      error: err => {\n        this.error = err;\n      }\n    });\n  }\n  /**\n   * Supprime toutes les notifications et les stocke dans le localStorage\n   */\n  deleteAllNotifications() {\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      notifications.forEach(notification => {\n        deletedNotificationIds.add(notification.id);\n      });\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          this.error = err;\n        }\n      });\n    });\n  }\n  getErrorMessage() {\n    return this.error?.message || 'Unknown error occurred';\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n      if (deletedIdsJson) {\n        return new Set(JSON.parse(deletedIdsJson));\n      }\n      return new Set();\n    } catch (error) {\n      return new Set();\n    }\n  }\n  /**\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\n   * @param deletedIds Set contenant les IDs des notifications supprimées\n   */\n  saveDeletedNotificationIds(deletedIds) {\n    try {\n      localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n    } catch (error) {\n      // Ignore silently\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Sélectionne ou désélectionne une notification\n   * @param notificationId ID de la notification\n   * @param event Événement de la case à cocher\n   */\n  toggleSelection(notificationId, event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n    // Mettre à jour l'état de sélection globale\n    this.updateSelectionState();\n    // Afficher ou masquer la barre de sélection\n    this.showSelectionBar = this.selectedNotifications.size > 0;\n  }\n  /**\n   * Sélectionne ou désélectionne toutes les notifications\n   * @param event Événement de la case à cocher\n   */\n  toggleSelectAll(event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    this.allSelected = !this.allSelected;\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      if (this.allSelected) {\n        // Sélectionner toutes les notifications\n        notifications.forEach(notification => {\n          this.selectedNotifications.add(notification.id);\n        });\n      } else {\n        // Désélectionner toutes les notifications\n        this.selectedNotifications.clear();\n      }\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    });\n  }\n  /**\n   * Met à jour l'état de sélection globale\n   */\n  updateSelectionState() {\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n    });\n  }\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications() {\n    if (this.selectedNotifications.size === 0) return;\n    const selectedIds = Array.from(this.selectedNotifications);\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n      this.updateUIWithNotifications(updatedNotifications);\n      this.resetSelection();\n    });\n    this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        // Success handled silently\n      },\n      error: err => {\n        // Error handled silently\n      }\n    });\n  }\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead() {\n    if (this.selectedNotifications.size === 0) return;\n    const selectedIds = Array.from(this.selectedNotifications);\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n        ...notification,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : notification);\n      this.updateUIWithNotifications(updatedNotifications);\n      this.resetSelection();\n    });\n    this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        // Success handled silently\n      },\n      error: err => {\n        // Error handled silently\n      }\n    });\n  }\n  /**\n   * Vérifie si une notification est sélectionnée\n   * @param notificationId ID de la notification\n   * @returns true si la notification est sélectionnée, false sinon\n   */\n  isSelected(notificationId) {\n    return this.selectedNotifications.has(notificationId);\n  }\n  static {\n    this.ɵfac = function NotificationListComponent_Factory(t) {\n      return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationListComponent,\n      selectors: [[\"app-notification-list\"]],\n      viewQuery: function NotificationListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n        }\n      },\n      hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n            return ctx.onScroll($event.target);\n          });\n        }\n      },\n      decls: 37,\n      vars: 22,\n      consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [\"class\", \"futuristic-modal-body\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails (ne marque PAS comme lu automatiquement)\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer cette notification comme lue\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [\"title\", \"Marquer cette notification comme lue\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"], [1, \"notification-detail-section\"], [1, \"notification-detail-title\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"notification-sender-info\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"notification-sender-avatar\", 3, \"src\"], [1, \"notification-sender-details\"], [1, \"notification-sender-name\"], [1, \"notification-timestamp\"], [1, \"fas\", \"fa-message\", \"mr-2\"], [1, \"notification-content-detail\"], [\"class\", \"notification-message-detail\", 4, \"ngIf\"], [1, \"fas\", \"fa-tag\", \"mr-2\"], [1, \"notification-info-grid\"], [1, \"notification-info-item\"], [1, \"notification-info-label\"], [1, \"notification-info-value\"], [\"class\", \"notification-info-item\", 4, \"ngIf\"], [\"class\", \"notification-info-item\", \"style\", \"\\n              background: rgba(255, 140, 0, 0.1);\\n              border: 1px solid rgba(255, 140, 0, 0.3);\\n            \", 4, \"ngIf\"], [\"class\", \"notification-detail-section\", 4, \"ngIf\"], [1, \"notification-detail-actions\"], [\"class\", \"futuristic-primary-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-secondary-button\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash-alt\", \"mr-2\"], [1, \"notification-message-detail\"], [1, \"notification-info-item\", 2, \"background\", \"rgba(255, 140, 0, 0.1)\", \"border\", \"1px solid rgba(255, 140, 0, 0.3)\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"notification-info-value\", 2, \"color\", \"#ff8c00\", \"font-style\", \"italic\"], [1, \"notification-attachments-grid\"], [\"class\", \"notification-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification-attachment-item\"], [\"class\", \"notification-attachment-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachment-icon\", 4, \"ngIf\"], [1, \"notification-attachment-info\"], [1, \"notification-attachment-name\"], [1, \"notification-attachment-meta\"], [1, \"notification-attachment-type\"], [\"class\", \"notification-attachment-size\", 4, \"ngIf\"], [1, \"notification-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"notification-attachment-button\", 3, \"click\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"notification-attachment-button\", 3, \"click\"], [1, \"notification-attachment-preview\"], [1, \"notification-attachment-icon\"], [1, \"notification-attachment-size\"], [1, \"futuristic-primary-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"futuristic-secondary-button\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"mr-2\"]],\n      template: function NotificationListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Notifications \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n          i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n          i0.ɵɵelement(20, \"i\", 16);\n          i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n          i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_28_listener() {\n            return ctx.closeNotificationDetailsModal();\n          });\n          i0.ɵɵelementStart(29, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_29_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"h3\", 15);\n          i0.ɵɵelement(32, \"i\", 21);\n          i0.ɵɵtext(33, \" D\\u00E9tails de la notification \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_34_listener() {\n            return ctx.closeNotificationDetailsModal();\n          });\n          i0.ɵɵelement(35, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, NotificationListComponent_div_36_Template, 44, 19, \"div\", 22);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 18, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 20, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"display\", ctx.showNotificationDetailsModal ? \"flex\" : \"none\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentNotification);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.futuristic-notifications-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  min-height: calc(100vh - 4rem);\\n  position: relative;\\n  overflow: hidden;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 1rem; \\n\\n  margin-bottom: 0;\\n  height: 100vh; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\\n  background-color: #edf1f4; \\n\\n  color: #6d6870;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\\n  background-color: #121212; \\n\\n  color: #a0a0a0;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.futuristic-notifications-container[_ngcontent-%COMP%]   .background-elements[_ngcontent-%COMP%] {\\n  position: absolute;\\n  inset: 0;\\n  overflow: hidden;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  opacity: 0.05;\\n  background-image: linear-gradient(to right, #4f5fad 1px, transparent 1px),\\n    linear-gradient(to bottom, #4f5fad 1px, transparent 1px);\\n  background-size: calc(100% / 12) 100%, 100% calc(100% / 12);\\n  z-index: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  opacity: 0.05; \\n\\n  background-image: linear-gradient(\\n      to right,\\n      rgba(255, 140, 0, 0.3) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(to bottom, rgba(255, 140, 0, 0.3) 1px, transparent 1px); \\n\\n  background-size: calc(100% / 20) 100%, 100% calc(100% / 20); \\n\\n  z-index: 0;\\n  animation: _ngcontent-%COMP%_grid-pulse 4s ease-in-out infinite; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(\\n    to right,\\n    transparent 0%,\\n    rgba(255, 140, 0, 0.5) 50%,\\n    transparent 100%\\n  );\\n  box-shadow: 0 0 10px rgba(255, 140, 0, 0.5);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_scan 8s linear infinite; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_grid-pulse {\\n  0% {\\n    opacity: 0.03;\\n  }\\n  50% {\\n    opacity: 0.07;\\n  }\\n  100% {\\n    opacity: 0.03;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_scan {\\n  0% {\\n    top: -10%;\\n    opacity: 0.5;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n  100% {\\n    top: 110%;\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n\\n.futuristic-notifications-card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  overflow: hidden;\\n  position: relative;\\n  z-index: 1;\\n  margin: 0.5rem auto; \\n\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  max-width: 1200px; \\n\\n  height: calc(100vh - 1rem); \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background-color: #1e1e1e; \\n\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  border: 1px solid rgba(109, 120, 201, 0.1); \\n\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n\\n\\n.futuristic-notifications-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n  margin-bottom: 1.5rem; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: rgba(240, 244, 248, 0.5);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\\n  background-color: rgba(0, 0, 0, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--accent-color);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  \\n\\n  position: relative;\\n  top: 0px; \\n\\n  \\n\\n  width: 22px;\\n  height: 22px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  \\n\\n  border-radius: 50%;\\n  background: rgba(0, 247, 255, 0.1);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: rgba(79, 95, 173, 0.1);\\n  border: 1px solid rgba(79, 95, 173, 0.3);\\n  box-shadow: 0 0 8px rgba(79, 95, 173, 0.3);\\n  color: #4f5fad;\\n}\\n\\n\\n\\n.futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.5);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 12px rgba(79, 95, 173, 0.5);\\n}\\n\\n\\n\\n.futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 2rem;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 2px solid transparent;\\n  border-top-color: var(--accent-color);\\n  border-bottom-color: var(--secondary-color);\\n  animation: _ngcontent-%COMP%_futuristic-spin 1.2s linear infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_futuristic-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  text-align: center;\\n}\\n\\n\\n\\n.futuristic-error-message[_ngcontent-%COMP%] {\\n  margin: 1rem;\\n  padding: 1rem;\\n  background-color: rgba(255, 0, 76, 0.1);\\n  border-left: 4px solid var(--error-color);\\n  border-radius: var(--border-radius-md);\\n}\\n\\n.futuristic-error-icon[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 1.25rem;\\n  margin-right: 0.75rem;\\n}\\n\\n.futuristic-error-title[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n\\n.futuristic-error-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  background-color: rgba(255, 0, 76, 0.1);\\n  color: var(--error-color);\\n  border: 1px solid var(--error-color);\\n  border-radius: var(--border-radius-sm);\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 0, 76, 0.2);\\n  box-shadow: 0 0 10px rgba(255, 0, 76, 0.3);\\n}\\n\\n\\n\\n.futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 2rem;\\n  text-align: center;\\n}\\n\\n.futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: var(--accent-color);\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n\\n.futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%] {\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow-y: auto;\\n  position: relative;\\n  scrollbar-width: thin;\\n  z-index: 1;\\n  width: 100%;\\n}\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\\n  scrollbar-color: #4f5fad transparent;\\n  background-color: white;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #4f5fad;\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\\n  scrollbar-color: var(--accent-color) transparent;\\n  background-color: transparent;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.futuristic-notification-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 30px 20px 16px 20px; \\n\\n  position: relative;\\n  transition: all 0.2s ease;\\n  margin: 0.5rem 1rem; \\n\\n  border-radius: 8px; \\n\\n  flex-wrap: nowrap; \\n\\n  justify-content: space-between; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: white;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  border-radius: 15px; \\n\\n  transition: all 0.3s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.1),\\n    rgba(61, 74, 133, 0.2)\\n  ); \\n\\n  border: 1px solid rgba(79, 95, 173, 0.3); \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); \\n\\n  border-bottom-right-radius: 0; \\n\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: inherit;\\n  pointer-events: none;\\n  z-index: -1;\\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2); \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.15),\\n    rgba(61, 74, 133, 0.25)\\n  ); \\n\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2); \\n\\n}\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n  background-color: var(\\n    --dark-medium-bg,\\n    #252740\\n  ); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.1); \\n\\n  border-radius: 15px; \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \\n\\n  margin-bottom: 15px; \\n\\n  margin-left: 15px; \\n\\n  margin-right: 15px; \\n\\n  transition: all 0.3s ease; \\n\\n  color: var(\\n    --text-light,\\n    #ffffff\\n  ); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(\\n    -2px\\n  ); \\n\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  background: linear-gradient(\\n    135deg,\\n    #00f7ff20,\\n    #00c3ff30\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \\n\\n  border-bottom-right-radius: 0; \\n\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: inherit;\\n  pointer-events: none;\\n  z-index: -1;\\n  box-shadow: inset 0 0 0 1px rgba(0, 247, 255, 0.3); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.2); \\n\\n  background: linear-gradient(\\n    135deg,\\n    #00f7ff30,\\n    #00c3ff40\\n  ); \\n\\n}\\n\\n\\n\\n.futuristic-notification-unread[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_borderFlow {\\n  0% {\\n    background-position: 0% 0%;\\n  }\\n  100% {\\n    background-position: 200% 0%;\\n  }\\n}\\n\\n\\n\\n.futuristic-modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n  opacity: 0; \\n\\n  transition: opacity 0.3s ease;\\n}\\n\\n\\n\\n.futuristic-modal-overlay[style*=\\\"display: flex\\\"][_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.8);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  animation: _ngcontent-%COMP%_modalBackdropFadeIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalBackdropFadeIn {\\n  from {\\n    background-color: rgba(0, 0, 0, 0);\\n    -webkit-backdrop-filter: blur(0px);\\n            backdrop-filter: blur(0px);\\n  }\\n  to {\\n    background-color: rgba(0, 0, 0, 0.8);\\n    -webkit-backdrop-filter: blur(8px);\\n            backdrop-filter: blur(8px);\\n  }\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\\n  width: 90%;\\n  max-width: 600px;\\n  max-height: 80vh;\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  flex-direction: column;\\n  animation: _ngcontent-%COMP%_scaleIn 0.3s ease;\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  position: relative;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(79, 95, 173, 0.2) 20%,\\n    rgba(79, 95, 173, 0.8) 50%,\\n    rgba(79, 95, 173, 0.2) 80%,\\n    transparent 100%\\n  );\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.4);\\n  z-index: 1;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\\n  width: 90%;\\n  max-width: 600px;\\n  max-height: 80vh;\\n  background-color: rgba(18, 18, 18, 0.95);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 0 40px rgba(0, 247, 255, 0.4),\\n    inset 0 0 20px rgba(0, 247, 255, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.19, 1, 0.22, 1);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  position: relative;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(0, 247, 255, 0.2) 20%,\\n    rgba(0, 247, 255, 0.8) 50%,\\n    rgba(0, 247, 255, 0.2) 80%,\\n    transparent 100%\\n  );\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n  z-index: 1;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\\n  background-color: rgba(79, 95, 173, 0.05);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\\n  background-color: rgba(0, 0, 0, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #00f7ff;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: #00f7ff;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.futuristic-modal-body[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  overflow-y: auto;\\n  max-height: calc(80vh - 70px);\\n}\\n\\n\\n\\n.futuristic-attachments-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 8px;\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #4f5fad;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 8px;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #00f7ff;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: #00f7ff;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n\\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.futuristic-attachment-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.futuristic-attachment-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.futuristic-attachment-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: var(--text-dim);\\n}\\n\\n.futuristic-attachment-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: 12px;\\n}\\n\\n\\n\\n.futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 3px solid transparent;\\n  margin-bottom: 16px;\\n  animation: _ngcontent-%COMP%_spin 1.2s linear infinite;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  border-top-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  border-top-color: #00f7ff;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-dim);\\n}\\n\\n.futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n  text-align: center;\\n}\\n\\n.futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n}\\n\\n.futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n.futuristic-empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-dim);\\n  max-width: 300px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.notification-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  flex-shrink: 0;\\n  margin-right: 12px;\\n  margin-left: 10px; \\n\\n}\\n\\n.notification-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n\\n\\n.notification-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  padding-right: 16px;\\n}\\n\\n.notification-header[_ngcontent-%COMP%] {\\n  margin-bottom: 6px; \\n\\n}\\n\\n.notification-header-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between; \\n\\n  align-items: center; \\n\\n  width: 100%; \\n\\n}\\n\\n.notification-sender[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.95rem; \\n\\n  color: #4f5fad;\\n  padding: 2px 0; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.notification-sender[_ngcontent-%COMP%]:hover {\\n  color: #3d4a85; \\n\\n  text-shadow: 0 0 1px rgba(79, 95, 173, 0.3); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%] {\\n  color: #ff8c00; \\n\\n  text-shadow: 0 0 5px rgba(255, 140, 0, 0.3); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover {\\n  color: #ffa040; \\n\\n  text-shadow: 0 0 8px rgba(255, 140, 0, 0.5); \\n\\n}\\n\\n.notification-text[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 0.9rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: #ffffff; \\n\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: var(\\n    --light-text,\\n    #333333\\n  ); \\n\\n  font-weight: 400; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\\n  color: var(\\n    --dark-text,\\n    #ffffff\\n  ); \\n\\n  font-weight: 400; \\n\\n}\\n\\n.notification-message-preview[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #666;\\n  margin-top: 4px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 100%;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%] {\\n  color: #cccccc; \\n\\n}\\n\\n\\n\\n.notification-attachments-indicator[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #ff8c00;\\n  margin-top: 0.25rem;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%] {\\n  color: rgba(0, 247, 255, 0.9);\\n}\\n\\n.notification-attachments-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n\\n\\n\\n\\n\\n.notification-action-button[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n  margin: 6px; \\n\\n  border: none;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.notification-time[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px; \\n\\n  right: 10px; \\n\\n  font-size: 0.75rem; \\n\\n  color: rgba(0, 247, 255, 0.9);\\n  font-weight: 600;\\n  padding: 5px 10px; \\n\\n  border-radius: 8px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.15),\\n    rgba(0, 200, 255, 0.1)\\n  );\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: 0 2px 8px rgba(0, 247, 255, 0.2);\\n  z-index: 15;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n  \\n\\n  letter-spacing: 0.5px;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  \\n\\n  color: #00ffff !important; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 255, 0.4),\\n    rgba(0, 255, 200, 0.35),\\n    rgba(0, 200, 255, 0.3)\\n  );\\n  border: 1px solid rgba(0, 255, 255, 0.8);\\n  \\n\\n  text-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6),\\n    0 0 15px rgba(0, 255, 255, 0.4);\\n  box-shadow: 0 2px 10px rgba(0, 255, 255, 0.6), 0 0 20px rgba(0, 255, 255, 0.3),\\n    inset 0 0 10px rgba(0, 255, 255, 0.2);\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none; \\n\\n  \\n\\n  animation: _ngcontent-%COMP%_fluoro-pulse 2s ease-in-out infinite alternate;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.1),\\n    rgba(79, 95, 173, 0.05)\\n  );\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  box-shadow: 0 2px 8px rgba(79, 95, 173, 0.15);\\n}\\n\\n\\n\\n.notification-time[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\\n  \\n\\n  color: #ffffff !important; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 255, 0.6),\\n    rgba(0, 255, 200, 0.5),\\n    rgba(0, 200, 255, 0.45)\\n  );\\n  border-color: rgba(0, 255, 255, 1); \\n\\n  \\n\\n  text-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.9),\\n    0 0 25px rgba(0, 255, 255, 0.7);\\n  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.8), 0 0 30px rgba(0, 255, 255, 0.5),\\n    inset 0 0 15px rgba(0, 255, 255, 0.3);\\n  \\n\\n  animation: _ngcontent-%COMP%_fluoro-pulse-intense 1s ease-in-out infinite alternate;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.15),\\n    rgba(79, 95, 173, 0.1)\\n  );\\n  box-shadow: 0 4px 15px rgba(79, 95, 173, 0.25);\\n}\\n\\n\\n\\n.notification-join-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, #00c853, #00a843);\\n  color: white;\\n  border: 2px solid #00c853;\\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\\n  position: relative;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #00e676, #00c853);\\n  transform: scale(1.15) rotate(5deg);\\n  box-shadow: 0 0 15px rgba(0, 200, 83, 0.6);\\n  border-color: #00e676;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, #00c853, #00e676);\\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n\\n\\n.notification-details-button[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 38px;\\n  background: linear-gradient(135deg, #2196f3, #1976d2);\\n  color: white;\\n  border: 2px solid #2196f3;\\n  border-radius: 8px;\\n  position: relative;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #42a5f5, #2196f3);\\n  transform: scale(1.1) rotateY(15deg);\\n  box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);\\n  border-color: #42a5f5;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 2px;\\n  left: 2px;\\n  right: 2px;\\n  bottom: 2px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  transition: all 0.3s ease;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n\\n\\n.notification-read-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(135deg, #ffc107, #ff9800);\\n  color: white;\\n  border: 2px solid #ffc107;\\n  border-radius: 50%;\\n  transform: rotate(45deg);\\n  position: relative;\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: rotate(-45deg);\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ffca28, #ffc107);\\n  transform: rotate(45deg) scale(1.15);\\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.6);\\n  border-color: #ffca28;\\n}\\n\\n.notification-read-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 8px;\\n  height: 8px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%) rotate(-45deg);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.notification-delete-button[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 38px;\\n  background: linear-gradient(135deg, #f44336, #d32f2f);\\n  color: white;\\n  border: 2px solid #f44336;\\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\\n  position: relative;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ef5350, #f44336);\\n  transform: scale(1.15) rotate(-5deg);\\n  box-shadow: 0 0 15px rgba(244, 67, 54, 0.6);\\n  border-color: #ef5350;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, #f44336, #ef5350);\\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n\\n\\n.notification-attachment-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\\n  color: white;\\n  border: 2px solid #9c27b0;\\n  clip-path: polygon(\\n    30% 0%,\\n    70% 0%,\\n    100% 30%,\\n    100% 70%,\\n    70% 100%,\\n    30% 100%,\\n    0% 70%,\\n    0% 30%\\n  );\\n  position: relative;\\n}\\n\\n.notification-attachment-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ab47bc, #9c27b0);\\n  transform: scale(1.1) rotate(10deg);\\n  box-shadow: 0 0 15px rgba(156, 39, 176, 0.6);\\n  border-color: #ab47bc;\\n}\\n\\n.notification-attachment-button[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 6px;\\n  height: 6px;\\n  background: rgba(255, 255, 255, 0.4);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_bounce 1.5s infinite;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00c853, #00a843);\\n  border-color: #00e676;\\n  box-shadow: 0 0 10px rgba(0, 200, 83, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3, #1976d2);\\n  border-color: #42a5f5;\\n  box-shadow: 0 0 10px rgba(33, 150, 243, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107, #ff9800);\\n  border-color: #ffca28;\\n  box-shadow: 0 0 10px rgba(255, 193, 7, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336, #d32f2f);\\n  border-color: #ef5350;\\n  box-shadow: 0 0 10px rgba(244, 67, 54, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\\n  border-color: #ab47bc;\\n  box-shadow: 0 0 10px rgba(156, 39, 176, 0.4);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%,\\n  100% {\\n    opacity: 1;\\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1);\\n  }\\n  50% {\\n    opacity: 0.5;\\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1.2);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%,\\n  100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.3);\\n  }\\n}\\n\\n\\n\\n.notification-join-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\uD83D\\uDCAC\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\u2139\\uFE0F\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n.notification-delete-button[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\uD83D\\uDDD1\\uFE0F\\\";\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  font-size: 10px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  width: 16px;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n  z-index: 10;\\n}\\n\\n\\n\\n.futuristic-checkbox[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 22px; \\n\\n  height: 22px; \\n\\n  cursor: pointer;\\n  transition: all 0.2s ease; \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 22px; \\n\\n  width: 22px; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.1),\\n    rgba(0, 200, 255, 0.1)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  border-radius: 50%; \\n\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 10px rgba(0, 255, 200, 0.4); \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \\n\\n  \\n\\n  position: relative;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_glow-pulse {\\n  0% {\\n    box-shadow: 0 0 8px rgba(0, 255, 200, 0.3);\\n  }\\n  100% {\\n    box-shadow: 0 0 15px rgba(0, 200, 255, 0.6);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.4); \\n\\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.2\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(\\n    --glow-effect\\n  ); \\n\\n  transform: scale(\\n    1.05\\n  ); \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.2\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(\\n    --glow-effect\\n  ); \\n\\n  transform: scale(\\n    1.05\\n  ); \\n\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.8),\\n    rgba(0, 200, 255, 0.8)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \\n\\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkbox-glow {\\n  0% {\\n    box-shadow: 0 0 15px rgba(0, 255, 200, 0.6);\\n    transform: scale(1);\\n  }\\n  100% {\\n    box-shadow: 0 0 25px rgba(0, 200, 255, 0.9);\\n    transform: scale(1.15);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkbox-pulse {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);\\n  }\\n  100% {\\n    transform: scale(1.15);\\n    box-shadow: 0 0 20px rgba(0, 247, 255, 0.8);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 255, 200, 0.8),\\n    rgba(0, 200, 255, 0.8)\\n  ); \\n\\n  border: 2px solid transparent; \\n\\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \\n\\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \\n\\n}\\n\\n.checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n}\\n\\n.futuristic-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 7px; \\n\\n  top: 3px; \\n\\n  width: 6px; \\n\\n  height: 12px; \\n\\n  border: solid white;\\n  border-width: 0 2px 2px 0; \\n\\n  transform: rotate(45deg);\\n  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8); \\n\\n  animation: _ngcontent-%COMP%_pulse-check 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse-check {\\n  0% {\\n    opacity: 0.8;\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    box-shadow: 0 0 10px rgba(255, 255, 255, 1);\\n  }\\n}\\n\\n\\n\\n.select-all-checkbox[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 5px;\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%; \\n\\n  background: rgba(\\n    0,\\n    247,\\n    255,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(0, 247, 255, 0.3); \\n\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.4); \\n\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.2);\\n  box-shadow: var(--glow-effect);\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 13px; \\n\\n  top: 7px; \\n\\n  width: 8px; \\n\\n  height: 16px; \\n\\n}\\n\\n\\n\\n.selection-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: rgba(255, 140, 0, 0.1);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 140, 0, 0.1);\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\\n}\\n\\n.selection-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 15px;\\n  color: #ff8c00;\\n}\\n\\n.dark[_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  color: rgba(255, 140, 0, 0.9);\\n}\\n\\n\\n\\n\\n\\n.futuristic-notification-selected[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 140, 0, 0.5) !important;\\n  background-color: rgba(255, 140, 0, 0.05) !important;\\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.1) !important;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 140, 0, 0.3) !important; \\n\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.15),\\n    rgba(255, 0, 128, 0.15),\\n    rgba(128, 0, 255, 0.15)\\n  ) !important; \\n\\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.2),\\n    inset 0 0 20px rgba(255, 0, 128, 0.1) !important; \\n\\n  transform: translateY(-2px);\\n  padding: 18px 22px !important; \\n\\n  margin-bottom: 18px !important; \\n\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(\\n    circle,\\n    rgba(255, 140, 0, 0.1) 0%,\\n    transparent 70%\\n  );\\n  animation: _ngcontent-%COMP%_rotate-gradient 8s linear infinite;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background-image: radial-gradient(\\n      circle at 10% 10%,\\n      rgba(255, 255, 255, 0.8) 0%,\\n      rgba(255, 255, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 20% 30%,\\n      rgba(255, 140, 0, 0.8) 0%,\\n      rgba(255, 140, 0, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 30% 70%,\\n      rgba(255, 0, 128, 0.8) 0%,\\n      rgba(255, 0, 128, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 70% 40%,\\n      rgba(128, 0, 255, 0.8) 0%,\\n      rgba(128, 0, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 80% 80%,\\n      rgba(255, 255, 255, 0.8) 0%,\\n      rgba(255, 255, 255, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 90% 10%,\\n      rgba(255, 140, 0, 0.8) 0%,\\n      rgba(255, 140, 0, 0) 2%\\n    ),\\n    radial-gradient(\\n      circle at 50% 50%,\\n      rgba(255, 0, 128, 0.8) 0%,\\n      rgba(255, 0, 128, 0) 2%\\n    );\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_sparkle-effect 4s ease-in-out infinite;\\n  pointer-events: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle-effect {\\n  0% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate-gradient {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%] {\\n  color: rgba(\\n    255,\\n    255,\\n    255,\\n    0.9\\n  ) !important; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    0,\\n    0,\\n    0,\\n    0.5\\n  ) !important; \\n\\n  color: rgba(255, 255, 255, 0.9) !important; \\n\\n  border-left: 2px solid rgba(255, 140, 0, 0.5) !important; \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%] {\\n  color: #ff8c00 !important; \\n\\n  font-weight: 600;\\n}\\n\\n\\n\\n\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.2),\\n    rgba(255, 94, 98, 0.2)\\n  );\\n  color: #ff8c00; \\n\\n  border: 1px solid rgba(255, 140, 0, 0.4);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(255, 140, 0, 0.3),\\n    rgba(255, 94, 98, 0.3)\\n  );\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.1\\n  ); \\n\\n  border: 1px solid rgba(255, 140, 0, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.2\\n  ); \\n\\n  color: #ff8c00; \\n\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.4); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.1\\n  ); \\n\\n  color: #ff8c00; \\n\\n  border: 1px solid rgba(255, 140, 0, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(\\n    255,\\n    140,\\n    0,\\n    0.2\\n  ); \\n\\n  color: #ff8c00; \\n\\n  box-shadow: 0 0 8px rgba(255, 140, 0, 0.4); \\n\\n}\\n\\n\\n\\n.notification-separator-dot[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: rgba(\\n    0,\\n    247,\\n    255,\\n    0.6\\n  ); \\n\\n  margin: 0 8px;\\n  box-shadow: 0 0 5px rgba(0, 247, 255, 0.4); \\n\\n  animation: _ngcontent-%COMP%_dot-pulse 2s infinite alternate; \\n\\n  transition: all 0.5s ease; \\n\\n}\\n\\n\\n\\n.notification-separator-dot.fade-out[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0);\\n  width: 0;\\n  margin: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%] {\\n  background-color: rgba(\\n    0,\\n    247,\\n    255,\\n    0.8\\n  ); \\n\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.6); \\n\\n  animation: _ngcontent-%COMP%_dot-pulse-selected 1.5s infinite alternate; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse-selected {\\n  0% {\\n    opacity: 0.6;\\n    transform: scale(1);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1.5);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse {\\n  0% {\\n    opacity: 0.4;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1.2);\\n  }\\n}\\n\\n\\n\\n.notification-checkbox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px; \\n\\n  left: 10px; \\n\\n  z-index: 10; \\n\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n\\n.futuristic-cancel-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(150, 150, 150, 0.2);\\n  color: #6d6870;\\n  border: none;\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 8px rgba(150, 150, 150, 0.2);\\n}\\n\\n.futuristic-cancel-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(150, 150, 150, 0.3);\\n  box-shadow: 0 0 12px rgba(150, 150, 150, 0.3);\\n  transform: translateY(-2px);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%] {\\n  background-color: rgba(100, 100, 100, 0.2);\\n  color: #e0e0e0;\\n  box-shadow: 0 0 8px rgba(100, 100, 100, 0.2);\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n.notification-detail-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 12px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.notification-detail-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin-bottom: 0.75rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n  text-shadow: 0 0 6px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.notification-sender-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n\\n.notification-sender-avatar[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%] {\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.notification-sender-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.notification-sender-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4f5fad;\\n  font-size: 1rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%] {\\n  color: #00f7ff;\\n}\\n\\n.notification-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #a0a0a0;\\n}\\n\\n\\n\\n.notification-content-detail[_ngcontent-%COMP%] {\\n  background: rgba(79, 95, 173, 0.1);\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  color: #333;\\n  line-height: 1.5;\\n  border-left: 3px solid #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n  color: #e0e0e0;\\n  border-left: 3px solid #00f7ff;\\n}\\n\\n.notification-message-detail[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.75rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 8px;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.1);\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.notification-info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 0.5rem;\\n}\\n\\n.notification-info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.5rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 6px;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.notification-info-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n\\n.notification-info-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.notification-attachments-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 0.75rem;\\n}\\n\\n.notification-attachment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.75rem;\\n  background: rgba(79, 95, 173, 0.05);\\n  border-radius: 8px;\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.notification-attachment-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(79, 95, 173, 0.1);\\n  border-color: rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 247, 255, 0.1);\\n  border-color: rgba(0, 247, 255, 0.3);\\n}\\n\\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  object-fit: cover;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n}\\n\\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.notification-attachment-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(79, 95, 173, 0.2);\\n  border-radius: 6px;\\n  font-size: 1.5rem;\\n  color: #4f5fad;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%] {\\n  background: rgba(0, 247, 255, 0.2);\\n  color: #00f7ff;\\n}\\n\\n.notification-attachment-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.notification-attachment-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n.notification-attachment-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.dark[_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n\\n.notification-attachment-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fluoro-pulse {\\n  0% {\\n    text-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6),\\n      0 0 15px rgba(0, 255, 255, 0.4);\\n    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.6),\\n      0 0 20px rgba(0, 255, 255, 0.3), inset 0 0 10px rgba(0, 255, 255, 0.2);\\n    border-color: rgba(0, 255, 255, 0.8);\\n  }\\n  100% {\\n    text-shadow: 0 0 8px rgba(0, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.8),\\n      0 0 25px rgba(0, 255, 255, 0.6);\\n    box-shadow: 0 2px 15px rgba(0, 255, 255, 0.8),\\n      0 0 30px rgba(0, 255, 255, 0.5), inset 0 0 15px rgba(0, 255, 255, 0.3);\\n    border-color: rgba(0, 255, 255, 1);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fluoro-pulse-intense {\\n  0% {\\n    text-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.9),\\n      0 0 25px rgba(0, 255, 255, 0.7);\\n    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.8),\\n      0 0 30px rgba(0, 255, 255, 0.5), inset 0 0 15px rgba(0, 255, 255, 0.3);\\n    transform: translateY(-2px) scale(1.05);\\n  }\\n  100% {\\n    text-shadow: 0 0 12px rgba(255, 255, 255, 1), 0 0 20px rgba(0, 255, 255, 1),\\n      0 0 35px rgba(0, 255, 255, 0.9);\\n    box-shadow: 0 6px 25px rgba(0, 255, 255, 1), 0 0 40px rgba(0, 255, 255, 0.7),\\n      inset 0 0 20px rgba(0, 255, 255, 0.4);\\n    transform: translateY(-3px) scale(1.08);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "of", "BehaviorSubject", "MessageType", "catchError", "map", "takeUntil", "take", "debounceTime", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationListComponent_div_8_div_3_Template_input_click_2_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r10", "allSelected", "NotificationListComponent_div_8_button_9_Template_button_click_0_listener", "_r16", "ctx_r15", "markAllAsRead", "ɵɵtext", "NotificationListComponent_div_8_button_11_Template_button_click_0_listener", "_r18", "ctx_r17", "deleteAllNotifications", "NotificationListComponent_div_8_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNotifications", "ɵɵtemplate", "NotificationListComponent_div_8_div_3_Template", "NotificationListComponent_div_8_Template_button_click_5_listener", "ctx_r21", "toggleUn<PERSON><PERSON><PERSON>er", "NotificationListComponent_div_8_Template_button_click_7_listener", "ctx_r22", "toggleSound", "NotificationListComponent_div_8_button_9_Template", "NotificationListComponent_div_8_button_11_Template", "ɵɵpipeBind1", "ctx_r0", "hasNotifications", "ɵɵclassProp", "showOnlyUnread", "isSoundMuted", "ɵɵpropertyInterpolate", "unreadCount$", "NotificationListComponent_div_9_Template_button_click_3_listener", "_r24", "ctx_r23", "markSelectedAsRead", "NotificationListComponent_div_9_Template_button_click_6_listener", "ctx_r25", "deleteSelectedNotifications", "NotificationListComponent_div_9_Template_button_click_9_listener", "ctx_r26", "selectedNotifications", "clear", "showSelectionBar", "ɵɵtextInterpolate1", "ctx_r1", "size", "NotificationListComponent_div_11_Template_button_click_8_listener", "_r28", "ctx_r27", "ɵɵtextInterpolate", "ctx_r3", "getErrorMessage", "NotificationListComponent_div_12_Template_button_click_7_listener", "_r30", "ctx_r29", "notification_r34", "message", "content", "attachments", "length", "NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener", "_r45", "$implicit", "ctx_r43", "getNotificationAttachments", "id", "stopPropagation", "NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener", "_r50", "ctx_r48", "joinConversation", "NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template", "NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template", "ctx_r39", "loading", "NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener", "_r53", "ctx_r51", "mark<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener", "restoredCtx", "_r55", "ctx_r54", "toggleSelection", "NotificationListComponent_div_14_ng_container_2_div_20_Template", "NotificationListComponent_div_14_ng_container_2_div_21_Template", "NotificationListComponent_div_14_ng_container_2_div_22_Template", "NotificationListComponent_div_14_ng_container_2_button_24_Template", "NotificationListComponent_div_14_ng_container_2_button_25_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener", "ctx_r56", "openNotificationDetails", "NotificationListComponent_div_14_ng_container_2_button_28_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener", "ctx_r57", "deleteNotification", "ɵɵelementContainerEnd", "isRead", "ctx_r32", "isSelected", "senderId", "image", "ɵɵsanitizeUrl", "username", "ɵɵpipeBind2", "timestamp", "type", "NotificationListComponent_div_14_Template_div_scroll_0_listener", "_r59", "_r31", "ɵɵreference", "ctx_r58", "onScroll", "NotificationListComponent_div_14_ng_container_2_Template", "NotificationListComponent_div_14_div_4_Template", "ctx_r5", "filteredNotifications$", "loadingMore", "NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener", "_r67", "attachment_r61", "ctx_r65", "openAttachment", "url", "ɵɵclassMap", "ctx_r63", "getFileIcon", "ctx_r64", "formatFileSize", "NotificationListComponent_div_27_div_1_div_1_Template", "NotificationListComponent_div_27_div_1_div_2_Template", "NotificationListComponent_div_27_div_1_span_9_Template", "NotificationListComponent_div_27_div_1_Template_button_click_11_listener", "_r72", "ctx_r71", "NotificationListComponent_div_27_div_1_Template_button_click_13_listener", "ctx_r73", "downloadAttachment", "ctx_r60", "isImage", "name", "getFileTypeLabel", "NotificationListComponent_div_27_div_1_Template", "ctx_r8", "currentAttachments", "ctx_r74", "currentNotification", "ctx_r75", "readAt", "NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener", "_r87", "attachment_r81", "ctx_r85", "ctx_r83", "ctx_r84", "NotificationListComponent_div_36_div_37_div_5_div_1_Template", "NotificationListComponent_div_36_div_37_div_5_div_2_Template", "NotificationListComponent_div_36_div_37_div_5_span_9_Template", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener", "_r92", "ctx_r91", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener", "ctx_r93", "ctx_r80", "NotificationListComponent_div_36_div_37_div_5_Template", "ctx_r77", "NotificationListComponent_div_36_button_39_Template_button_click_0_listener", "_r97", "ctx_r96", "closeNotificationDetailsModal", "NotificationListComponent_div_36_button_39_i_1_Template", "NotificationListComponent_div_36_button_39_i_2_Template", "ctx_r78", "NotificationListComponent_div_36_button_40_Template_button_click_0_listener", "_r99", "ctx_r98", "NotificationListComponent_div_36_div_19_Template", "NotificationListComponent_div_36_div_35_Template", "NotificationListComponent_div_36_div_36_Template", "NotificationListComponent_div_36_div_37_Template", "NotificationListComponent_div_36_button_39_Template", "NotificationListComponent_div_36_button_40_Template", "NotificationListComponent_div_36_Template_button_click_41_listener", "_r101", "ctx_r100", "ctx_r9", "NotificationListComponent", "constructor", "messageService", "themeService", "router", "hasMoreNotifications", "error", "Set", "showAttachmentsModal", "loadingAttachments", "showNotificationDetailsModal", "destroy$", "scrollPosition$", "notifications$", "notificationCount$", "isDarkMode$", "darkMode$", "isMuted", "notification", "conversationId", "metadata", "relatedEntity", "includes", "groupId", "navigate", "getOrCreateConversation", "subscribe", "next", "conversation", "target", "scrollPosition", "scrollTop", "scrollHeight", "clientHeight", "ngOnInit", "savedMutePreference", "localStorage", "getItem", "setMuted", "setupSubscriptions", "setupInfiniteScroll", "filterDeletedNotifications", "deletedNotificationIds", "getDeletedNotificationIds", "pipe", "notifications", "filteredNotifications", "has", "unreadCount", "n", "notificationCount", "updateNotificationCache", "loadMoreNotifications", "getNotifications", "err", "existingNotifications", "allNotifications", "subscribeToNewNotifications", "console", "log", "subscribeToNotificationsRead", "notificationId", "Error", "find", "updatedNotifications", "Date", "toISOString", "updateUIWithNotifications", "result", "success", "revertedNotifications", "undefined", "revertedUnreadCount", "for<PERSON>ach", "resetSelection", "unreadIds", "validIds", "trim", "hasUnreadNotifications", "count", "getUnreadNotifications", "setTimeout", "playNotificationSound", "setItem", "toString", "attachment", "convertAttachmentTypeToMessageType", "duration", "closeAttachmentsModal", "getNotificationAttachmentsForModal", "IMAGE", "VIDEO", "AUDIO", "FILE", "startsWith", "units", "i", "formattedSize", "toFixed", "window", "open", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "acceptFriendRequest", "add", "saveDeletedNotificationIds", "deletedIdsJson", "JSON", "parse", "deletedIds", "stringify", "Array", "from", "ngOnDestroy", "complete", "event", "delete", "updateSelectionState", "selectedIds", "deleteMultipleNotifications", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ThemeService", "i3", "Router", "selectors", "viewQuery", "NotificationListComponent_Query", "rf", "ctx", "NotificationListComponent_div_8_Template", "NotificationListComponent_div_9_Template", "NotificationListComponent_div_10_Template", "NotificationListComponent_div_11_Template", "NotificationListComponent_div_12_Template", "NotificationListComponent_div_14_Template", "NotificationListComponent_Template_div_click_16_listener", "NotificationListComponent_Template_div_click_17_listener", "NotificationListComponent_Template_button_click_22_listener", "NotificationListComponent_div_25_Template", "NotificationListComponent_div_26_Template", "NotificationListComponent_div_27_Template", "NotificationListComponent_Template_div_click_28_listener", "NotificationListComponent_Template_div_click_29_listener", "NotificationListComponent_Template_button_click_34_listener", "NotificationListComponent_div_36_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HostL<PERSON>ener,\r\n  ElementRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/services/message.service';\r\nimport { Observable, Subject, of, BehaviorSubject } from 'rxjs';\r\nimport {\r\n  Notification,\r\n  Attachment,\r\n  NotificationAttachment,\r\n  AttachmentType,\r\n  MessageType,\r\n} from 'src/app/models/message.model';\r\nimport {\r\n  catchError,\r\n  map,\r\n  takeUntil,\r\n  take,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  filter,\r\n} from 'rxjs/operators';\r\nimport { ThemeService } from '@app/services/theme.service';\r\n@Component({\r\n  selector: 'app-notification-list',\r\n  templateUrl: './notification-list.component.html',\r\n  styleUrls: ['./notification-list.component.css'],\r\n})\r\nexport class NotificationListComponent implements OnInit, OnDestroy {\r\n  @ViewChild('notificationContainer', { static: false })\r\n  notificationContainer!: ElementRef;\r\n\r\n  notifications$: Observable<Notification[]>;\r\n  filteredNotifications$: Observable<Notification[]>;\r\n  unreadCount$: Observable<number>;\r\n  isDarkMode$: Observable<boolean>;\r\n  loading = true;\r\n  loadingMore = false;\r\n  hasMoreNotifications = true;\r\n  error: Error | null = null;\r\n  showOnlyUnread = false;\r\n  isSoundMuted = false;\r\n\r\n  // Propriétés pour la sélection multiple\r\n  selectedNotifications: Set<string> = new Set<string>();\r\n  allSelected = false;\r\n  showSelectionBar = false;\r\n\r\n  // Propriétés pour le modal des pièces jointes\r\n  showAttachmentsModal = false;\r\n  loadingAttachments = false;\r\n  currentAttachments: Attachment[] = [];\r\n\r\n  // Propriétés pour le modal des détails de notification\r\n  showNotificationDetailsModal = false;\r\n  currentNotification: Notification | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  private scrollPosition$ = new BehaviorSubject<number>(0);\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private themeService: ThemeService,\r\n    private router: Router\r\n  ) {\r\n    this.notifications$ = this.messageService.notifications$;\r\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\r\n    this.unreadCount$ = this.messageService.notificationCount$;\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n\r\n    // Vérifier l'état du son\r\n    this.isSoundMuted = this.messageService.isMuted();\r\n  }\r\n\r\n  /**\r\n   * Rejoint une conversation ou un groupe à partir d'une notification\r\n   * @param notification Notification contenant les informations de la conversation ou du groupe\r\n   */\r\n  joinConversation(notification: Notification): void {\r\n    // Marquer la notification comme lue d'abord\r\n    this.markAsRead(notification.id);\r\n\r\n    // Extraire les informations pertinentes de la notification\r\n    const conversationId =\r\n      notification.conversationId ||\r\n      (notification.metadata && notification.metadata['conversationId']) ||\r\n      (notification.relatedEntity &&\r\n      notification.relatedEntity.includes('conversation')\r\n        ? notification.relatedEntity\r\n        : null);\r\n\r\n    const groupId =\r\n      notification.groupId ||\r\n      (notification.metadata && notification.metadata['groupId']) ||\r\n      (notification.relatedEntity &&\r\n      notification.relatedEntity.includes('group')\r\n        ? notification.relatedEntity\r\n        : null);\r\n\r\n    // Déterminer où rediriger l'utilisateur\r\n    if (conversationId) {\r\n      this.router.navigate(['/messages/conversations/chat', conversationId]);\r\n    } else if (groupId) {\r\n      this.router.navigate(['/messages/group', groupId]);\r\n    } else if (notification.senderId && notification.senderId.id) {\r\n      this.loading = true;\r\n\r\n      this.messageService\r\n        .getOrCreateConversation(notification.senderId.id)\r\n        .subscribe({\r\n          next: (conversation) => {\r\n            this.loading = false;\r\n            if (conversation && conversation.id) {\r\n              this.router.navigate([\r\n                '/messages/conversations/chat',\r\n                conversation.id,\r\n              ]);\r\n            } else {\r\n              this.router.navigate(['/messages']);\r\n            }\r\n          },\r\n          error: (error) => {\r\n            this.loading = false;\r\n            this.error = error;\r\n            this.router.navigate(['/messages']);\r\n          },\r\n        });\r\n    } else {\r\n      this.router.navigate(['/messages']);\r\n    }\r\n  }\r\n\r\n  @HostListener('scroll', ['$event.target'])\r\n  onScroll(target: HTMLElement): void {\r\n    if (!target) return;\r\n\r\n    const scrollPosition = target.scrollTop;\r\n    const scrollHeight = target.scrollHeight;\r\n    const clientHeight = target.clientHeight;\r\n\r\n    // Si on est proche du bas (à 200px du bas)\r\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\r\n      this.scrollPosition$.next(scrollPosition);\r\n    }\r\n  }\r\n  ngOnInit(): void {\r\n    // Charger la préférence de son depuis le localStorage\r\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\r\n    if (savedMutePreference !== null) {\r\n      this.isSoundMuted = savedMutePreference === 'true';\r\n      this.messageService.setMuted(this.isSoundMuted);\r\n    }\r\n\r\n    this.loadNotifications();\r\n    this.setupSubscriptions();\r\n    this.setupInfiniteScroll();\r\n    this.filterDeletedNotifications();\r\n  }\r\n\r\n  /**\r\n   * Filtre les notifications supprimées lors du chargement initial\r\n   */\r\n  private filterDeletedNotifications(): void {\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n    if (deletedNotificationIds.size > 0) {\r\n      this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n        const filteredNotifications = notifications.filter(\r\n          (notification) => !deletedNotificationIds.has(notification.id)\r\n        );\r\n\r\n        (this.messageService as any).notifications.next(filteredNotifications);\r\n        const unreadCount = filteredNotifications.filter(\r\n          (n) => !n.isRead\r\n        ).length;\r\n        (this.messageService as any).notificationCount.next(unreadCount);\r\n        this.updateNotificationCache(filteredNotifications);\r\n      });\r\n    }\r\n  }\r\n\r\n  setupInfiniteScroll(): void {\r\n    // Configurer le chargement des anciennes notifications lors du défilement\r\n    this.scrollPosition$\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        debounceTime(200), // Attendre 200ms après le dernier événement de défilement\r\n        distinctUntilChanged(), // Ne déclencher que si la position de défilement a changé\r\n        filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\r\n      )\r\n      .subscribe(() => {\r\n        this.loadMoreNotifications();\r\n      });\r\n  }\r\n  loadNotifications(): void {\r\n    this.loading = true;\r\n    this.loadingMore = false;\r\n    this.error = null;\r\n    this.hasMoreNotifications = true;\r\n\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n    this.messageService\r\n      .getNotifications(true)\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map((notifications) => {\r\n          if (deletedNotificationIds.size > 0) {\r\n            return notifications.filter(\r\n              (notification) => !deletedNotificationIds.has(notification.id)\r\n            );\r\n          }\r\n          return notifications;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (notifications) => {\r\n          (this.messageService as any).notifications.next(notifications);\r\n          const unreadCount = notifications.filter((n) => !n.isRead).length;\r\n          (this.messageService as any).notificationCount.next(unreadCount);\r\n          this.loading = false;\r\n          this.hasMoreNotifications =\r\n            this.messageService.hasMoreNotifications();\r\n        },\r\n        error: (err: Error) => {\r\n          this.error = err;\r\n          this.loading = false;\r\n          this.hasMoreNotifications = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  loadMoreNotifications(): void {\r\n    if (this.loadingMore || !this.hasMoreNotifications) return;\r\n\r\n    this.loadingMore = true;\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n    this.messageService\r\n      .loadMoreNotifications()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map((notifications) => {\r\n          if (deletedNotificationIds.size > 0) {\r\n            return notifications.filter(\r\n              (notification) => !deletedNotificationIds.has(notification.id)\r\n            );\r\n          }\r\n          return notifications;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (notifications) => {\r\n          this.notifications$\r\n            .pipe(take(1))\r\n            .subscribe((existingNotifications) => {\r\n              const allNotifications = [\r\n                ...existingNotifications,\r\n                ...notifications,\r\n              ];\r\n              (this.messageService as any).notifications.next(allNotifications);\r\n              const unreadCount = allNotifications.filter(\r\n                (n) => !n.isRead\r\n              ).length;\r\n              (this.messageService as any).notificationCount.next(unreadCount);\r\n              this.updateNotificationCache(allNotifications);\r\n            });\r\n\r\n          this.loadingMore = false;\r\n          this.hasMoreNotifications =\r\n            this.messageService.hasMoreNotifications();\r\n        },\r\n        error: (err: Error) => {\r\n          this.loadingMore = false;\r\n          this.hasMoreNotifications = false;\r\n        },\r\n      });\r\n  }\r\n  setupSubscriptions(): void {\r\n    this.messageService\r\n      .subscribeToNewNotifications()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        catchError((error) => {\r\n          console.log('Notification stream error:', error);\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.messageService\r\n      .subscribeToNotificationsRead()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        catchError((error) => {\r\n          console.log('Notifications read stream error:', error);\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe();\r\n  }\r\n  markAsRead(notificationId: string): void {\r\n    if (!notificationId) {\r\n      this.error = new Error('ID de notification invalide');\r\n      return;\r\n    }\r\n\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const notification = notifications.find((n) => n.id === notificationId);\r\n      if (notification) {\r\n        if (notification.isRead) return;\r\n\r\n        const updatedNotifications = notifications.map((n) =>\r\n          n.id === notificationId\r\n            ? { ...n, isRead: true, readAt: new Date().toISOString() }\r\n            : n\r\n        );\r\n\r\n        this.updateUIWithNotifications(updatedNotifications);\r\n\r\n        this.messageService\r\n          .markAsRead([notificationId])\r\n          .pipe(takeUntil(this.destroy$))\r\n          .subscribe({\r\n            next: (result) => {\r\n              if (result && result.success) {\r\n                if (this.error && this.error.message.includes('mark')) {\r\n                  this.error = null;\r\n                }\r\n              }\r\n            },\r\n            error: (err) => {\r\n              const revertedNotifications = notifications.map((n) =>\r\n                n.id === notificationId\r\n                  ? { ...n, isRead: false, readAt: undefined }\r\n                  : n\r\n              );\r\n              (this.messageService as any).notifications.next(\r\n                revertedNotifications\r\n              );\r\n\r\n              const revertedUnreadCount = revertedNotifications.filter(\r\n                (n) => !n.isRead\r\n              ).length;\r\n              (this.messageService as any).notificationCount.next(\r\n                revertedUnreadCount\r\n              );\r\n            },\r\n          });\r\n      } else {\r\n        this.loadNotifications();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Met à jour l'interface utilisateur avec les nouvelles notifications\r\n   * @param notifications Notifications à afficher\r\n   */\r\n  private updateUIWithNotifications(notifications: any[]): void {\r\n    // Mettre à jour l'interface utilisateur immédiatement\r\n    (this.messageService as any).notifications.next(notifications);\r\n\r\n    // Mettre à jour le compteur de notifications non lues\r\n    const unreadCount = notifications.filter((n) => !n.isRead).length;\r\n    (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n    // Mettre à jour le cache de notifications dans le service\r\n    this.updateNotificationCache(notifications);\r\n  }\r\n\r\n  /**\r\n   * Met à jour le cache de notifications dans le service\r\n   * @param notifications Notifications à mettre à jour\r\n   */\r\n  private updateNotificationCache(notifications: any[]): void {\r\n    notifications.forEach((notification) => {\r\n      (this.messageService as any).updateNotificationCache?.(notification);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Réinitialise la sélection des notifications\r\n   */\r\n  private resetSelection(): void {\r\n    this.selectedNotifications.clear();\r\n    this.allSelected = false;\r\n    this.showSelectionBar = false;\r\n  }\r\n\r\n  markAllAsRead(): void {\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const unreadIds = notifications.filter((n) => !n.isRead).map((n) => n.id);\r\n\r\n      if (unreadIds.length === 0) return;\r\n\r\n      const validIds = unreadIds.filter(\r\n        (id) => id && typeof id === 'string' && id.trim() !== ''\r\n      );\r\n\r\n      if (validIds.length !== unreadIds.length) {\r\n        this.error = new Error('Invalid notification IDs');\r\n        return;\r\n      }\r\n\r\n      const updatedNotifications = notifications.map((n) =>\r\n        validIds.includes(n.id)\r\n          ? { ...n, isRead: true, readAt: new Date().toISOString() }\r\n          : n\r\n      );\r\n\r\n      this.updateUIWithNotifications(updatedNotifications);\r\n\r\n      this.messageService\r\n        .markAsRead(validIds)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (result) => {\r\n            if (result && result.success) {\r\n              if (this.error && this.error.message.includes('mark')) {\r\n                this.error = null;\r\n              }\r\n            }\r\n          },\r\n          error: (err) => {\r\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\r\n          },\r\n        });\r\n    });\r\n  }\r\n\r\n  hasNotifications(): Observable<boolean> {\r\n    return this.notifications$.pipe(\r\n      map((notifications) => notifications?.length > 0)\r\n    );\r\n  }\r\n  hasUnreadNotifications(): Observable<boolean> {\r\n    return this.unreadCount$.pipe(map((count) => count > 0));\r\n  }\r\n\r\n  /**\r\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\r\n   */\r\n  toggleUnreadFilter(): void {\r\n    this.showOnlyUnread = !this.showOnlyUnread;\r\n\r\n    if (this.showOnlyUnread) {\r\n      this.filteredNotifications$ =\r\n        this.messageService.getUnreadNotifications();\r\n    } else {\r\n      this.filteredNotifications$ = this.notifications$;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Active/désactive le son des notifications\r\n   */\r\n  toggleSound(): void {\r\n    this.isSoundMuted = !this.isSoundMuted;\r\n    this.messageService.setMuted(this.isSoundMuted);\r\n\r\n    if (!this.isSoundMuted) {\r\n      setTimeout(() => {\r\n        this.messageService.playNotificationSound();\r\n        setTimeout(() => {\r\n          this.messageService.playNotificationSound();\r\n        }, 1000);\r\n      }, 100);\r\n    }\r\n\r\n    localStorage.setItem(\r\n      'notificationSoundMuted',\r\n      this.isSoundMuted.toString()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupère les pièces jointes d'une notification et ouvre le modal\r\n   * @param notificationId ID de la notification\r\n   */\r\n  getNotificationAttachments(notificationId: string): void {\r\n    if (!notificationId) return;\r\n\r\n    this.currentAttachments = [];\r\n    this.loadingAttachments = true;\r\n    this.showAttachmentsModal = true;\r\n\r\n    let notification: Notification | undefined;\r\n\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      notification = notifications.find(\r\n        (n: Notification) => n.id === notificationId\r\n      );\r\n    });\r\n\r\n    if (\r\n      notification &&\r\n      notification.message &&\r\n      notification.message.attachments &&\r\n      notification.message.attachments.length > 0\r\n    ) {\r\n      this.loadingAttachments = false;\r\n      this.currentAttachments = notification.message.attachments.map(\r\n        (attachment: NotificationAttachment) =>\r\n          ({\r\n            id: '',\r\n            url: attachment.url || '',\r\n            type: this.convertAttachmentTypeToMessageType(attachment.type),\r\n            name: attachment.name || '',\r\n            size: attachment.size || 0,\r\n            duration: 0,\r\n          } as Attachment)\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.messageService\r\n      .getNotificationAttachments(notificationId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (attachments) => {\r\n          this.loadingAttachments = false;\r\n          this.currentAttachments = attachments;\r\n        },\r\n        error: (err) => {\r\n          this.loadingAttachments = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Ferme le modal des pièces jointes\r\n   */\r\n  closeAttachmentsModal(): void {\r\n    this.showAttachmentsModal = false;\r\n  }\r\n\r\n  /**\r\n   * Ouvre le modal des détails de notification\r\n   * @param notification Notification à afficher\r\n   */\r\n  openNotificationDetails(notification: Notification): void {\r\n    this.currentNotification = notification;\r\n    this.showNotificationDetailsModal = true;\r\n\r\n    if (notification.message?.attachments?.length) {\r\n      this.getNotificationAttachmentsForModal(notification.id);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Ferme le modal des détails de notification\r\n   */\r\n  closeNotificationDetailsModal(): void {\r\n    this.showNotificationDetailsModal = false;\r\n    this.currentNotification = null;\r\n    this.currentAttachments = [];\r\n  }\r\n\r\n  /**\r\n   * Récupère les pièces jointes d'une notification pour le modal de détails\r\n   */\r\n  private getNotificationAttachmentsForModal(notificationId: string): void {\r\n    this.currentAttachments = [];\r\n\r\n    if (this.currentNotification?.message?.attachments?.length) {\r\n      this.currentAttachments =\r\n        this.currentNotification.message.attachments.map(\r\n          (attachment: NotificationAttachment) =>\r\n            ({\r\n              id: '',\r\n              url: attachment.url || '',\r\n              type: this.convertAttachmentTypeToMessageType(attachment.type),\r\n              name: attachment.name || '',\r\n              size: attachment.size || 0,\r\n              duration: 0,\r\n            } as Attachment)\r\n        );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convertit AttachmentType en MessageType\r\n   */\r\n  private convertAttachmentTypeToMessageType(\r\n    type: AttachmentType\r\n  ): MessageType {\r\n    switch (type) {\r\n      case 'IMAGE':\r\n        return MessageType.IMAGE;\r\n      case 'VIDEO':\r\n        return MessageType.VIDEO;\r\n      case 'AUDIO':\r\n        return MessageType.AUDIO;\r\n      case 'FILE':\r\n        return MessageType.FILE;\r\n      default:\r\n        return MessageType.FILE;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifie si un type de fichier est une image\r\n   */\r\n  isImage(type: string): boolean {\r\n    return type?.startsWith('image/') || false;\r\n  }\r\n\r\n  /**\r\n   * Obtient l'icône FontAwesome correspondant au type de fichier\r\n   * @param type Type MIME du fichier\r\n   * @returns Classe CSS de l'icône\r\n   */\r\n  getFileIcon(type: string): string {\r\n    if (!type) return 'fas fa-file';\r\n\r\n    if (type.startsWith('image/')) return 'fas fa-file-image';\r\n    if (type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\r\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (type.includes('word') || type.includes('document'))\r\n      return 'fas fa-file-word';\r\n    if (type.includes('excel') || type.includes('sheet'))\r\n      return 'fas fa-file-excel';\r\n    if (type.includes('powerpoint') || type.includes('presentation'))\r\n      return 'fas fa-file-powerpoint';\r\n    if (type.includes('zip') || type.includes('compressed'))\r\n      return 'fas fa-file-archive';\r\n\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  /**\r\n   * Obtient le libellé du type de fichier\r\n   * @param type Type MIME du fichier\r\n   * @returns Libellé du type de fichier\r\n   */\r\n  getFileTypeLabel(type: string): string {\r\n    if (!type) return 'Fichier';\r\n\r\n    if (type.startsWith('image/')) return 'Image';\r\n    if (type.startsWith('video/')) return 'Vidéo';\r\n    if (type.startsWith('audio/')) return 'Audio';\r\n    if (type.startsWith('text/')) return 'Texte';\r\n    if (type.includes('pdf')) return 'PDF';\r\n    if (type.includes('word') || type.includes('document')) return 'Document';\r\n    if (type.includes('excel') || type.includes('sheet'))\r\n      return 'Feuille de calcul';\r\n    if (type.includes('powerpoint') || type.includes('presentation'))\r\n      return 'Présentation';\r\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\r\n\r\n    return 'Fichier';\r\n  }\r\n\r\n  /**\r\n   * Formate la taille du fichier en unités lisibles\r\n   * @param size Taille en octets\r\n   * @returns Taille formatée (ex: \"1.5 MB\")\r\n   */\r\n  formatFileSize(size: number): string {\r\n    if (!size) return '';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n    let i = 0;\r\n    let formattedSize = size;\r\n\r\n    while (formattedSize >= 1024 && i < units.length - 1) {\r\n      formattedSize /= 1024;\r\n      i++;\r\n    }\r\n\r\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\r\n  }\r\n\r\n  /**\r\n   * Ouvre une pièce jointe dans un nouvel onglet\r\n   * @param url URL de la pièce jointe\r\n   */\r\n  openAttachment(url: string): void {\r\n    if (!url) return;\r\n    window.open(url, '_blank');\r\n  }\r\n\r\n  /**\r\n   * Télécharge une pièce jointe\r\n   * @param attachment Pièce jointe à télécharger\r\n   */\r\n  downloadAttachment(attachment: Attachment): void {\r\n    if (!attachment?.url) return;\r\n\r\n    const link = document.createElement('a');\r\n    link.href = attachment.url;\r\n    link.download = attachment.name || 'attachment';\r\n    link.target = '_blank';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  acceptFriendRequest(notification: Notification): void {\r\n    this.markAsRead(notification.id);\r\n  }\r\n\r\n  /**\r\n   * Supprime une notification et la stocke dans le localStorage\r\n   * @param notificationId ID de la notification à supprimer\r\n   */\r\n  deleteNotification(notificationId: string): void {\r\n    if (!notificationId) {\r\n      this.error = new Error('ID de notification invalide');\r\n      return;\r\n    }\r\n\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n    deletedNotificationIds.add(notificationId);\r\n    this.saveDeletedNotificationIds(deletedNotificationIds);\r\n\r\n    this.messageService\r\n      .deleteNotification(notificationId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result && result.success) {\r\n            if (this.error && this.error.message.includes('suppression')) {\r\n              this.error = null;\r\n            }\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.error = err;\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Supprime toutes les notifications et les stocke dans le localStorage\r\n   */\r\n  deleteAllNotifications(): void {\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n      notifications.forEach((notification) => {\r\n        deletedNotificationIds.add(notification.id);\r\n      });\r\n\r\n      this.saveDeletedNotificationIds(deletedNotificationIds);\r\n\r\n      this.messageService\r\n        .deleteAllNotifications()\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (result) => {\r\n            if (result && result.success) {\r\n              if (this.error && this.error.message.includes('suppression')) {\r\n                this.error = null;\r\n              }\r\n            }\r\n          },\r\n          error: (err) => {\r\n            this.error = err;\r\n          },\r\n        });\r\n    });\r\n  }\r\n\r\n  getErrorMessage(): string {\r\n    return this.error?.message || 'Unknown error occurred';\r\n  }\r\n\r\n  /**\r\n   * Récupère les IDs des notifications supprimées du localStorage\r\n   * @returns Set contenant les IDs des notifications supprimées\r\n   */\r\n  private getDeletedNotificationIds(): Set<string> {\r\n    try {\r\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\r\n      if (deletedIdsJson) {\r\n        return new Set<string>(JSON.parse(deletedIdsJson));\r\n      }\r\n      return new Set<string>();\r\n    } catch (error) {\r\n      return new Set<string>();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\r\n   * @param deletedIds Set contenant les IDs des notifications supprimées\r\n   */\r\n  private saveDeletedNotificationIds(deletedIds: Set<string>): void {\r\n    try {\r\n      localStorage.setItem(\r\n        'deletedNotificationIds',\r\n        JSON.stringify(Array.from(deletedIds))\r\n      );\r\n    } catch (error) {\r\n      // Ignore silently\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  /**\r\n   * Sélectionne ou désélectionne une notification\r\n   * @param notificationId ID de la notification\r\n   * @param event Événement de la case à cocher\r\n   */\r\n  toggleSelection(notificationId: string, event: Event): void {\r\n    event.stopPropagation(); // Empêcher la propagation de l'événement\r\n\r\n    if (this.selectedNotifications.has(notificationId)) {\r\n      this.selectedNotifications.delete(notificationId);\r\n    } else {\r\n      this.selectedNotifications.add(notificationId);\r\n    }\r\n\r\n    // Mettre à jour l'état de sélection globale\r\n    this.updateSelectionState();\r\n\r\n    // Afficher ou masquer la barre de sélection\r\n    this.showSelectionBar = this.selectedNotifications.size > 0;\r\n  }\r\n\r\n  /**\r\n   * Sélectionne ou désélectionne toutes les notifications\r\n   * @param event Événement de la case à cocher\r\n   */\r\n  toggleSelectAll(event: Event): void {\r\n    event.stopPropagation(); // Empêcher la propagation de l'événement\r\n\r\n    this.allSelected = !this.allSelected;\r\n\r\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\r\n      if (this.allSelected) {\r\n        // Sélectionner toutes les notifications\r\n        notifications.forEach((notification) => {\r\n          this.selectedNotifications.add(notification.id);\r\n        });\r\n      } else {\r\n        // Désélectionner toutes les notifications\r\n        this.selectedNotifications.clear();\r\n      }\r\n\r\n      // Afficher ou masquer la barre de sélection\r\n      this.showSelectionBar = this.selectedNotifications.size > 0;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Met à jour l'état de sélection globale\r\n   */\r\n  private updateSelectionState(): void {\r\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\r\n      this.allSelected =\r\n        notifications.length > 0 &&\r\n        this.selectedNotifications.size === notifications.length;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Supprime les notifications sélectionnées\r\n   */\r\n  deleteSelectedNotifications(): void {\r\n    if (this.selectedNotifications.size === 0) return;\r\n\r\n    const selectedIds = Array.from(this.selectedNotifications);\r\n\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const updatedNotifications = notifications.filter(\r\n        (notification) => !this.selectedNotifications.has(notification.id)\r\n      );\r\n\r\n      this.updateUIWithNotifications(updatedNotifications);\r\n      this.resetSelection();\r\n    });\r\n\r\n    this.messageService\r\n      .deleteMultipleNotifications(selectedIds)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          // Success handled silently\r\n        },\r\n        error: (err) => {\r\n          // Error handled silently\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Marque les notifications sélectionnées comme lues\r\n   */\r\n  markSelectedAsRead(): void {\r\n    if (this.selectedNotifications.size === 0) return;\r\n\r\n    const selectedIds = Array.from(this.selectedNotifications);\r\n\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const updatedNotifications = notifications.map((notification) =>\r\n        this.selectedNotifications.has(notification.id)\r\n          ? { ...notification, isRead: true, readAt: new Date().toISOString() }\r\n          : notification\r\n      );\r\n\r\n      this.updateUIWithNotifications(updatedNotifications);\r\n      this.resetSelection();\r\n    });\r\n\r\n    this.messageService\r\n      .markAsRead(selectedIds)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          // Success handled silently\r\n        },\r\n        error: (err) => {\r\n          // Error handled silently\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Vérifie si une notification est sélectionnée\r\n   * @param notificationId ID de la notification\r\n   * @returns true si la notification est sélectionnée, false sinon\r\n   */\r\n  isSelected(notificationId: string): boolean {\r\n    return this.selectedNotifications.has(notificationId);\r\n  }\r\n}\r\n", "<div\r\n  class=\"futuristic-notifications-container main-grid-container\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"background-elements background-grid\">\r\n    <!-- Grid pattern and scan line will be added via CSS -->\r\n  </div>\r\n\r\n  <div class=\"futuristic-notifications-card content-card relative z-10\">\r\n    <div class=\"futuristic-notifications-header\">\r\n      <h2 class=\"futuristic-title\">\r\n        <i class=\"fas fa-bell mr-2\"></i>\r\n        Notifications\r\n      </h2>\r\n\r\n      <!-- Barre d'actions normale -->\r\n      <div class=\"flex space-x-2\" *ngIf=\"!showSelectionBar\">\r\n        <!-- Bouton de rafraîchissement -->\r\n        <button\r\n          (click)=\"loadNotifications()\"\r\n          class=\"futuristic-action-button\"\r\n          title=\"Rafraîchir\"\r\n        >\r\n          <i class=\"fas fa-sync-alt\"></i>\r\n        </button>\r\n\r\n        <!-- Case à cocher \"Tout sélectionner\" (déplacée après le bouton de rafraîchissement) -->\r\n        <div *ngIf=\"hasNotifications() | async\" class=\"select-all-checkbox\">\r\n          <label class=\"futuristic-checkbox\">\r\n            <input\r\n              type=\"checkbox\"\r\n              [checked]=\"allSelected\"\r\n              (click)=\"toggleSelectAll($event)\"\r\n            />\r\n            <span class=\"checkmark\"></span>\r\n          </label>\r\n        </div>\r\n\r\n        <!-- Bouton de filtrage des notifications non lues -->\r\n        <button\r\n          (click)=\"toggleUnreadFilter()\"\r\n          class=\"futuristic-action-button\"\r\n          [class.active]=\"showOnlyUnread\"\r\n          title=\"Filtrer les non lues\"\r\n        >\r\n          <i class=\"fas fa-filter\"></i>\r\n        </button>\r\n\r\n        <!-- Bouton pour activer/désactiver le son -->\r\n        <button\r\n          (click)=\"toggleSound()\"\r\n          class=\"futuristic-action-button\"\r\n          [class.active]=\"!isSoundMuted\"\r\n          title=\"{{ isSoundMuted ? 'Activer le son' : 'Désactiver le son' }}\"\r\n        >\r\n          <i\r\n            class=\"fas\"\r\n            [ngClass]=\"isSoundMuted ? 'fa-volume-mute' : 'fa-volume-up'\"\r\n          ></i>\r\n        </button>\r\n\r\n        <!-- Bouton pour marquer toutes les notifications comme lues -->\r\n        <button\r\n          *ngIf=\"(unreadCount$ | async) || 0\"\r\n          (click)=\"markAllAsRead()\"\r\n          class=\"futuristic-primary-button\"\r\n        >\r\n          <i class=\"fas fa-check-double mr-1\"></i> Tout marquer comme lu\r\n        </button>\r\n\r\n        <!-- Bouton pour supprimer toutes les notifications -->\r\n        <button\r\n          *ngIf=\"hasNotifications() | async\"\r\n          (click)=\"deleteAllNotifications()\"\r\n          class=\"futuristic-danger-button\"\r\n          title=\"Supprimer toutes les notifications\"\r\n        >\r\n          <i class=\"fas fa-trash-alt mr-1\"></i> Tout supprimer\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Barre d'actions pour les notifications sélectionnées -->\r\n      <div class=\"flex space-x-2 selection-actions\" *ngIf=\"showSelectionBar\">\r\n        <span class=\"selection-count\"\r\n          >{{ selectedNotifications.size }} sélectionné(s)</span\r\n        >\r\n\r\n        <!-- Bouton pour marquer les notifications sélectionnées comme lues -->\r\n        <button\r\n          (click)=\"markSelectedAsRead()\"\r\n          class=\"futuristic-primary-button\"\r\n        >\r\n          <i class=\"fas fa-check mr-1\"></i> Marquer comme lu\r\n        </button>\r\n\r\n        <!-- Bouton pour supprimer les notifications sélectionnées -->\r\n        <button\r\n          (click)=\"deleteSelectedNotifications()\"\r\n          class=\"futuristic-danger-button\"\r\n        >\r\n          <i class=\"fas fa-trash-alt mr-1\"></i> Supprimer\r\n        </button>\r\n\r\n        <!-- Bouton pour annuler la sélection -->\r\n        <button\r\n          (click)=\"\r\n            selectedNotifications.clear();\r\n            showSelectionBar = false;\r\n            allSelected = false\r\n          \"\r\n          class=\"futuristic-cancel-button\"\r\n        >\r\n          <i class=\"fas fa-times mr-1\"></i> Annuler\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État de chargement futuriste -->\r\n    <div *ngIf=\"loading\" class=\"futuristic-loading-container\">\r\n      <div class=\"futuristic-loading-circle\"></div>\r\n      <p class=\"futuristic-loading-text\">Chargement des notifications...</p>\r\n    </div>\r\n\r\n    <!-- État d'erreur futuriste -->\r\n    <div *ngIf=\"error\" class=\"futuristic-error-message\">\r\n      <div class=\"flex items-center\">\r\n        <i class=\"fas fa-exclamation-triangle futuristic-error-icon\"></i>\r\n        <div>\r\n          <h3 class=\"futuristic-error-title\">Erreur de chargement</h3>\r\n          <p class=\"futuristic-error-text\">{{ getErrorMessage() }}</p>\r\n        </div>\r\n        <button\r\n          (click)=\"loadNotifications()\"\r\n          class=\"futuristic-retry-button ml-auto\"\r\n        >\r\n          Réessayer\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État vide futuriste -->\r\n    <div\r\n      *ngIf=\"!loading && !(hasNotifications() | async)\"\r\n      class=\"futuristic-empty-state\"\r\n    >\r\n      <div class=\"futuristic-empty-icon\">\r\n        <i class=\"fas fa-bell-slash\"></i>\r\n      </div>\r\n      <h3 class=\"futuristic-empty-title\">Aucune notification</h3>\r\n      <p class=\"futuristic-empty-text\">Vous êtes à jour !</p>\r\n      <button (click)=\"loadNotifications()\" class=\"futuristic-check-button\">\r\n        Vérifier les nouvelles notifications\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Liste des notifications futuriste -->\r\n    <div\r\n      *ngIf=\"!loading && (hasNotifications() | async)\"\r\n      class=\"futuristic-notifications-list\"\r\n      #notificationContainer\r\n      (scroll)=\"onScroll(notificationContainer)\"\r\n    >\r\n      <ng-container *ngFor=\"let notification of filteredNotifications$ | async\">\r\n        <div\r\n          [class.futuristic-notification-unread]=\"!notification.isRead\"\r\n          [class.futuristic-notification-read]=\"notification.isRead\"\r\n          [class.futuristic-notification-selected]=\"isSelected(notification.id)\"\r\n          class=\"futuristic-notification-card\"\r\n        >\r\n          <!-- Case à cocher pour la sélection (déplacée en haut à gauche) -->\r\n          <div class=\"notification-checkbox\">\r\n            <label class=\"futuristic-checkbox\">\r\n              <input\r\n                type=\"checkbox\"\r\n                [checked]=\"isSelected(notification.id)\"\r\n                (click)=\"toggleSelection(notification.id, $event)\"\r\n              />\r\n              <span class=\"checkmark\"></span>\r\n            </label>\r\n          </div>\r\n\r\n          <!-- Avatar de l'expéditeur simplifié -->\r\n          <div class=\"notification-avatar\">\r\n            <img\r\n              [src]=\"\r\n                notification.senderId?.image ||\r\n                'assets/images/default-avatar.png'\r\n              \"\r\n              alt=\"Avatar\"\r\n              onerror=\"this.src='assets/images/default-avatar.png'\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Contenu principal de la notification -->\r\n          <div class=\"notification-main-content\">\r\n            <!-- Contenu de notification simplifié -->\r\n            <div class=\"notification-content\">\r\n              <div class=\"notification-header\">\r\n                <div class=\"notification-header-top\">\r\n                  <span class=\"notification-sender\">{{\r\n                    notification.senderId?.username || \"Système\"\r\n                  }}</span>\r\n\r\n                  <!-- Heure de la notification (placée à droite du nom d'utilisateur) -->\r\n                  <div class=\"notification-time\">\r\n                    {{ notification.timestamp | date : \"shortTime\" }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Contenu du message (déplacé après l'en-tête) -->\r\n              <div class=\"notification-text-container\">\r\n                <span class=\"notification-text\">{{\r\n                  notification.content\r\n                }}</span>\r\n              </div>\r\n\r\n              <!-- Aperçu du message simplifié -->\r\n              <div\r\n                *ngIf=\"notification.message?.content\"\r\n                class=\"notification-message-preview\"\r\n              >\r\n                {{ notification.message?.content }}\r\n              </div>\r\n\r\n              <!-- Indicateur de pièces jointes -->\r\n              <div\r\n                *ngIf=\"notification.message?.attachments?.length\"\r\n                class=\"notification-attachments-indicator\"\r\n              >\r\n                <i class=\"fas fa-paperclip\"></i>\r\n                {{ notification.message?.attachments?.length }} pièce(s)\r\n                jointe(s)\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Indicateur de non-lu (petit point bleu) -->\r\n            <div *ngIf=\"!notification.isRead\" class=\"unread-indicator\"></div>\r\n          </div>\r\n\r\n          <!-- Actions de notification -->\r\n          <div class=\"notification-actions\">\r\n            <!-- Bouton pour afficher les pièces jointes -->\r\n            <button\r\n              *ngIf=\"notification.message?.attachments?.length\"\r\n              (click)=\"\r\n                getNotificationAttachments(notification.id);\r\n                $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-attachment-button\"\r\n              title=\"Voir les pièces jointes\"\r\n            >\r\n              <i class=\"fas fa-paperclip\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour rejoindre la conversation -->\r\n            <button\r\n              *ngIf=\"\r\n                notification.type === 'NEW_MESSAGE' ||\r\n                notification.type === 'GROUP_INVITE' ||\r\n                notification.type === 'MESSAGE_REACTION'\r\n              \"\r\n              (click)=\"joinConversation(notification); $event.stopPropagation()\"\r\n              class=\"notification-action-button notification-join-button\"\r\n              title=\"Rejoindre la conversation\"\r\n              [disabled]=\"loading\"\r\n            >\r\n              <i class=\"fas fa-comments\" *ngIf=\"!loading\"></i>\r\n              <i class=\"fas fa-spinner fa-spin\" *ngIf=\"loading\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour voir les détails de la notification -->\r\n            <button\r\n              (click)=\"\r\n                openNotificationDetails(notification); $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-details-button\"\r\n              title=\"Voir les détails (ne marque PAS comme lu automatiquement)\"\r\n            >\r\n              <i class=\"fas fa-info-circle\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton marquer comme lu -->\r\n            <button\r\n              *ngIf=\"!notification.isRead\"\r\n              (click)=\"markAsRead(notification.id); $event.stopPropagation()\"\r\n              class=\"notification-action-button notification-read-button\"\r\n              title=\"Marquer cette notification comme lue\"\r\n            >\r\n              <i class=\"fas fa-check\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour supprimer la notification -->\r\n            <button\r\n              (click)=\"\r\n                deleteNotification(notification.id); $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-delete-button\"\r\n              title=\"Supprimer cette notification\"\r\n            >\r\n              <i class=\"fas fa-trash-alt\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <!-- Indicateur de chargement des anciennes notifications -->\r\n      <div *ngIf=\"loadingMore\" class=\"futuristic-loading-more\">\r\n        <div class=\"futuristic-loading-circle-small\"></div>\r\n        <p class=\"futuristic-loading-text-small\">\r\n          Chargement des notifications plus anciennes...\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Modal pour afficher les pièces jointes -->\r\n<div\r\n  class=\"futuristic-modal-overlay\"\r\n  [style.display]=\"showAttachmentsModal ? 'flex' : 'none'\"\r\n  (click)=\"closeAttachmentsModal()\"\r\n>\r\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"futuristic-modal-header\">\r\n      <h3 class=\"futuristic-modal-title\">\r\n        <i class=\"fas fa-paperclip mr-2\"></i>\r\n        Pièces jointes\r\n      </h3>\r\n      <button class=\"futuristic-modal-close\" (click)=\"closeAttachmentsModal()\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n    <div class=\"futuristic-modal-body\">\r\n      <div *ngIf=\"loadingAttachments\" class=\"futuristic-loading-container\">\r\n        <div class=\"futuristic-loading-circle\"></div>\r\n        <p class=\"futuristic-loading-text\">Chargement des pièces jointes...</p>\r\n      </div>\r\n\r\n      <div\r\n        *ngIf=\"!loadingAttachments && currentAttachments.length === 0\"\r\n        class=\"futuristic-empty-state\"\r\n      >\r\n        <div class=\"futuristic-empty-icon\">\r\n          <i class=\"fas fa-file-alt\"></i>\r\n        </div>\r\n        <h3 class=\"futuristic-empty-title\">Aucune pièce jointe</h3>\r\n        <p class=\"futuristic-empty-text\">\r\n          Aucune pièce jointe n'a été trouvée pour cette notification.\r\n        </p>\r\n      </div>\r\n\r\n      <div\r\n        *ngIf=\"!loadingAttachments && currentAttachments.length > 0\"\r\n        class=\"futuristic-attachments-list\"\r\n      >\r\n        <div\r\n          *ngFor=\"let attachment of currentAttachments\"\r\n          class=\"futuristic-attachment-item\"\r\n        >\r\n          <!-- Image -->\r\n          <div\r\n            *ngIf=\"isImage(attachment.type)\"\r\n            class=\"futuristic-attachment-preview\"\r\n          >\r\n            <img\r\n              [src]=\"attachment.url\"\r\n              alt=\"Image\"\r\n              (click)=\"openAttachment(attachment.url)\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Document -->\r\n          <div\r\n            *ngIf=\"!isImage(attachment.type)\"\r\n            class=\"futuristic-attachment-icon\"\r\n          >\r\n            <i [class]=\"getFileIcon(attachment.type)\"></i>\r\n          </div>\r\n\r\n          <div class=\"futuristic-attachment-info\">\r\n            <div class=\"futuristic-attachment-name\">\r\n              {{ attachment.name || \"Pièce jointe\" }}\r\n            </div>\r\n            <div class=\"futuristic-attachment-meta\">\r\n              <span class=\"futuristic-attachment-type\">{{\r\n                getFileTypeLabel(attachment.type)\r\n              }}</span>\r\n              <span\r\n                *ngIf=\"attachment.size\"\r\n                class=\"futuristic-attachment-size\"\r\n                >{{ formatFileSize(attachment.size) }}</span\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"futuristic-attachment-actions\">\r\n            <button\r\n              class=\"futuristic-attachment-button\"\r\n              (click)=\"openAttachment(attachment.url)\"\r\n              title=\"Ouvrir\"\r\n            >\r\n              <i class=\"fas fa-external-link-alt\"></i>\r\n            </button>\r\n            <button\r\n              class=\"futuristic-attachment-button\"\r\n              (click)=\"downloadAttachment(attachment)\"\r\n              title=\"Télécharger\"\r\n            >\r\n              <i class=\"fas fa-download\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Modal pour afficher les détails de notification -->\r\n<div\r\n  class=\"futuristic-modal-overlay\"\r\n  [style.display]=\"showNotificationDetailsModal ? 'flex' : 'none'\"\r\n  (click)=\"closeNotificationDetailsModal()\"\r\n>\r\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"futuristic-modal-header\">\r\n      <h3 class=\"futuristic-modal-title\">\r\n        <i class=\"fas fa-info-circle mr-2\"></i>\r\n        Détails de la notification\r\n      </h3>\r\n      <button\r\n        class=\"futuristic-modal-close\"\r\n        (click)=\"closeNotificationDetailsModal()\"\r\n      >\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n    <div class=\"futuristic-modal-body\" *ngIf=\"currentNotification\">\r\n      <!-- Informations de l'expéditeur -->\r\n      <div class=\"notification-detail-section\">\r\n        <h4 class=\"notification-detail-title\">\r\n          <i class=\"fas fa-user mr-2\"></i>\r\n          Expéditeur\r\n        </h4>\r\n        <div class=\"notification-sender-info\">\r\n          <img\r\n            [src]=\"\r\n              currentNotification.senderId?.image ||\r\n              'assets/images/default-avatar.png'\r\n            \"\r\n            alt=\"Avatar\"\r\n            class=\"notification-sender-avatar\"\r\n            onerror=\"this.src='assets/images/default-avatar.png'\"\r\n          />\r\n          <div class=\"notification-sender-details\">\r\n            <span class=\"notification-sender-name\">\r\n              {{ currentNotification.senderId?.username || \"Système\" }}\r\n            </span>\r\n            <span class=\"notification-timestamp\">\r\n              {{ currentNotification.timestamp | date : \"medium\" }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Contenu de la notification -->\r\n      <div class=\"notification-detail-section\">\r\n        <h4 class=\"notification-detail-title\">\r\n          <i class=\"fas fa-message mr-2\"></i>\r\n          Message\r\n        </h4>\r\n        <div class=\"notification-content-detail\">\r\n          {{ currentNotification.content }}\r\n        </div>\r\n        <div\r\n          *ngIf=\"currentNotification.message?.content\"\r\n          class=\"notification-message-detail\"\r\n        >\r\n          <strong>Message original :</strong>\r\n          {{ currentNotification.message?.content }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Type et statut -->\r\n      <div class=\"notification-detail-section\">\r\n        <h4 class=\"notification-detail-title\">\r\n          <i class=\"fas fa-tag mr-2\"></i>\r\n          Informations\r\n        </h4>\r\n        <div class=\"notification-info-grid\">\r\n          <div class=\"notification-info-item\">\r\n            <span class=\"notification-info-label\">Type :</span>\r\n            <span class=\"notification-info-value\">{{\r\n              currentNotification.type\r\n            }}</span>\r\n          </div>\r\n          <div class=\"notification-info-item\">\r\n            <span class=\"notification-info-label\">Statut :</span>\r\n            <span\r\n              class=\"notification-info-value\"\r\n              [class.text-green-500]=\"currentNotification.isRead\"\r\n              [class.text-orange-500]=\"!currentNotification.isRead\"\r\n            >\r\n              {{ currentNotification.isRead ? \"Lu\" : \"Non lu\" }}\r\n            </span>\r\n          </div>\r\n          <div\r\n            class=\"notification-info-item\"\r\n            *ngIf=\"currentNotification.readAt\"\r\n          >\r\n            <span class=\"notification-info-label\">Lu le :</span>\r\n            <span class=\"notification-info-value\">{{\r\n              currentNotification.readAt | date : \"medium\"\r\n            }}</span>\r\n          </div>\r\n          <div\r\n            class=\"notification-info-item\"\r\n            *ngIf=\"!currentNotification.isRead\"\r\n            style=\"\r\n              background: rgba(255, 140, 0, 0.1);\r\n              border: 1px solid rgba(255, 140, 0, 0.3);\r\n            \"\r\n          >\r\n            <span class=\"notification-info-label\">\r\n              <i class=\"fas fa-info-circle mr-1\"></i>\r\n              Note :\r\n            </span>\r\n            <span\r\n              class=\"notification-info-value\"\r\n              style=\"color: #ff8c00; font-style: italic\"\r\n            >\r\n              Ouvrir les détails ne marque pas automatiquement comme lu\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Pièces jointes -->\r\n      <div\r\n        class=\"notification-detail-section\"\r\n        *ngIf=\"currentAttachments.length > 0\"\r\n      >\r\n        <h4 class=\"notification-detail-title\">\r\n          <i class=\"fas fa-paperclip mr-2\"></i>\r\n          Pièces jointes ({{ currentAttachments.length }})\r\n        </h4>\r\n        <div class=\"notification-attachments-grid\">\r\n          <div\r\n            *ngFor=\"let attachment of currentAttachments\"\r\n            class=\"notification-attachment-item\"\r\n          >\r\n            <!-- Image -->\r\n            <div\r\n              *ngIf=\"isImage(attachment.type)\"\r\n              class=\"notification-attachment-preview\"\r\n            >\r\n              <img\r\n                [src]=\"attachment.url\"\r\n                alt=\"Image\"\r\n                (click)=\"openAttachment(attachment.url)\"\r\n              />\r\n            </div>\r\n\r\n            <!-- Document -->\r\n            <div\r\n              *ngIf=\"!isImage(attachment.type)\"\r\n              class=\"notification-attachment-icon\"\r\n            >\r\n              <i [class]=\"getFileIcon(attachment.type)\"></i>\r\n            </div>\r\n\r\n            <div class=\"notification-attachment-info\">\r\n              <div class=\"notification-attachment-name\">\r\n                {{ attachment.name || \"Pièce jointe\" }}\r\n              </div>\r\n              <div class=\"notification-attachment-meta\">\r\n                <span class=\"notification-attachment-type\">{{\r\n                  getFileTypeLabel(attachment.type)\r\n                }}</span>\r\n                <span\r\n                  *ngIf=\"attachment.size\"\r\n                  class=\"notification-attachment-size\"\r\n                  >{{ formatFileSize(attachment.size) }}</span\r\n                >\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"notification-attachment-actions\">\r\n              <button\r\n                class=\"notification-attachment-button\"\r\n                (click)=\"openAttachment(attachment.url)\"\r\n                title=\"Ouvrir\"\r\n              >\r\n                <i class=\"fas fa-external-link-alt\"></i>\r\n              </button>\r\n              <button\r\n                class=\"notification-attachment-button\"\r\n                (click)=\"downloadAttachment(attachment)\"\r\n                title=\"Télécharger\"\r\n              >\r\n                <i class=\"fas fa-download\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Actions -->\r\n      <div class=\"notification-detail-actions\">\r\n        <button\r\n          *ngIf=\"\r\n            currentNotification.type === 'NEW_MESSAGE' ||\r\n            currentNotification.type === 'GROUP_INVITE' ||\r\n            currentNotification.type === 'MESSAGE_REACTION'\r\n          \"\r\n          (click)=\"\r\n            joinConversation(currentNotification);\r\n            closeNotificationDetailsModal()\r\n          \"\r\n          class=\"futuristic-primary-button\"\r\n          [disabled]=\"loading\"\r\n        >\r\n          <i class=\"fas fa-comments mr-2\" *ngIf=\"!loading\"></i>\r\n          <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"loading\"></i>\r\n          Rejoindre la conversation\r\n        </button>\r\n\r\n        <button\r\n          *ngIf=\"!currentNotification.isRead\"\r\n          (click)=\"markAsRead(currentNotification.id)\"\r\n          class=\"futuristic-secondary-button\"\r\n        >\r\n          <i class=\"fas fa-check mr-2\"></i>\r\n          Marquer comme lu\r\n        </button>\r\n\r\n        <button\r\n          (click)=\"\r\n            deleteNotification(currentNotification.id);\r\n            closeNotificationDetailsModal()\r\n          \"\r\n          class=\"futuristic-danger-button\"\r\n        >\r\n          <i class=\"fas fa-trash-alt mr-2\"></i>\r\n          Supprimer\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAUA,SAAqBA,OAAO,EAAEC,EAAE,EAAEC,eAAe,QAAQ,MAAM;AAC/D,SAKEC,WAAW,QACN,8BAA8B;AACrC,SACEC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,YAAY,EACZC,oBAAoB,EACpBC,MAAM,QACD,gBAAgB;;;;;;;;;;ICEfC,EAAA,CAAAC,cAAA,cAAoE;IAK9DD,EAAA,CAAAE,UAAA,mBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAHnCJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;;;;IAJJX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAC,OAAA,CAAAC,WAAA,CAAuB;;;;;;IA+B7BhB,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAe,0EAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAU,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBpB,EAAA,CAAAY,SAAA,YAAwC;IAACZ,EAAA,CAAAqB,MAAA,8BAC3C;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAAoB,2EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAe,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAIlCzB,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,uBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IA9DXX,EAAA,CAAAC,cAAA,cAAsD;IAGlDD,EAAA,CAAAE,UAAA,mBAAAwB,iEAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAmB,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAI7B7B,EAAA,CAAAY,SAAA,YAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAC,8CAAA,kBASM;;IAGN/B,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAA8B,iEAAA;MAAAhC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAM,OAAA,GAAAjC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAK9BlC,EAAA,CAAAY,SAAA,YAA6B;IAC/BZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAAiC,iEAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAS,OAAA,GAAApC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2B,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBrC,EAAA,CAAAY,SAAA,YAGK;IACPZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAQ,iDAAA,qBAMS;;IAGTtC,EAAA,CAAA8B,UAAA,KAAAS,kDAAA,qBAOS;;IACXvC,EAAA,CAAAW,YAAA,EAAM;;;;IApDEX,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,OAAAC,MAAA,CAAAC,gBAAA,IAAgC;IAepC1C,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA2C,WAAA,WAAAF,MAAA,CAAAG,cAAA,CAA+B;IAU/B5C,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA2C,WAAA,YAAAF,MAAA,CAAAI,YAAA,CAA8B;IAC9B7C,EAAA,CAAA8C,qBAAA,UAAAL,MAAA,CAAAI,YAAA,+CAAmE;IAIjE7C,EAAA,CAAAa,SAAA,GAA4D;IAA5Db,EAAA,CAAAc,UAAA,YAAA2B,MAAA,CAAAI,YAAA,qCAA4D;IAM7D7C,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAM,YAAA,OAAiC;IASjC/C,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAC,gBAAA,IAAgC;;;;;;IAUrC1C,EAAA,CAAAC,cAAA,cAAuE;IAElED,EAAA,CAAAqB,MAAA,GAA+C;IAAArB,EAAA,CAAAW,YAAA,EACjD;IAGDX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA8C,iEAAA;MAAAhD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyC,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BnD,EAAA,CAAAY,SAAA,YAAiC;IAACZ,EAAA,CAAAqB,MAAA,yBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAAkD,iEAAA;MAAApD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4C,OAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAGvCtD,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,kBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAOC;IANCD,EAAA,CAAAE,UAAA,mBAAAqD,iEAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAQ,aAAA;MACegD,OAAA,CAAAC,qBAAA,CAAAC,KAAA,EACf;MAAAF,OAAA,CAAAG,gBAAA,GACN,KAAK;MAAA,OAAA3D,EAAA,CAAAS,WAAA,CAAA+C,OAAA,CAAAxC,WAAA,GACL,KAAK;IAAA,EAAE;IAGDhB,EAAA,CAAAY,SAAA,aAAiC;IAACZ,EAAA,CAAAqB,MAAA,iBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IA7BNX,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAA4D,kBAAA,KAAAC,MAAA,CAAAJ,qBAAA,CAAAK,IAAA,8BAA+C;;;;;IAkCtD9D,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,sCAA+B;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAIxEX,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAY,SAAA,YAAiE;IACjEZ,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAqB,MAAA,2BAAoB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC5DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IAE9DX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6D,kEAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwD,OAAA,CAAApC,iBAAA,EAAmB;IAAA,EAAC;IAG7B7B,EAAA,CAAAqB,MAAA,uBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IAP0BX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAkE,iBAAA,CAAAC,MAAA,CAAAC,eAAA,GAAuB;;;;;;IAY9DpE,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,YAAiC;IACnCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,0BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,mCAAkB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,iBAAsE;IAA9DD,EAAA,CAAAE,UAAA,mBAAAmE,kEAAA;MAAArE,EAAA,CAAAK,aAAA,CAAAiE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8D,OAAA,CAAA1C,iBAAA,EAAmB;IAAA,EAAC;IACnC7B,EAAA,CAAAqB,MAAA,kDACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;IAkEDX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,MACF;;;;;IAGA1E,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAY,SAAA,YAAgC;IAChCZ,EAAA,CAAAqB,MAAA,GAEF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IAFJX,EAAA,CAAAa,SAAA,GAEF;IAFEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,8BAEF;;;;;IAIF5E,EAAA,CAAAY,SAAA,cAAiE;;;;;;IAMjEZ,EAAA,CAAAC,cAAA,iBAQC;IANCD,EAAA,CAAAE,UAAA,mBAAA2E,2FAAAzE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAN,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAQ,aAAA;MACmBwE,OAAA,CAAAC,0BAAA,CAAAT,gBAAA,CAAAU,EAAA,CACnB;MAAA,OAAkBlF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAClB;IAAA,EAAC;IAIDnF,EAAA,CAAAY,SAAA,YAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAcPX,EAAA,CAAAY,SAAA,YAAgD;;;;;IAChDZ,EAAA,CAAAY,SAAA,YAAsD;;;;;;IAZxDZ,EAAA,CAAAC,cAAA,iBAUC;IAJCD,EAAA,CAAAE,UAAA,mBAAAkF,2FAAAhF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgF,IAAA;MAAA,MAAAb,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAO,OAAA,GAAAtF,EAAA,CAAAQ,aAAA;MAAS8E,OAAA,CAAAC,gBAAA,CAAAf,gBAAA,CAA8B;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAKlEnF,EAAA,CAAA8B,UAAA,IAAA0D,sEAAA,gBAAgD;IAChDxF,EAAA,CAAA8B,UAAA,IAAA2D,sEAAA,gBAAsD;IACxDzF,EAAA,CAAAW,YAAA,EAAS;;;;IAJPX,EAAA,CAAAc,UAAA,aAAA4E,OAAA,CAAAC,OAAA,CAAoB;IAEQ3F,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,UAAA,UAAA4E,OAAA,CAAAC,OAAA,CAAc;IACP3F,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,UAAA,SAAA4E,OAAA,CAAAC,OAAA,CAAa;;;;;;IAelD3F,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAA0F,2FAAAxF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwF,IAAA;MAAA,MAAArB,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAe,OAAA,GAAA9F,EAAA,CAAAQ,aAAA;MAASsF,OAAA,CAAAC,UAAA,CAAAvB,gBAAA,CAAAU,EAAA,CAA2B;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAI/DnF,EAAA,CAAAY,SAAA,aAA4B;IAC9BZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAhIfX,EAAA,CAAAgG,uBAAA,GAA0E;IACxEhG,EAAA,CAAAC,cAAA,cAKC;IAOOD,EAAA,CAAAE,UAAA,mBAAA+F,gFAAA7F,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAqB,OAAA,GAAApG,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2F,OAAA,CAAAC,eAAA,CAAA7B,gBAAA,CAAAU,EAAA,EAAA9E,MAAA,CAAwC;IAAA,EAAC;IAHpDJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;IAIVX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAY,SAAA,cAOE;IACJZ,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAAuC;IAKGD,EAAA,CAAAqB,MAAA,IAEhC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAqB,MAAA,IACF;;IAAArB,EAAA,CAAAW,YAAA,EAAM;IAKVX,EAAA,CAAAC,cAAA,eAAyC;IACPD,EAAA,CAAAqB,MAAA,IAE9B;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAA8B,UAAA,KAAAwE,+DAAA,kBAKM;IAGNtG,EAAA,CAAA8B,UAAA,KAAAyE,+DAAA,kBAOM;IACRvG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA8B,UAAA,KAAA0E,+DAAA,kBAAiE;IACnExG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAAkC;IAEhCD,EAAA,CAAA8B,UAAA,KAAA2E,kEAAA,qBAUS;IAGTzG,EAAA,CAAA8B,UAAA,KAAA4E,kEAAA,qBAaS;IAGT1G,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAyG,kFAAAvG,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAA6B,OAAA,GAAA5G,EAAA,CAAAQ,aAAA;MACmBoG,OAAA,CAAAC,uBAAA,CAAArC,gBAAA,CAAqC;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EACzD;IAAA;IAIDnF,EAAA,CAAAY,SAAA,aAAkC;IACpCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,KAAAgF,kEAAA,qBAOS;IAGT9G,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAA6G,kFAAA3G,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA8F,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAiC,OAAA,GAAAhH,EAAA,CAAAQ,aAAA;MACmBwG,OAAA,CAAAC,kBAAA,CAAAzC,gBAAA,CAAAU,EAAA,CAAmC;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EACvD;IAAA;IAIDnF,EAAA,CAAAY,SAAA,aAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;IAGfX,EAAA,CAAAkH,qBAAA,EAAe;;;;;IA5IXlH,EAAA,CAAAa,SAAA,GAA6D;IAA7Db,EAAA,CAAA2C,WAAA,oCAAA6B,gBAAA,CAAA2C,MAAA,CAA6D,iCAAA3C,gBAAA,CAAA2C,MAAA,sCAAAC,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA;IAUvDlF,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,UAAA,YAAAsG,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA,EAAuC;IAUzClF,EAAA,CAAAa,SAAA,GAGC;IAHDb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAC,KAAA,yCAAAvH,EAAA,CAAAwH,aAAA,CAGC;IAYqCxH,EAAA,CAAAa,SAAA,GAEhC;IAFgCb,EAAA,CAAAkE,iBAAA,EAAAM,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAG,QAAA,oBAEhC;IAIAzH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAA0H,WAAA,SAAAlD,gBAAA,CAAAmD,SAAA,oBACF;IAM8B3H,EAAA,CAAAa,SAAA,GAE9B;IAF8Bb,EAAA,CAAAkE,iBAAA,CAAAM,gBAAA,CAAAE,OAAA,CAE9B;IAKD1E,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,CAAmC;IAQnC1E,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAU9C5E,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAA2C,MAAA,CAA0B;IAO7BnH,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAa/C5E,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAoD,IAAA,sBAAApD,gBAAA,CAAAoD,IAAA,uBAAApD,gBAAA,CAAAoD,IAAA,wBAIF;IAuBE5H,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAA2C,MAAA,CAA0B;;;;;IAuBnCnH,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAY,SAAA,eAAmD;IACnDZ,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAqB,MAAA,uDACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IA3JRX,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAE,UAAA,oBAAA2H,gEAAA;MAAA7H,EAAA,CAAAK,aAAA,CAAAyH,IAAA;MAAA,MAAAC,IAAA,GAAA/H,EAAA,CAAAgI,WAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAAwH,OAAA,CAAAC,QAAA,CAAAH,IAAA,CAA+B;IAAA,EAAC;IAE1C/H,EAAA,CAAA8B,UAAA,IAAAqG,wDAAA,6BA8Ie;;IAGfnI,EAAA,CAAA8B,UAAA,IAAAsG,+CAAA,kBAKM;IACRpI,EAAA,CAAAW,YAAA,EAAM;;;;IAvJmCX,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwC,WAAA,OAAA6F,MAAA,CAAAC,sBAAA,EAAiC;IAiJlEtI,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,SAAAuH,MAAA,CAAAE,WAAA,CAAiB;;;;;IA2BvBvI,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,4CAAgC;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;IAGzEX,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,aAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,+BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAqB,MAAA,yFACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAYFX,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,UAAA,mBAAAsI,2EAAA;MAAAxI,EAAA,CAAAK,aAAA,CAAAoI,IAAA;MAAA,MAAAC,cAAA,GAAA1I,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAA4D,OAAA,GAAA3I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkI,OAAA,CAAAC,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,EAAA,CAAAW,YAAA,EAIE;;;;IAHAX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,QAAA4H,cAAA,CAAAG,GAAA,EAAA7I,EAAA,CAAAwH,aAAA,CAAsB;;;;;IAO1BxH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,SAAA,QAA8C;IAChDZ,EAAA,CAAAW,YAAA,EAAM;;;;;IADDX,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA8I,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAN,cAAA,CAAAd,IAAA,EAAsC;;;;;IAWvC5H,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAqB,MAAA,GAAqC;IAAArB,EAAA,CAAAW,YAAA,EACvC;;;;;IADEX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkE,iBAAA,CAAA+E,OAAA,CAAAC,cAAA,CAAAR,cAAA,CAAA5E,IAAA,EAAqC;;;;;;IAnC9C9D,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAA8B,UAAA,IAAAqH,qDAAA,mBASM;IAGNnJ,EAAA,CAAA8B,UAAA,IAAAsH,qDAAA,mBAKM;IAENpJ,EAAA,CAAAC,cAAA,eAAwC;IAEpCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwC;IACGD,EAAA,CAAAqB,MAAA,GAEvC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACTX,EAAA,CAAA8B,UAAA,IAAAuH,sDAAA,oBAIC;IACHrJ,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,gBAA2C;IAGvCD,EAAA,CAAAE,UAAA,mBAAAoJ,yEAAA;MAAA,MAAApD,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAAkJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAAyE,OAAA,GAAAxJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA+I,OAAA,CAAAZ,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,EAAA,CAAAY,SAAA,cAAwC;IAC1CZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAuJ,yEAAA;MAAA,MAAAvD,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAAkJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAA2E,OAAA,GAAA1J,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAiJ,OAAA,CAAAC,kBAAA,CAAAjB,cAAA,CAA8B;IAAA,EAAC;IAGxC1I,EAAA,CAAAY,SAAA,cAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAhDRX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,SAAA8I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA8B;IAY9B5H,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,UAAA8I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA+B;IAQ9B5H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA8E,cAAA,CAAAoB,IAAA,6BACF;IAE2C9J,EAAA,CAAAa,SAAA,GAEvC;IAFuCb,EAAA,CAAAkE,iBAAA,CAAA0F,OAAA,CAAAG,gBAAA,CAAArB,cAAA,CAAAd,IAAA,EAEvC;IAEC5H,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAA4H,cAAA,CAAA5E,IAAA,CAAqB;;;;;IArChC9D,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAA8B,UAAA,IAAAkI,+CAAA,oBAwDM;IACRhK,EAAA,CAAAW,YAAA,EAAM;;;;IAxDqBX,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,YAAAmJ,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAqH9ClK,EAAA,CAAAC,cAAA,eAGC;IACSD,EAAA,CAAAqB,MAAA,yBAAkB;IAAArB,EAAA,CAAAW,YAAA,EAAS;IACnCX,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAAuG,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,kBAAA0F,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,MACF;;;;;IA0BE1E,EAAA,CAAAC,cAAA,eAGC;IACuCD,EAAA,CAAAqB,MAAA,cAAO;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACpDX,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAEpC;;IAAArB,EAAA,CAAAW,YAAA,EAAO;;;;IAF6BX,EAAA,CAAAa,SAAA,GAEpC;IAFoCb,EAAA,CAAAkE,iBAAA,CAAAlE,EAAA,CAAA0H,WAAA,OAAA2C,OAAA,CAAAD,mBAAA,CAAAE,MAAA,YAEpC;;;;;IAEJtK,EAAA,CAAAC,cAAA,eAOC;IAEGD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAqB,MAAA,eACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAqB,MAAA,uEACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;;;;;;IAoBPX,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,UAAA,mBAAAqK,kFAAA;MAAAvK,EAAA,CAAAK,aAAA,CAAAmK,IAAA;MAAA,MAAAC,cAAA,GAAAzK,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAA2F,OAAA,GAAA1K,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAiK,OAAA,CAAA9B,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,EAAA,CAAAW,YAAA,EAIE;;;;IAHAX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,QAAA2J,cAAA,CAAA5B,GAAA,EAAA7I,EAAA,CAAAwH,aAAA,CAAsB;;;;;IAO1BxH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,SAAA,QAA8C;IAChDZ,EAAA,CAAAW,YAAA,EAAM;;;;;IADDX,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA8I,UAAA,CAAA6B,OAAA,CAAA3B,WAAA,CAAAyB,cAAA,CAAA7C,IAAA,EAAsC;;;;;IAWvC5H,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAqB,MAAA,GAAqC;IAAArB,EAAA,CAAAW,YAAA,EACvC;;;;;IADEX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkE,iBAAA,CAAA0G,OAAA,CAAA1B,cAAA,CAAAuB,cAAA,CAAA3G,IAAA,EAAqC;;;;;;IAnC9C9D,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAA8B,UAAA,IAAA+I,4DAAA,mBASM;IAGN7K,EAAA,CAAA8B,UAAA,IAAAgJ,4DAAA,mBAKM;IAEN9K,EAAA,CAAAC,cAAA,eAA0C;IAEtCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA0C;IACGD,EAAA,CAAAqB,MAAA,GAEzC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACTX,EAAA,CAAA8B,UAAA,IAAAiJ,6DAAA,oBAIC;IACH/K,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,gBAA6C;IAGzCD,EAAA,CAAAE,UAAA,mBAAA8K,gFAAA;MAAA,MAAA9E,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAmG,OAAA,GAAAlL,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyK,OAAA,CAAAtC,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,EAAA,CAAAY,SAAA,cAAwC;IAC1CZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAiL,gFAAA;MAAA,MAAAjF,WAAA,GAAAlG,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAqG,OAAA,GAAApL,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2K,OAAA,CAAAzB,kBAAA,CAAAc,cAAA,CAA8B;IAAA,EAAC;IAGxCzK,EAAA,CAAAY,SAAA,cAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAhDRX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,SAAAuK,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA8B;IAY9B5H,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,UAAAuK,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA+B;IAQ9B5H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA6G,cAAA,CAAAX,IAAA,6BACF;IAE6C9J,EAAA,CAAAa,SAAA,GAEzC;IAFyCb,EAAA,CAAAkE,iBAAA,CAAAmH,OAAA,CAAAtB,gBAAA,CAAAU,cAAA,CAAA7C,IAAA,EAEzC;IAEC5H,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAA2J,cAAA,CAAA3G,IAAA,CAAqB;;;;;IA1ClC9D,EAAA,CAAAC,cAAA,eAGC;IAEGD,EAAA,CAAAY,SAAA,YAAqC;IACrCZ,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAA8B,UAAA,IAAAwJ,sDAAA,oBAwDM;IACRtL,EAAA,CAAAW,YAAA,EAAM;;;;IA5DJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,2BAAA2H,OAAA,CAAArB,kBAAA,CAAAtF,MAAA,OACF;IAG2B5E,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,YAAAyK,OAAA,CAAArB,kBAAA,CAAqB;;;;;IA0E9ClK,EAAA,CAAAY,SAAA,aAAqD;;;;;IACrDZ,EAAA,CAAAY,SAAA,aAA2D;;;;;;IAd7DZ,EAAA,CAAAC,cAAA,kBAYC;IANCD,EAAA,CAAAE,UAAA,mBAAAsL,4EAAA;MAAAxL,EAAA,CAAAK,aAAA,CAAAoL,IAAA;MAAA,MAAAC,OAAA,GAAA1L,EAAA,CAAAQ,aAAA;MACekL,OAAA,CAAAnG,gBAAA,CAAAmG,OAAA,CAAAtB,mBAAA,CACf;MAAA,OAAcpK,EAAA,CAAAS,WAAA,CAAAiL,OAAA,CAAAC,6BAAA,EACd;IAAA,EAAC;IAID3L,EAAA,CAAA8B,UAAA,IAAA8J,uDAAA,iBAAqD;IACrD5L,EAAA,CAAA8B,UAAA,IAAA+J,uDAAA,iBAA2D;IAC3D7L,EAAA,CAAAqB,MAAA,kCACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IALPX,EAAA,CAAAc,UAAA,aAAAgL,OAAA,CAAAnG,OAAA,CAAoB;IAEa3F,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,UAAA,UAAAgL,OAAA,CAAAnG,OAAA,CAAc;IACP3F,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,UAAA,SAAAgL,OAAA,CAAAnG,OAAA,CAAa;;;;;;IAIvD3F,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6L,4EAAA;MAAA/L,EAAA,CAAAK,aAAA,CAAA2L,IAAA;MAAA,MAAAC,OAAA,GAAAjM,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwL,OAAA,CAAAlG,UAAA,CAAAkG,OAAA,CAAA7B,mBAAA,CAAAlF,EAAA,CAAkC;IAAA,EAAC;IAG5ClF,EAAA,CAAAY,SAAA,aAAiC;IACjCZ,EAAA,CAAAqB,MAAA,yBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IArMbX,EAAA,CAAAC,cAAA,cAA+D;IAIzDD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAqB,MAAA,wBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAY,SAAA,eAQE;IACFZ,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,iBAAqC;IACnCD,EAAA,CAAAqB,MAAA,IACF;;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAMbX,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAqB,MAAA,iBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAqB,MAAA,IACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA8B,UAAA,KAAAoK,gDAAA,mBAMM;IACRlM,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAY,SAAA,cAA+B;IAC/BZ,EAAA,CAAAqB,MAAA,sBACF;IAAArB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,gBAAoC;IAEMD,EAAA,CAAAqB,MAAA,cAAM;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACnDX,EAAA,CAAAC,cAAA,iBAAsC;IAAAD,EAAA,CAAAqB,MAAA,IAEpC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAEXX,EAAA,CAAAC,cAAA,gBAAoC;IACID,EAAA,CAAAqB,MAAA,gBAAQ;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACrDX,EAAA,CAAAC,cAAA,iBAIC;IACCD,EAAA,CAAAqB,MAAA,IACF;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAETX,EAAA,CAAA8B,UAAA,KAAAqK,gDAAA,mBAQM;IACNnM,EAAA,CAAA8B,UAAA,KAAAsK,gDAAA,mBAkBM;IACRpM,EAAA,CAAAW,YAAA,EAAM;IAIRX,EAAA,CAAA8B,UAAA,KAAAuK,gDAAA,mBAmEM;IAGNrM,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAA8B,UAAA,KAAAwK,mDAAA,sBAgBS;IAETtM,EAAA,CAAA8B,UAAA,KAAAyK,mDAAA,sBAOS;IAETvM,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAsM,mEAAA;MAAAxM,EAAA,CAAAK,aAAA,CAAAoM,KAAA;MAAA,MAAAC,QAAA,GAAA1M,EAAA,CAAAQ,aAAA;MACekM,QAAA,CAAAzF,kBAAA,CAAAyF,QAAA,CAAAtC,mBAAA,CAAAlF,EAAA,CACf;MAAA,OAAclF,EAAA,CAAAS,WAAA,CAAAiM,QAAA,CAAAf,6BAAA,EACd;IAAA,EAAC;IAGD3L,EAAA,CAAAY,SAAA,cAAqC;IACrCZ,EAAA,CAAAqB,MAAA,mBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IAvMLX,EAAA,CAAAa,SAAA,GAGC;IAHDb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAC,KAAA,yCAAAvH,EAAA,CAAAwH,aAAA,CAGC;IAOCxH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,OAAA+I,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAG,QAAA,yBACF;IAEEzH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAA0H,WAAA,SAAAiF,MAAA,CAAAvC,mBAAA,CAAAzC,SAAA,iBACF;IAYF3H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA+I,MAAA,CAAAvC,mBAAA,CAAA1F,OAAA,MACF;IAEG1E,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,kBAAAkI,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,CAA0C;IAiBH1E,EAAA,CAAAa,SAAA,IAEpC;IAFoCb,EAAA,CAAAkE,iBAAA,CAAAyI,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,CAEpC;IAMA5H,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAA2C,WAAA,mBAAAgK,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAmD,qBAAAwF,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA;IAGnDnH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA+I,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,wBACF;IAICnH,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAAE,MAAA,CAAgC;IAShCtK,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,UAAA6L,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;IAuBrCnH,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAzC,kBAAA,CAAAtF,MAAA,KAAmC;IAsEjC5E,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAc,UAAA,SAAA6L,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,sBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,uBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,wBAIF;IAcE5H,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,UAAA6L,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;;;ADplB5C,OAAM,MAAOyF,yBAAyB;EAgCpCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAArH,OAAO,GAAG,IAAI;IACd,KAAA4C,WAAW,GAAG,KAAK;IACnB,KAAA0E,oBAAoB,GAAG,IAAI;IAC3B,KAAAC,KAAK,GAAiB,IAAI;IAC1B,KAAAtK,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAY,qBAAqB,GAAgB,IAAI0J,GAAG,EAAU;IACtD,KAAAnM,WAAW,GAAG,KAAK;IACnB,KAAA2C,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAyJ,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAnD,kBAAkB,GAAiB,EAAE;IAErC;IACA,KAAAoD,4BAA4B,GAAG,KAAK;IACpC,KAAAlD,mBAAmB,GAAwB,IAAI;IAEvC,KAAAmD,QAAQ,GAAG,IAAIlO,OAAO,EAAQ;IAC9B,KAAAmO,eAAe,GAAG,IAAIjO,eAAe,CAAS,CAAC,CAAC;IAOtD,IAAI,CAACkO,cAAc,GAAG,IAAI,CAACX,cAAc,CAACW,cAAc;IACxD,IAAI,CAACnF,sBAAsB,GAAG,IAAI,CAACmF,cAAc,CAAC,CAAC;IACnD,IAAI,CAAC1K,YAAY,GAAG,IAAI,CAAC+J,cAAc,CAACY,kBAAkB;IAC1D,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,YAAY,CAACa,SAAS;IAE9C;IACA,IAAI,CAAC/K,YAAY,GAAG,IAAI,CAACiK,cAAc,CAACe,OAAO,EAAE;EACnD;EAEA;;;;EAIAtI,gBAAgBA,CAACuI,YAA0B;IACzC;IACA,IAAI,CAAC/H,UAAU,CAAC+H,YAAY,CAAC5I,EAAE,CAAC;IAEhC;IACA,MAAM6I,cAAc,GAClBD,YAAY,CAACC,cAAc,IAC1BD,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,gBAAgB,CAAE,KACjEF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,cAAc,CAAC,GAC/CJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX,MAAME,OAAO,GACXL,YAAY,CAACK,OAAO,IACnBL,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAE,KAC1DF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,OAAO,CAAC,GACxCJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX;IACA,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACf,MAAM,CAACoB,QAAQ,CAAC,CAAC,8BAA8B,EAAEL,cAAc,CAAC,CAAC;KACvE,MAAM,IAAII,OAAO,EAAE;MAClB,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,iBAAiB,EAAED,OAAO,CAAC,CAAC;KACnD,MAAM,IAAIL,YAAY,CAACxG,QAAQ,IAAIwG,YAAY,CAACxG,QAAQ,CAACpC,EAAE,EAAE;MAC5D,IAAI,CAACS,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACmH,cAAc,CAChBuB,uBAAuB,CAACP,YAAY,CAACxG,QAAQ,CAACpC,EAAE,CAAC,CACjDoJ,SAAS,CAAC;QACTC,IAAI,EAAGC,YAAY,IAAI;UACrB,IAAI,CAAC7I,OAAO,GAAG,KAAK;UACpB,IAAI6I,YAAY,IAAIA,YAAY,CAACtJ,EAAE,EAAE;YACnC,IAAI,CAAC8H,MAAM,CAACoB,QAAQ,CAAC,CACnB,8BAA8B,EAC9BI,YAAY,CAACtJ,EAAE,CAChB,CAAC;WACH,MAAM;YACL,IAAI,CAAC8H,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAEvC,CAAC;QACDlB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACuH,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACF,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAACpB,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;EAEvC;EAGAlG,QAAQA,CAACuG,MAAmB;IAC1B,IAAI,CAACA,MAAM,EAAE;IAEb,MAAMC,cAAc,GAAGD,MAAM,CAACE,SAAS;IACvC,MAAMC,YAAY,GAAGH,MAAM,CAACG,YAAY;IACxC,MAAMC,YAAY,GAAGJ,MAAM,CAACI,YAAY;IAExC;IACA,IAAID,YAAY,GAAGF,cAAc,GAAGG,YAAY,GAAG,GAAG,EAAE;MACtD,IAAI,CAACrB,eAAe,CAACe,IAAI,CAACG,cAAc,CAAC;;EAE7C;EACAI,QAAQA,CAAA;IACN;IACA,MAAMC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC1E,IAAIF,mBAAmB,KAAK,IAAI,EAAE;MAChC,IAAI,CAAClM,YAAY,GAAGkM,mBAAmB,KAAK,MAAM;MAClD,IAAI,CAACjC,cAAc,CAACoC,QAAQ,CAAC,IAAI,CAACrM,YAAY,CAAC;;IAGjD,IAAI,CAAChB,iBAAiB,EAAE;IACxB,IAAI,CAACsN,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;;;EAGQA,0BAA0BA,CAAA;IAChC,MAAMC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAID,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC2J,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;QAC5D,MAAMC,qBAAqB,GAAGD,aAAa,CAAC1P,MAAM,CAC/C+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;QAEA,IAAI,CAAC4H,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACmB,qBAAqB,CAAC;QACtE,MAAME,WAAW,GAAGF,qBAAqB,CAAC3P,MAAM,CAC7C8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;QACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAChE,IAAI,CAACG,uBAAuB,CAACL,qBAAqB,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEAN,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC5B,eAAe,CACjBgC,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB1N,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE;IAAE;IACxBC,MAAM,CAAC,MAAM,CAAC,IAAI,CAACwI,WAAW,IAAI,IAAI,CAAC0E,oBAAoB,CAAC,CAAC;KAC9D,CACAqB,SAAS,CAAC,MAAK;MACd,IAAI,CAAC0B,qBAAqB,EAAE;IAC9B,CAAC,CAAC;EACN;EACAnO,iBAAiBA,CAAA;IACf,IAAI,CAAC8D,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2E,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,MAAMqC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAACzC,cAAc,CAChBmD,gBAAgB,CAAC,IAAI,CAAC,CACtBT,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,GAAG,CAAE+P,aAAa,IAAI;MACpB,IAAIH,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO2L,aAAa,CAAC1P,MAAM,CACxB+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;;MAEH,OAAOuK,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACrB,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACkB,aAAa,CAAC;QAC9D,MAAMG,WAAW,GAAGH,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACvC,MAAM;QAChE,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAChE,IAAI,CAACjK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGgD,GAAU,IAAI;QACpB,IAAI,CAAChD,KAAK,GAAGgD,GAAG;QAChB,IAAI,CAACvK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EAEA+C,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACzH,WAAW,IAAI,CAAC,IAAI,CAAC0E,oBAAoB,EAAE;IAEpD,IAAI,CAAC1E,WAAW,GAAG,IAAI;IACvB,MAAM+G,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAACzC,cAAc,CAChBkD,qBAAqB,EAAE,CACvBR,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,GAAG,CAAE+P,aAAa,IAAI;MACpB,IAAIH,sBAAsB,CAACxL,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO2L,aAAa,CAAC1P,MAAM,CACxB+N,YAAY,IAAK,CAACwB,sBAAsB,CAACK,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CAC/D;;MAEH,OAAOuK,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACtB,IAAI,CAAChC,cAAc,CAChB+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CACb0O,SAAS,CAAE6B,qBAAqB,IAAI;UACnC,MAAMC,gBAAgB,GAAG,CACvB,GAAGD,qBAAqB,EACxB,GAAGV,aAAa,CACjB;UACA,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAAC6B,gBAAgB,CAAC;UACjE,MAAMR,WAAW,GAAGQ,gBAAgB,CAACrQ,MAAM,CACxC8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;UACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;UAChE,IAAI,CAACG,uBAAuB,CAACK,gBAAgB,CAAC;QAChD,CAAC,CAAC;QAEJ,IAAI,CAAC7H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGgD,GAAU,IAAI;QACpB,IAAI,CAAC3H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EACAkC,kBAAkBA,CAAA;IAChB,IAAI,CAACrC,cAAc,CAChBuD,2BAA2B,EAAE,CAC7Bb,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,UAAU,CAAEyN,KAAK,IAAI;MACnBoD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAErD,KAAK,CAAC;MAChD,OAAO5N,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAgP,SAAS,EAAE;IACd,IAAI,CAACxB,cAAc,CAChB0D,4BAA4B,EAAE,CAC9BhB,IAAI,CACH7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,UAAU,CAAEyN,KAAK,IAAI;MACnBoD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAErD,KAAK,CAAC;MACtD,OAAO5N,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAgP,SAAS,EAAE;EAChB;EACAvI,UAAUA,CAAC0K,cAAsB;IAC/B,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACvD,KAAK,GAAG,IAAIwD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,IAAI,CAACjD,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAM3B,YAAY,GAAG2B,aAAa,CAACkB,IAAI,CAAEd,CAAC,IAAKA,CAAC,CAAC3K,EAAE,KAAKuL,cAAc,CAAC;MACvE,IAAI3C,YAAY,EAAE;QAChB,IAAIA,YAAY,CAAC3G,MAAM,EAAE;QAEzB,MAAMyJ,oBAAoB,GAAGnB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAC/CA,CAAC,CAAC3K,EAAE,KAAKuL,cAAc,GACnB;UAAE,GAAGZ,CAAC;UAAE1I,MAAM,EAAE,IAAI;UAAEmD,MAAM,EAAE,IAAIuG,IAAI,EAAE,CAACC,WAAW;QAAE,CAAE,GACxDjB,CAAC,CACN;QAED,IAAI,CAACkB,yBAAyB,CAACH,oBAAoB,CAAC;QAEpD,IAAI,CAAC9D,cAAc,CAChB/G,UAAU,CAAC,CAAC0K,cAAc,CAAC,CAAC,CAC5BjB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;UACTC,IAAI,EAAGyC,MAAM,IAAI;YACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;cAC5B,IAAI,IAAI,CAAC/D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACrD,IAAI,CAAChB,KAAK,GAAG,IAAI;;;UAGvB,CAAC;UACDA,KAAK,EAAGgD,GAAG,IAAI;YACb,MAAMgB,qBAAqB,GAAGzB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAChDA,CAAC,CAAC3K,EAAE,KAAKuL,cAAc,GACnB;cAAE,GAAGZ,CAAC;cAAE1I,MAAM,EAAE,KAAK;cAAEmD,MAAM,EAAE6G;YAAS,CAAE,GAC1CtB,CAAC,CACN;YACA,IAAI,CAAC/C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAC7C2C,qBAAqB,CACtB;YAED,MAAME,mBAAmB,GAAGF,qBAAqB,CAACnR,MAAM,CACrD8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CACjB,CAACvC,MAAM;YACP,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CACjD6C,mBAAmB,CACpB;UACH;SACD,CAAC;OACL,MAAM;QACL,IAAI,CAACvP,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEA;;;;EAIQkP,yBAAyBA,CAACtB,aAAoB;IACpD;IACC,IAAI,CAAC3C,cAAsB,CAAC2C,aAAa,CAAClB,IAAI,CAACkB,aAAa,CAAC;IAE9D;IACA,MAAMG,WAAW,GAAGH,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACvC,MAAM;IAChE,IAAI,CAACkI,cAAsB,CAACgD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;IAEhE;IACA,IAAI,CAACG,uBAAuB,CAACN,aAAa,CAAC;EAC7C;EAEA;;;;EAIQM,uBAAuBA,CAACN,aAAoB;IAClDA,aAAa,CAAC4B,OAAO,CAAEvD,YAAY,IAAI;MACpC,IAAI,CAAChB,cAAsB,CAACiD,uBAAuB,GAAGjC,YAAY,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA;;;EAGQwD,cAAcA,CAAA;IACpB,IAAI,CAAC7N,qBAAqB,CAACC,KAAK,EAAE;IAClC,IAAI,CAAC1C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2C,gBAAgB,GAAG,KAAK;EAC/B;EAEAvC,aAAaA,CAAA;IACX,IAAI,CAACqM,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAM8B,SAAS,GAAG9B,aAAa,CAAC1P,MAAM,CAAE8P,CAAC,IAAK,CAACA,CAAC,CAAC1I,MAAM,CAAC,CAACzH,GAAG,CAAEmQ,CAAC,IAAKA,CAAC,CAAC3K,EAAE,CAAC;MAEzE,IAAIqM,SAAS,CAAC3M,MAAM,KAAK,CAAC,EAAE;MAE5B,MAAM4M,QAAQ,GAAGD,SAAS,CAACxR,MAAM,CAC9BmF,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACuM,IAAI,EAAE,KAAK,EAAE,CACzD;MAED,IAAID,QAAQ,CAAC5M,MAAM,KAAK2M,SAAS,CAAC3M,MAAM,EAAE;QACxC,IAAI,CAACsI,KAAK,GAAG,IAAIwD,KAAK,CAAC,0BAA0B,CAAC;QAClD;;MAGF,MAAME,oBAAoB,GAAGnB,aAAa,CAAC/P,GAAG,CAAEmQ,CAAC,IAC/C2B,QAAQ,CAACtD,QAAQ,CAAC2B,CAAC,CAAC3K,EAAE,CAAC,GACnB;QAAE,GAAG2K,CAAC;QAAE1I,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIuG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACxDjB,CAAC,CACN;MAED,IAAI,CAACkB,yBAAyB,CAACH,oBAAoB,CAAC;MAEpD,IAAI,CAAC9D,cAAc,CAChB/G,UAAU,CAACyL,QAAQ,CAAC,CACpBhC,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;QACTC,IAAI,EAAGyC,MAAM,IAAI;UACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5B,IAAI,IAAI,CAAC/D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,MAAM,CAAC,EAAE;cACrD,IAAI,CAAChB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGgD,GAAG,IAAI;UACb;QAAA;OAEH,CAAC;IACN,CAAC,CAAC;EACJ;EAEAxN,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC+K,cAAc,CAAC+B,IAAI,CAC7B9P,GAAG,CAAE+P,aAAa,IAAKA,aAAa,EAAE7K,MAAM,GAAG,CAAC,CAAC,CAClD;EACH;EACA8M,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC3O,YAAY,CAACyM,IAAI,CAAC9P,GAAG,CAAEiS,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EAC1D;EAEA;;;EAGAzP,kBAAkBA,CAAA;IAChB,IAAI,CAACU,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAAC0F,sBAAsB,GACzB,IAAI,CAACwE,cAAc,CAAC8E,sBAAsB,EAAE;KAC/C,MAAM;MACL,IAAI,CAACtJ,sBAAsB,GAAG,IAAI,CAACmF,cAAc;;EAErD;EAEA;;;EAGApL,WAAWA,CAAA;IACT,IAAI,CAACQ,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACiK,cAAc,CAACoC,QAAQ,CAAC,IAAI,CAACrM,YAAY,CAAC;IAE/C,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;MACtBgP,UAAU,CAAC,MAAK;QACd,IAAI,CAAC/E,cAAc,CAACgF,qBAAqB,EAAE;QAC3CD,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/E,cAAc,CAACgF,qBAAqB,EAAE;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;;IAGT9C,YAAY,CAAC+C,OAAO,CAClB,wBAAwB,EACxB,IAAI,CAAClP,YAAY,CAACmP,QAAQ,EAAE,CAC7B;EACH;EAEA;;;;EAIA/M,0BAA0BA,CAACwL,cAAsB;IAC/C,IAAI,CAACA,cAAc,EAAE;IAErB,IAAI,CAACvG,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACmD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,IAAIU,YAAsC;IAE1C,IAAI,CAACL,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D3B,YAAY,GAAG2B,aAAa,CAACkB,IAAI,CAC9Bd,CAAe,IAAKA,CAAC,CAAC3K,EAAE,KAAKuL,cAAc,CAC7C;IACH,CAAC,CAAC;IAEF,IACE3C,YAAY,IACZA,YAAY,CAACrJ,OAAO,IACpBqJ,YAAY,CAACrJ,OAAO,CAACE,WAAW,IAChCmJ,YAAY,CAACrJ,OAAO,CAACE,WAAW,CAACC,MAAM,GAAG,CAAC,EAC3C;MACA,IAAI,CAACyI,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnD,kBAAkB,GAAG4D,YAAY,CAACrJ,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC3DuS,UAAkC,KAChC;QACC/M,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEoJ,UAAU,CAACpJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACsK,kCAAkC,CAACD,UAAU,CAACrK,IAAI,CAAC;QAC9DkC,IAAI,EAAEmI,UAAU,CAACnI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEmO,UAAU,CAACnO,IAAI,IAAI,CAAC;QAC1BqO,QAAQ,EAAE;OACI,EACnB;MACD;;IAGF,IAAI,CAACrF,cAAc,CAChB7H,0BAA0B,CAACwL,cAAc,CAAC,CAC1CjB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAG5J,WAAW,IAAI;QACpB,IAAI,CAAC0I,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACnD,kBAAkB,GAAGvF,WAAW;MACvC,CAAC;MACDuI,KAAK,EAAGgD,GAAG,IAAI;QACb,IAAI,CAAC7C,kBAAkB,GAAG,KAAK;MACjC;KACD,CAAC;EACN;EAEA;;;EAGA+E,qBAAqBA,CAAA;IACnB,IAAI,CAAChF,oBAAoB,GAAG,KAAK;EACnC;EAEA;;;;EAIAvG,uBAAuBA,CAACiH,YAA0B;IAChD,IAAI,CAAC1D,mBAAmB,GAAG0D,YAAY;IACvC,IAAI,CAACR,4BAA4B,GAAG,IAAI;IAExC,IAAIQ,YAAY,CAACrJ,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC7C,IAAI,CAACyN,kCAAkC,CAACvE,YAAY,CAAC5I,EAAE,CAAC;;EAE5D;EAEA;;;EAGAyG,6BAA6BA,CAAA;IAC3B,IAAI,CAAC2B,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAAClD,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACF,kBAAkB,GAAG,EAAE;EAC9B;EAEA;;;EAGQmI,kCAAkCA,CAAC5B,cAAsB;IAC/D,IAAI,CAACvG,kBAAkB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAACE,mBAAmB,EAAE3F,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC1D,IAAI,CAACsF,kBAAkB,GACrB,IAAI,CAACE,mBAAmB,CAAC3F,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC7CuS,UAAkC,KAChC;QACC/M,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEoJ,UAAU,CAACpJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACsK,kCAAkC,CAACD,UAAU,CAACrK,IAAI,CAAC;QAC9DkC,IAAI,EAAEmI,UAAU,CAACnI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEmO,UAAU,CAACnO,IAAI,IAAI,CAAC;QAC1BqO,QAAQ,EAAE;OACI,EACnB;;EAEP;EAEA;;;EAGQD,kCAAkCA,CACxCtK,IAAoB;IAEpB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAOpI,WAAW,CAAC8S,KAAK;MAC1B,KAAK,OAAO;QACV,OAAO9S,WAAW,CAAC+S,KAAK;MAC1B,KAAK,OAAO;QACV,OAAO/S,WAAW,CAACgT,KAAK;MAC1B,KAAK,MAAM;QACT,OAAOhT,WAAW,CAACiT,IAAI;MACzB;QACE,OAAOjT,WAAW,CAACiT,IAAI;;EAE7B;EAEA;;;EAGA5I,OAAOA,CAACjC,IAAY;IAClB,OAAOA,IAAI,EAAE8K,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;EAC5C;EAEA;;;;;EAKA1J,WAAWA,CAACpB,IAAY;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,aAAa;IAE/B,IAAIA,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,iBAAiB;IACtD,IAAI9K,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAClD,IAAItG,IAAI,CAACsG,QAAQ,CAAC,MAAM,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,UAAU,CAAC,EACpD,OAAO,kBAAkB;IAC3B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,wBAAwB;IACjC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,EACrD,OAAO,qBAAqB;IAE9B,OAAO,aAAa;EACtB;EAEA;;;;;EAKAnE,gBAAgBA,CAACnC,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAE3B,IAAIA,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI9K,IAAI,CAAC8K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC5C,IAAI9K,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;IACtC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,MAAM,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACzE,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,cAAc;IACvB,IAAItG,IAAI,CAACsG,QAAQ,CAAC,KAAK,CAAC,IAAItG,IAAI,CAACsG,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,SAAS;IAEzE,OAAO,SAAS;EAClB;EAEA;;;;;EAKAhF,cAAcA,CAACpF,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAM6O,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,aAAa,GAAG/O,IAAI;IAExB,OAAO+O,aAAa,IAAI,IAAI,IAAID,CAAC,GAAGD,KAAK,CAAC/N,MAAM,GAAG,CAAC,EAAE;MACpDiO,aAAa,IAAI,IAAI;MACrBD,CAAC,EAAE;;IAGL,OAAO,GAAGC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,CAAC,CAAC,EAAE;EAClD;EAEA;;;;EAIAhK,cAAcA,CAACC,GAAW;IACxB,IAAI,CAACA,GAAG,EAAE;IACVkK,MAAM,CAACC,IAAI,CAACnK,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEA;;;;EAIAc,kBAAkBA,CAACsI,UAAsB;IACvC,IAAI,CAACA,UAAU,EAAEpJ,GAAG,EAAE;IAEtB,MAAMoK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGnB,UAAU,CAACpJ,GAAG;IAC1BoK,IAAI,CAACI,QAAQ,GAAGpB,UAAU,CAACnI,IAAI,IAAI,YAAY;IAC/CmJ,IAAI,CAACxE,MAAM,GAAG,QAAQ;IACtByE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC;EAEAS,mBAAmBA,CAAC5F,YAA0B;IAC5C,IAAI,CAAC/H,UAAU,CAAC+H,YAAY,CAAC5I,EAAE,CAAC;EAClC;EAEA;;;;EAIA+B,kBAAkBA,CAACwJ,cAAsB;IACvC,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACvD,KAAK,GAAG,IAAIwD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,MAAMpB,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/DD,sBAAsB,CAACqE,GAAG,CAAClD,cAAc,CAAC;IAC1C,IAAI,CAACmD,0BAA0B,CAACtE,sBAAsB,CAAC;IAEvD,IAAI,CAACxC,cAAc,CAChB7F,kBAAkB,CAACwJ,cAAc,CAAC,CAClCjB,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGyC,MAAM,IAAI;QACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5B,IAAI,IAAI,CAAC/D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC5D,IAAI,CAAChB,KAAK,GAAG,IAAI;;;MAGvB,CAAC;MACDA,KAAK,EAAGgD,GAAG,IAAI;QACb,IAAI,CAAChD,KAAK,GAAGgD,GAAG;MAClB;KACD,CAAC;EACN;EAEA;;;EAGAzO,sBAAsBA,CAAA;IACpB,IAAI,CAACgM,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMH,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;MAE/DE,aAAa,CAAC4B,OAAO,CAAEvD,YAAY,IAAI;QACrCwB,sBAAsB,CAACqE,GAAG,CAAC7F,YAAY,CAAC5I,EAAE,CAAC;MAC7C,CAAC,CAAC;MAEF,IAAI,CAAC0O,0BAA0B,CAACtE,sBAAsB,CAAC;MAEvD,IAAI,CAACxC,cAAc,CAChBrL,sBAAsB,EAAE,CACxB+N,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;QACTC,IAAI,EAAGyC,MAAM,IAAI;UACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5B,IAAI,IAAI,CAAC/D,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAACyJ,QAAQ,CAAC,aAAa,CAAC,EAAE;cAC5D,IAAI,CAAChB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGgD,GAAG,IAAI;UACb,IAAI,CAAChD,KAAK,GAAGgD,GAAG;QAClB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEA9L,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC8I,KAAK,EAAEzI,OAAO,IAAI,wBAAwB;EACxD;EAEA;;;;EAIQ8K,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMsE,cAAc,GAAG7E,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MACrE,IAAI4E,cAAc,EAAE;QAClB,OAAO,IAAI1G,GAAG,CAAS2G,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC;;MAEpD,OAAO,IAAI1G,GAAG,EAAU;KACzB,CAAC,OAAOD,KAAK,EAAE;MACd,OAAO,IAAIC,GAAG,EAAU;;EAE5B;EAEA;;;;EAIQyG,0BAA0BA,CAACI,UAAuB;IACxD,IAAI;MACFhF,YAAY,CAAC+C,OAAO,CAClB,wBAAwB,EACxB+B,IAAI,CAACG,SAAS,CAACC,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC,CACvC;KACF,CAAC,OAAO9G,KAAK,EAAE;MACd;IAAA;EAEJ;EAEAkH,WAAWA,CAAA;IACT,IAAI,CAAC7G,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAAC8G,QAAQ,EAAE;EAC1B;EAEA;;;;;EAKAhO,eAAeA,CAACoK,cAAsB,EAAE6D,KAAY;IAClDA,KAAK,CAACnP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC1B,qBAAqB,CAACkM,GAAG,CAACc,cAAc,CAAC,EAAE;MAClD,IAAI,CAAChN,qBAAqB,CAAC8Q,MAAM,CAAC9D,cAAc,CAAC;KAClD,MAAM;MACL,IAAI,CAAChN,qBAAqB,CAACkQ,GAAG,CAAClD,cAAc,CAAC;;IAGhD;IACA,IAAI,CAAC+D,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC7Q,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;EAC7D;EAEA;;;;EAIApD,eAAeA,CAAC4T,KAAY;IAC1BA,KAAK,CAACnP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,CAACnE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACsH,sBAAsB,CAACkH,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,IAAI,CAACzO,WAAW,EAAE;QACpB;QACAyO,aAAa,CAAC4B,OAAO,CAAEvD,YAAY,IAAI;UACrC,IAAI,CAACrK,qBAAqB,CAACkQ,GAAG,CAAC7F,YAAY,CAAC5I,EAAE,CAAC;QACjD,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACzB,qBAAqB,CAACC,KAAK,EAAE;;MAGpC;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEA;;;EAGQ0Q,oBAAoBA,CAAA;IAC1B,IAAI,CAAClM,sBAAsB,CAACkH,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,CAACzO,WAAW,GACdyO,aAAa,CAAC7K,MAAM,GAAG,CAAC,IACxB,IAAI,CAACnB,qBAAqB,CAACK,IAAI,KAAK2L,aAAa,CAAC7K,MAAM;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGAtB,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACG,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;IAE3C,MAAM2Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1Q,qBAAqB,CAAC;IAE1D,IAAI,CAACgK,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMmB,oBAAoB,GAAGnB,aAAa,CAAC1P,MAAM,CAC9C+N,YAAY,IAAK,CAAC,IAAI,CAACrK,qBAAqB,CAACkM,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,CACnE;MAED,IAAI,CAAC6L,yBAAyB,CAACH,oBAAoB,CAAC;MACpD,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACxE,cAAc,CAChB4H,2BAA2B,CAACD,WAAW,CAAC,CACxCjF,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGyC,MAAM,IAAI;QACf;MAAA,CACD;MACD9D,KAAK,EAAGgD,GAAG,IAAI;QACb;MAAA;KAEH,CAAC;EACN;EAEA;;;EAGA/M,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACM,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;IAE3C,MAAM2Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1Q,qBAAqB,CAAC;IAE1D,IAAI,CAACgK,cAAc,CAAC+B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0O,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMmB,oBAAoB,GAAGnB,aAAa,CAAC/P,GAAG,CAAEoO,YAAY,IAC1D,IAAI,CAACrK,qBAAqB,CAACkM,GAAG,CAAC7B,YAAY,CAAC5I,EAAE,CAAC,GAC3C;QAAE,GAAG4I,YAAY;QAAE3G,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIuG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACnEhD,YAAY,CACjB;MAED,IAAI,CAACiD,yBAAyB,CAACH,oBAAoB,CAAC;MACpD,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACxE,cAAc,CAChB/G,UAAU,CAAC0O,WAAW,CAAC,CACvBjF,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGyC,MAAM,IAAI;QACf;MAAA,CACD;MACD9D,KAAK,EAAGgD,GAAG,IAAI;QACb;MAAA;KAEH,CAAC;EACN;EAEA;;;;;EAKA7I,UAAUA,CAACoJ,cAAsB;IAC/B,OAAO,IAAI,CAAChN,qBAAqB,CAACkM,GAAG,CAACc,cAAc,CAAC;EACvD;;;uBAx4BW7D,yBAAyB,EAAA5M,EAAA,CAAA2U,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7U,EAAA,CAAA2U,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA/U,EAAA,CAAA2U,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBrI,yBAAyB;MAAAsI,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;mBAAzBC,GAAA,CAAApN,QAAA,CAAA9H,MAAA,CAAAqO,MAAA,CAAuB;UAAA;;;;;;;;UCjCpCzO,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAY,SAAA,aAEM;UAENZ,EAAA,CAAAC,cAAA,aAAsE;UAGhED,EAAA,CAAAY,SAAA,WAAgC;UAChCZ,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UAGLX,EAAA,CAAA8B,UAAA,IAAAyT,wCAAA,mBA+DM;UAGNvV,EAAA,CAAA8B,UAAA,IAAA0T,wCAAA,kBAgCM;UACRxV,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAA8B,UAAA,KAAA2T,yCAAA,iBAGM;UAGNzV,EAAA,CAAA8B,UAAA,KAAA4T,yCAAA,kBAcM;UAGN1V,EAAA,CAAA8B,UAAA,KAAA6T,yCAAA,kBAYM;;UAGN3V,EAAA,CAAA8B,UAAA,KAAA8T,yCAAA,kBA6JM;;UACR5V,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAE,UAAA,mBAAA2V,yDAAA;YAAA,OAASP,GAAA,CAAAlD,qBAAA,EAAuB;UAAA,EAAC;UAEjCpS,EAAA,CAAAC,cAAA,eAA2E;UAAnCD,EAAA,CAAAE,UAAA,mBAAA4V,yDAAA1V,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAY,SAAA,aAAqC;UACrCZ,EAAA,CAAAqB,MAAA,6BACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,kBAAyE;UAAlCD,EAAA,CAAAE,UAAA,mBAAA6V,4DAAA;YAAA,OAAST,GAAA,CAAAlD,qBAAA,EAAuB;UAAA,EAAC;UACtEpS,EAAA,CAAAY,SAAA,aAA4B;UAC9BZ,EAAA,CAAAW,YAAA,EAAS;UAEXX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAA8B,UAAA,KAAAkU,yCAAA,iBAGM;UAENhW,EAAA,CAAA8B,UAAA,KAAAmU,yCAAA,kBAWM;UAENjW,EAAA,CAAA8B,UAAA,KAAAoU,yCAAA,kBA6DM;UACRlW,EAAA,CAAAW,YAAA,EAAM;UAKVX,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAE,UAAA,mBAAAiW,yDAAA;YAAA,OAASb,GAAA,CAAA3J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,EAAA,CAAAC,cAAA,eAA2E;UAAnCD,EAAA,CAAAE,UAAA,mBAAAkW,yDAAAhW,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAY,SAAA,aAAuC;UACvCZ,EAAA,CAAAqB,MAAA,yCACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,kBAGC;UADCD,EAAA,CAAAE,UAAA,mBAAAmW,4DAAA;YAAA,OAASf,GAAA,CAAA3J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,EAAA,CAAAY,SAAA,aAA4B;UAC9BZ,EAAA,CAAAW,YAAA,EAAS;UAEXX,EAAA,CAAA8B,UAAA,KAAAwU,yCAAA,oBAkNM;UACRtW,EAAA,CAAAW,YAAA,EAAM;;;UAvoBNX,EAAA,CAAA2C,WAAA,SAAA3C,EAAA,CAAAwC,WAAA,QAAA8S,GAAA,CAAA3H,WAAA,EAAkC;UAeD3N,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3R,gBAAA,CAAuB;UAkEL3D,EAAA,CAAAa,SAAA,GAAsB;UAAtBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAA3R,gBAAA,CAAsB;UAoCjE3D,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAA3P,OAAA,CAAa;UAMb3F,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAApI,KAAA,CAAW;UAkBdlN,EAAA,CAAAa,SAAA,GAA+C;UAA/Cb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3P,OAAA,KAAA3F,EAAA,CAAAwC,WAAA,SAAA8S,GAAA,CAAA5S,gBAAA,IAA+C;UAe/C1C,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAA3P,OAAA,IAAA3F,EAAA,CAAAwC,WAAA,SAAA8S,GAAA,CAAA5S,gBAAA,IAA8C;UAmKnD1C,EAAA,CAAAa,SAAA,GAAwD;UAAxDb,EAAA,CAAAuW,WAAA,YAAAjB,GAAA,CAAAlI,oBAAA,mBAAwD;UAc9CpN,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAAjI,kBAAA,CAAwB;UAM3BrN,EAAA,CAAAa,SAAA,GAA4D;UAA5Db,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAAjI,kBAAA,IAAAiI,GAAA,CAAApL,kBAAA,CAAAtF,MAAA,OAA4D;UAa5D5E,EAAA,CAAAa,SAAA,GAA0D;UAA1Db,EAAA,CAAAc,UAAA,UAAAwU,GAAA,CAAAjI,kBAAA,IAAAiI,GAAA,CAAApL,kBAAA,CAAAtF,MAAA,KAA0D;UAoEjE5E,EAAA,CAAAa,SAAA,GAAgE;UAAhEb,EAAA,CAAAuW,WAAA,YAAAjB,GAAA,CAAAhI,4BAAA,mBAAgE;UAgB1BtN,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAc,UAAA,SAAAwU,GAAA,CAAAlL,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}