{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction ProfileCompletionComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"bg-white\", ctx_r0.currentStep >= step_r9)(\"text-[#4f5fad]\", ctx_r0.currentStep >= step_r9)(\"border-white\", ctx_r0.currentStep >= step_r9)(\"border-opacity-50\", ctx_r0.currentStep < step_r9)(\"text-white\", ctx_r0.currentStep < step_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", step_r9, \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"dateOfBirth\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \" Basic Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"div\")(5, \"label\", 30);\n    i0.ɵɵtext(6, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 31);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_7_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ProfileCompletionComponent_div_25_p_8_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 30);\n    i0.ɵɵtext(11, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 33);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_12_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ProfileCompletionComponent_div_25_p_13_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\")(15, \"label\", 30);\n    i0.ɵɵtext(16, \" Date of Birth * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 34);\n    i0.ɵɵlistener(\"change\", function ProfileCompletionComponent_div_25_Template_input_change_17_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProfileCompletionComponent_div_25_p_18_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\")(20, \"label\", 30);\n    i0.ɵɵtext(21, \" Phone Number * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 35);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_22_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ProfileCompletionComponent_div_25_p_23_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"dateOfBirth\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"dateOfBirth\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"phoneNumber\"));\n  }\n}\nfunction ProfileCompletionComponent_div_26_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getFieldError(\"department\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_26_p_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getFieldError(\"bio\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \" Professional Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"div\")(5, \"label\", 30);\n    i0.ɵɵtext(6, \" Department * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 37);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_26_Template_input_input_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ProfileCompletionComponent_div_26_p_8_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 30);\n    i0.ɵɵtext(11, \" Position \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 38);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_26_Template_input_input_12_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.calculateProgress());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 30);\n    i0.ɵɵtext(15, \" Bio * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"textarea\", 39);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_26_Template_textarea_input_16_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ProfileCompletionComponent_div_26_p_17_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\")(19, \"label\", 30);\n    i0.ɵɵtext(20, \" Skills \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 40);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_26_Template_input_input_21_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 41);\n    i0.ɵɵtext(23, \" Separate skills with commas \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r2.isFieldInvalid(\"department\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFieldError(\"department\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r2.isFieldInvalid(\"bio\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFieldError(\"bio\"));\n  }\n}\nfunction ProfileCompletionComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \" Final Touches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"label\", 30);\n    i0.ɵɵtext(5, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"input\", 42);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_27_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.calculateProgress());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"label\", 30);\n    i0.ɵɵtext(9, \" Profile Picture \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 43)(11, \"div\", 44);\n    i0.ɵɵelement(12, \"img\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 46)(14, \"input\", 47);\n    i0.ɵɵlistener(\"change\", function ProfileCompletionComponent_div_27_Template_input_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 41);\n    i0.ɵɵtext(16, \" Upload a profile picture to help others recognize you \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewUrl || (ctx_r3.currentUser == null ? null : ctx_r3.currentUser.profileImage) || \"assets/images/default-profile.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileCompletionComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.previousStep());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.nextStep());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_35_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Complete Profile\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_35_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 54);\n    i0.ɵɵelement(2, \"circle\", 55)(3, \"path\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Completing... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵtemplate(1, ProfileCompletionComponent_button_35_span_1_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(2, ProfileCompletionComponent_button_35_span_2_Template, 5, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r6.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isLoading);\n  }\n}\nfunction ProfileCompletionComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.message);\n  }\n}\nfunction ProfileCompletionComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"p\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.error);\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3];\n};\nexport class ProfileCompletionComponent {\n  constructor(fb, authService, dataService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.dataService = dataService;\n    this.router = router;\n    this.currentUser = null;\n    this.progressPercentage = 0;\n    this.isLoading = false;\n    this.message = '';\n    this.error = '';\n    this.selectedFile = null;\n    this.previewUrl = null;\n    // Form steps for better UX\n    this.currentStep = 1;\n    this.totalSteps = 3;\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.calculateProgress();\n    this.prefillForm();\n  }\n  prefillForm() {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n  calculateProgress() {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  onSubmit() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n    // Convert FormData to regular object for the API\n    const profileData = {};\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        profileData[key] = skillsArray;\n      } else if (value) {\n        profileData[key] = value;\n      }\n    });\n    this.authService.completeProfile(profileData).subscribe({\n      next: response => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n        // Update current user in localStorage\n        const currentUser = this.authService.getCurrentUser();\n        if (currentUser) {\n          const updatedUser = {\n            ...currentUser,\n            ...response.user\n          };\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n        // Redirect based on user role after a short delay\n        setTimeout(() => {\n          const userRole = this.authService.getUserRole();\n          if (userRole === 'admin') {\n            this.router.navigate(['/admin/dashboard']);\n          } else {\n            this.router.navigate(['/dashboard']);\n          }\n        }, 2000);\n      },\n      error: err => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n  skipForNow() {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n\n  static {\n    this.ɵfac = function ProfileCompletionComponent_Factory(t) {\n      return new (t || ProfileCompletionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileCompletionComponent,\n      selectors: [[\"app-profile-completion\"]],\n      decls: 38,\n      vars: 19,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-[#f8fafc]\", \"to-[#e2e8f0]\", \"dark:from-[#0f172a]\", \"dark:to-[#1e293b]\", \"py-8\", \"px-4\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"text-center\", \"mb-8\"], [1, \"text-4xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"bg-clip-text\", \"text-transparent\", \"mb-4\"], [1, \"text-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-6\"], [1, \"max-w-md\", \"mx-auto\", \"mb-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-3\", \"overflow-hidden\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"space-x-4\"], [\"class\", \"flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all\", 3, \"bg-white\", \"text-[#4f5fad]\", \"border-white\", \"border-opacity-50\", \"text-white\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-white\", \"text-sm\"], [1, \"p-8\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", \"class\", \"px-6 py-3 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad] dark:border-[#6d78c9] rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"click\"], [\"type\", \"button\", \"class\", \"px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-center\", \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"transition-all\"], [1, \"space-y-6\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [\"type\", \"text\", \"formControlName\", \"department\", \"placeholder\", \"e.g., Computer Science, Marketing\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"type\", \"text\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Student, Professor, Developer\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"formControlName\", \"bio\", \"rows\", \"4\", \"placeholder\", \"Tell us about yourself, your interests, and goals...\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"resize-none\", 3, \"input\"], [\"type\", \"text\", \"formControlName\", \"skills\", \"placeholder\", \"e.g., JavaScript, Python, Project Management (comma separated)\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex-shrink-0\"], [\"alt\", \"Profile preview\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", 3, \"src\"], [1, \"flex-1\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"dark:hover:text-white\", \"transition-all\", 3, \"click\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n      template: function ProfileCompletionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \" Complete Your Profile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \" Help us get to know you better! Complete your profile to unlock all features. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"span\", 7);\n          i0.ɵɵtext(10, \"Progress\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 8);\n          i0.ɵɵelement(14, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\", 10);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\", 12)(19, \"div\", 13)(20, \"div\", 14);\n          i0.ɵɵtemplate(21, ProfileCompletionComponent_div_21_Template, 2, 11, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 16);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"form\", 17);\n          i0.ɵɵlistener(\"ngSubmit\", function ProfileCompletionComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(25, ProfileCompletionComponent_div_25_Template, 24, 12, \"div\", 18);\n          i0.ɵɵtemplate(26, ProfileCompletionComponent_div_26_Template, 24, 6, \"div\", 18);\n          i0.ɵɵtemplate(27, ProfileCompletionComponent_div_27_Template, 17, 1, \"div\", 18);\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\");\n          i0.ɵɵtemplate(30, ProfileCompletionComponent_button_30_Template, 2, 0, \"button\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 14)(32, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_Template_button_click_32_listener() {\n            return ctx.skipForNow();\n          });\n          i0.ɵɵtext(33, \" Skip for now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, ProfileCompletionComponent_button_34_Template, 2, 0, \"button\", 22);\n          i0.ɵɵtemplate(35, ProfileCompletionComponent_button_35_Template, 3, 3, \"button\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, ProfileCompletionComponent_div_36_Template, 3, 1, \"div\", 24);\n          i0.ɵɵtemplate(37, ProfileCompletionComponent_div_37_Template, 3, 1, \"div\", 25);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\"\", ctx.progressPercentage, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.progressPercentage, \"%\")(\"background-color\", ctx.getProgressColor());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getMotivationalMessage(), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(18, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" Step \", ctx.currentStep, \" of \", ctx.totalSteps, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.profileForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < ctx.totalSteps);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === ctx.totalSteps);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  transition: width 0.5s ease-in-out;\\n}\\n\\n\\n\\n.step-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%]::-webkit-file-upload-button {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  margin-right: 12px;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n}\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%]::-webkit-file-upload-button:hover {\\n  background: linear-gradient(135deg, #3d4a85, #6a1b9a);\\n}\\n\\n\\n\\ninput[_ngcontent-%COMP%]:focus, textarea[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);\\n}\\n\\n\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #4f5fad;\\n  border-radius: 3px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #3d4a85;\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #2a2a2a;\\n}\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #6d78c9;\\n}\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #5a67b8;\\n}\\n\\n\\n\\n.step-indicator[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.step-indicator.active[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n\\n\\n.btn-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-gradient[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #3d4a85, #6a1b9a);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.animate-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n\\n\\n.profile-preview[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.profile-preview[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2);\\n}\\n\\n\\n\\n.field-error[_ngcontent-%COMP%] {\\n  border-color: #ef4444 !important;\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;\\n}\\n\\n.field-success[_ngcontent-%COMP%] {\\n  border-color: #22c55e !important;\\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;\\n}\\n\\n\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f5fad, #7826b5, #4f5fad);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_progressGradient 2s ease infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressGradient {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .step-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .grid-cols-2[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .flex-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .space-x-4[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n\\ninput[type=\\\"checkbox\\\"][_ngcontent-%COMP%], input[type=\\\"radio\\\"][_ngcontent-%COMP%] {\\n  accent-color: #4f5fad;\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: #1f2937;\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  font-size: 0.875rem;\\n  white-space: nowrap;\\n  z-index: 10;\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.form-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n.success-checkmark[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_checkmark 0.5s ease-in-out;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "currentStep", "step_r9", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r10", "getFieldError", "ctx_r11", "ctx_r12", "ctx_r13", "ɵɵlistener", "ProfileCompletionComponent_div_25_Template_input_input_7_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "calculateProgress", "ɵɵtemplate", "ProfileCompletionComponent_div_25_p_8_Template", "ProfileCompletionComponent_div_25_Template_input_input_12_listener", "ctx_r16", "ProfileCompletionComponent_div_25_p_13_Template", "ProfileCompletionComponent_div_25_Template_input_change_17_listener", "ctx_r17", "ProfileCompletionComponent_div_25_p_18_Template", "ProfileCompletionComponent_div_25_Template_input_input_22_listener", "ctx_r18", "ProfileCompletionComponent_div_25_p_23_Template", "ctx_r1", "isFieldInvalid", "ɵɵproperty", "ctx_r19", "ctx_r20", "ProfileCompletionComponent_div_26_Template_input_input_7_listener", "_r22", "ctx_r21", "ProfileCompletionComponent_div_26_p_8_Template", "ProfileCompletionComponent_div_26_Template_input_input_12_listener", "ctx_r23", "ProfileCompletionComponent_div_26_Template_textarea_input_16_listener", "ctx_r24", "ProfileCompletionComponent_div_26_p_17_Template", "ProfileCompletionComponent_div_26_Template_input_input_21_listener", "ctx_r25", "ctx_r2", "ProfileCompletionComponent_div_27_Template_input_input_6_listener", "_r27", "ctx_r26", "ɵɵelement", "ProfileCompletionComponent_div_27_Template_input_change_14_listener", "$event", "ctx_r28", "onFileSelected", "ctx_r3", "previewUrl", "currentUser", "profileImage", "ɵɵsanitizeUrl", "ProfileCompletionComponent_button_30_Template_button_click_0_listener", "_r30", "ctx_r29", "previousStep", "ProfileCompletionComponent_button_34_Template_button_click_0_listener", "_r32", "ctx_r31", "nextStep", "ɵɵnamespaceSVG", "ProfileCompletionComponent_button_35_span_1_Template", "ProfileCompletionComponent_button_35_span_2_Template", "ctx_r6", "isLoading", "ɵɵtextInterpolate", "ctx_r7", "message", "ctx_r8", "error", "ProfileCompletionComponent", "constructor", "fb", "authService", "dataService", "router", "progressPercentage", "selectedFile", "totalSteps", "profileForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "dateOfBirth", "phoneNumber", "pattern", "department", "position", "bio", "address", "skills", "ngOnInit", "getCurrentUser", "navigate", "isProfileComplete", "prefillForm", "patchValue", "join", "formValues", "value", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "toString", "trim", "hasProfileImage", "requiredPercentage", "length", "optionalPercentage", "imagePercentage", "Math", "round", "event", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "onSubmit", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "profileData", "completeProfile", "subscribe", "next", "response", "updatedUser", "user", "localStorage", "setItem", "setTimeout", "userRole", "getUserRole", "err", "skipFor<PERSON>ow", "confirm", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "getMotivationalMessage", "getProgressColor", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "ProfileCompletionComponent_Template", "rf", "ctx", "ProfileCompletionComponent_div_21_Template", "ProfileCompletionComponent_Template_form_ngSubmit_24_listener", "ProfileCompletionComponent_div_25_Template", "ProfileCompletionComponent_div_26_Template", "ProfileCompletionComponent_div_27_Template", "ProfileCompletionComponent_button_30_Template", "ProfileCompletionComponent_Template_button_click_32_listener", "ProfileCompletionComponent_button_34_Template", "ProfileCompletionComponent_button_35_Template", "ProfileCompletionComponent_div_36_Template", "ProfileCompletionComponent_div_37_Template", "ɵɵstyleProp", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { DataService } from 'src/app/services/data.service';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-profile-completion',\n  templateUrl: './profile-completion.component.html',\n  styleUrls: ['./profile-completion.component.css']\n})\nexport class ProfileCompletionComponent implements OnInit {\n  profileForm: FormGroup;\n  currentUser: User | null = null;\n  progressPercentage: number = 0;\n  isLoading = false;\n  message = '';\n  error = '';\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n\n  // Form steps for better UX\n  currentStep = 1;\n  totalSteps = 3;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthuserService,\n    private dataService: DataService,\n    private router: Router\n  ) {\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n\n    this.calculateProgress();\n    this.prefillForm();\n  }\n\n  prefillForm(): void {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n\n  calculateProgress(): void {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    \n    let completedRequired = 0;\n    let completedOptional = 0;\n\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || (this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png')) {\n      hasProfileImage = 1;\n    }\n\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\n    const imagePercentage = hasProfileImage * 10;\n\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      \n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  nextStep(): void {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  onSubmit(): void {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n\n    const formData = new FormData();\n    \n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n\n    // Convert FormData to regular object for the API\n    const profileData: any = {};\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\n        profileData[key] = skillsArray;\n      } else if (value) {\n        profileData[key] = value;\n      }\n    });\n\n    this.authService.completeProfile(profileData).subscribe({\n      next: (response: any) => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n\n        // Update current user in localStorage\n        const currentUser = this.authService.getCurrentUser();\n        if (currentUser) {\n          const updatedUser = { ...currentUser, ...response.user };\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n\n        // Redirect based on user role after a short delay\n        setTimeout(() => {\n          const userRole = this.authService.getUserRole();\n          if (userRole === 'admin') {\n            this.router.navigate(['/admin/dashboard']);\n          } else {\n            this.router.navigate(['/dashboard']);\n          }\n        }, 2000);\n      },\n      error: (err) => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n\n  skipForNow(): void {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n\n  // Helper methods for template\n  getFieldError(fieldName: string): string {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n\n  getMotivationalMessage(): string {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n\n  getProgressColor(): string {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n}\n", "<div class=\"min-h-screen bg-gradient-to-br from-[#f8fafc] to-[#e2e8f0] dark:from-[#0f172a] dark:to-[#1e293b] py-8 px-4\">\n  <div class=\"max-w-4xl mx-auto\">\n    <!-- Header -->\n    <div class=\"text-center mb-8\">\n      <h1 class=\"text-4xl font-bold bg-gradient-to-r from-[#4f5fad] to-[#7826b5] bg-clip-text text-transparent mb-4\">\n        Complete Your Profile\n      </h1>\n      <p class=\"text-lg text-[#6d6870] dark:text-[#a0a0a0] mb-6\">\n        Help us get to know you better! Complete your profile to unlock all features.\n      </p>\n\n      <!-- Progress Bar -->\n      <div class=\"max-w-md mx-auto mb-6\">\n        <div class=\"flex justify-between items-center mb-2\">\n          <span class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9]\">Progress</span>\n          <span class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9]\">{{ progressPercentage }}%</span>\n        </div>\n        <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-3 overflow-hidden\">\n          <div\n            class=\"h-full rounded-full transition-all duration-500 ease-out\"\n            [style.width.%]=\"progressPercentage\"\n            [style.background-color]=\"getProgressColor()\">\n          </div>\n        </div>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n          {{ getMotivationalMessage() }}\n        </p>\n      </div>\n    </div>\n\n    <!-- Main Form Card -->\n    <div class=\"bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-xl border border-[#edf1f4] dark:border-[#2a2a2a] overflow-hidden\">\n      <!-- Step Indicator -->\n      <div class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] p-6\">\n        <div class=\"flex justify-between items-center\">\n          <div class=\"flex space-x-4\">\n            <div\n              *ngFor=\"let step of [1, 2, 3]\"\n              class=\"flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all\"\n              [class.bg-white]=\"currentStep >= step\"\n              [class.text-[#4f5fad]]=\"currentStep >= step\"\n              [class.border-white]=\"currentStep >= step\"\n              [class.border-opacity-50]=\"currentStep < step\"\n              [class.text-white]=\"currentStep < step\">\n              {{ step }}\n            </div>\n          </div>\n          <div class=\"text-white text-sm\">\n            Step {{ currentStep }} of {{ totalSteps }}\n          </div>\n        </div>\n      </div>\n\n      <!-- Form Content -->\n      <form [formGroup]=\"profileForm\" (ngSubmit)=\"onSubmit()\" class=\"p-8\">\n        <!-- Step 1: Basic Information -->\n        <div *ngIf=\"currentStep === 1\" class=\"space-y-6\">\n          <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-4\">\n            Basic Information\n          </h3>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <!-- First Name -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"firstName\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('firstName')\"\n                placeholder=\"Enter your first name\">\n              <p *ngIf=\"getFieldError('firstName')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('firstName') }}\n              </p>\n            </div>\n\n            <!-- Last Name -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"lastName\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('lastName')\"\n                placeholder=\"Enter your last name\">\n              <p *ngIf=\"getFieldError('lastName')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('lastName') }}\n              </p>\n            </div>\n\n            <!-- Date of Birth -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Date of Birth *\n              </label>\n              <input\n                type=\"date\"\n                formControlName=\"dateOfBirth\"\n                (change)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('dateOfBirth')\">\n              <p *ngIf=\"getFieldError('dateOfBirth')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('dateOfBirth') }}\n              </p>\n            </div>\n\n            <!-- Phone Number -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                formControlName=\"phoneNumber\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('phoneNumber')\"\n                placeholder=\"Enter your phone number\">\n              <p *ngIf=\"getFieldError('phoneNumber')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('phoneNumber') }}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 2: Professional Information -->\n        <div *ngIf=\"currentStep === 2\" class=\"space-y-6\">\n          <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-4\">\n            Professional Information\n          </h3>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <!-- Department -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Department *\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"department\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('department')\"\n                placeholder=\"e.g., Computer Science, Marketing\">\n              <p *ngIf=\"getFieldError('department')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('department') }}\n              </p>\n            </div>\n\n            <!-- Position -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Position\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"position\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                placeholder=\"e.g., Student, Professor, Developer\">\n            </div>\n          </div>\n\n          <!-- Bio -->\n          <div>\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n              Bio *\n            </label>\n            <textarea\n              formControlName=\"bio\"\n              (input)=\"calculateProgress()\"\n              rows=\"4\"\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all resize-none\"\n              [class.border-red-500]=\"isFieldInvalid('bio')\"\n              placeholder=\"Tell us about yourself, your interests, and goals...\"></textarea>\n            <p *ngIf=\"getFieldError('bio')\" class=\"text-red-500 text-sm mt-1\">\n              {{ getFieldError('bio') }}\n            </p>\n          </div>\n\n          <!-- Skills -->\n          <div>\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n              Skills\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"skills\"\n              (input)=\"calculateProgress()\"\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n              placeholder=\"e.g., JavaScript, Python, Project Management (comma separated)\">\n            <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\n              Separate skills with commas\n            </p>\n          </div>\n        </div>\n\n        <!-- Step 3: Additional Information & Profile Picture -->\n        <div *ngIf=\"currentStep === 3\" class=\"space-y-6\">\n          <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-4\">\n            Final Touches\n          </h3>\n\n          <!-- Address -->\n          <div>\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n              Address\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"address\"\n              (input)=\"calculateProgress()\"\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n              placeholder=\"Enter your address\">\n          </div>\n\n          <!-- Profile Picture -->\n          <div>\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n              Profile Picture\n            </label>\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"flex-shrink-0\">\n                <img\n                  [src]=\"previewUrl || currentUser?.profileImage || 'assets/images/default-profile.png'\"\n                  alt=\"Profile preview\"\n                  class=\"w-20 h-20 rounded-full object-cover border-2 border-[#4f5fad] dark:border-[#6d78c9]\">\n              </div>\n              <div class=\"flex-1\">\n                <input\n                  type=\"file\"\n                  (change)=\"onFileSelected($event)\"\n                  accept=\"image/*\"\n                  class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\n                <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\n                  Upload a profile picture to help others recognize you\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Navigation Buttons -->\n        <div class=\"flex justify-between items-center mt-8 pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\n          <div>\n            <button\n              *ngIf=\"currentStep > 1\"\n              type=\"button\"\n              (click)=\"previousStep()\"\n              class=\"px-6 py-3 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad] dark:border-[#6d78c9] rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all\">\n              Previous\n            </button>\n          </div>\n\n          <div class=\"flex space-x-4\">\n            <button\n              type=\"button\"\n              (click)=\"skipForNow()\"\n              class=\"px-6 py-3 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\">\n              Skip for now\n            </button>\n\n            <button\n              *ngIf=\"currentStep < totalSteps\"\n              type=\"button\"\n              (click)=\"nextStep()\"\n              class=\"px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all\">\n              Next\n            </button>\n\n            <button\n              *ngIf=\"currentStep === totalSteps\"\n              type=\"submit\"\n              [disabled]=\"isLoading\"\n              class=\"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\">\n              <span *ngIf=\"!isLoading\">Complete Profile</span>\n              <span *ngIf=\"isLoading\" class=\"flex items-center\">\n                <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Completing...\n              </span>\n            </button>\n          </div>\n        </div>\n\n        <!-- Messages -->\n        <div *ngIf=\"message\" class=\"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\n          <p class=\"text-green-800 dark:text-green-200\">{{ message }}</p>\n        </div>\n\n        <div *ngIf=\"error\" class=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICmCvDC,EAAA,CAAAC,cAAA,cAO0C;IACxCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IANJH,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,WAAA,IAAAC,OAAA,CAAsC,mBAAAF,MAAA,CAAAC,WAAA,IAAAC,OAAA,kBAAAF,MAAA,CAAAC,WAAA,IAAAC,OAAA,uBAAAF,MAAA,CAAAC,WAAA,GAAAC,OAAA,gBAAAF,MAAA,CAAAC,WAAA,GAAAC,OAAA;IAKtCP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAF,OAAA,MACF;;;;;IA6BEP,EAAA,CAAAC,cAAA,YAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,OAAA,CAAAC,aAAA,mBACF;;;;;IAeAX,EAAA,CAAAC,cAAA,YAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAG,OAAA,CAAAD,aAAA,kBACF;;;;;IAcAX,EAAA,CAAAC,cAAA,YAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAI,OAAA,CAAAF,aAAA,qBACF;;;;;IAeAX,EAAA,CAAAC,cAAA,YAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAK,OAAA,CAAAH,aAAA,qBACF;;;;;;IAtENX,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAmD;IAI7CD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAMsC;IAHpCD,EAAA,CAAAe,UAAA,mBAAAC,kEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAMsC;IACtCH,EAAA,CAAAuB,UAAA,IAAAC,8CAAA,gBAEI;IACNxB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAMqC;IAHnCD,EAAA,CAAAe,UAAA,mBAAAU,mEAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAK,OAAA,CAAAJ,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAMqC;IACrCH,EAAA,CAAAuB,UAAA,KAAAI,+CAAA,gBAEI;IACN3B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAKyD;IAFvDD,EAAA,CAAAe,UAAA,oBAAAa,oEAAA;MAAA5B,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAA7B,EAAA,CAAAoB,aAAA;MAAA,OAAUpB,EAAA,CAAAqB,WAAA,CAAAQ,OAAA,CAAAP,iBAAA,EAAmB;IAAA,EAAC;IAHhCtB,EAAA,CAAAG,YAAA,EAKyD;IACzDH,EAAA,CAAAuB,UAAA,KAAAO,+CAAA,gBAEI;IACN9B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAMwC;IAHtCD,EAAA,CAAAe,UAAA,mBAAAgB,mEAAA;MAAA/B,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAc,OAAA,GAAAhC,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAW,OAAA,CAAAV,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAMwC;IACxCH,EAAA,CAAAuB,UAAA,KAAAU,+CAAA,gBAEI;IACNjC,EAAA,CAAAG,YAAA,EAAM;;;;IAvDFH,EAAA,CAAAQ,SAAA,GAAoD;IAApDR,EAAA,CAAAI,WAAA,mBAAA8B,MAAA,CAAAC,cAAA,cAAoD;IAElDnC,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAoC,UAAA,SAAAF,MAAA,CAAAvB,aAAA,cAAgC;IAelCX,EAAA,CAAAQ,SAAA,GAAmD;IAAnDR,EAAA,CAAAI,WAAA,mBAAA8B,MAAA,CAAAC,cAAA,aAAmD;IAEjDnC,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAoC,UAAA,SAAAF,MAAA,CAAAvB,aAAA,aAA+B;IAejCX,EAAA,CAAAQ,SAAA,GAAsD;IAAtDR,EAAA,CAAAI,WAAA,mBAAA8B,MAAA,CAAAC,cAAA,gBAAsD;IACpDnC,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAoC,UAAA,SAAAF,MAAA,CAAAvB,aAAA,gBAAkC;IAepCX,EAAA,CAAAQ,SAAA,GAAsD;IAAtDR,EAAA,CAAAI,WAAA,mBAAA8B,MAAA,CAAAC,cAAA,gBAAsD;IAEpDnC,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAoC,UAAA,SAAAF,MAAA,CAAAvB,aAAA,gBAAkC;;;;;IA0BtCX,EAAA,CAAAC,cAAA,YAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAA4B,OAAA,CAAA1B,aAAA,oBACF;;;;;IA6BFX,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAA6B,OAAA,CAAA3B,aAAA,aACF;;;;;;IAnDJX,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAmD;IAI7CD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAMkD;IAHhDD,EAAA,CAAAe,UAAA,mBAAAwB,kEAAA;MAAAvC,EAAA,CAAAiB,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAoB,OAAA,CAAAnB,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAMkD;IAClDH,EAAA,CAAAuB,UAAA,IAAAmB,8CAAA,gBAEI;IACN1C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAKoD;IAFlDD,EAAA,CAAAe,UAAA,mBAAA4B,mEAAA;MAAA3C,EAAA,CAAAiB,aAAA,CAAAuB,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAuB,OAAA,CAAAtB,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAKoD;IAKxDH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAMqE;IAJnED,EAAA,CAAAe,UAAA,mBAAA8B,sEAAA;MAAA7C,EAAA,CAAAiB,aAAA,CAAAuB,IAAA;MAAA,MAAAM,OAAA,GAAA9C,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAyB,OAAA,CAAAxB,iBAAA,EAAmB;IAAA,EAAC;IAIsCtB,EAAA,CAAAG,YAAA,EAAW;IAChFH,EAAA,CAAAuB,UAAA,KAAAwB,+CAAA,gBAEI;IACN/C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAK+E;IAF7ED,EAAA,CAAAe,UAAA,mBAAAiC,mEAAA;MAAAhD,EAAA,CAAAiB,aAAA,CAAAuB,IAAA;MAAA,MAAAS,OAAA,GAAAjD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA4B,OAAA,CAAA3B,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAK+E;IAC/EH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAnDAH,EAAA,CAAAQ,SAAA,GAAqD;IAArDR,EAAA,CAAAI,WAAA,mBAAA8C,MAAA,CAAAf,cAAA,eAAqD;IAEnDnC,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAoC,UAAA,SAAAc,MAAA,CAAAvC,aAAA,eAAiC;IA6BrCX,EAAA,CAAAQ,SAAA,GAA8C;IAA9CR,EAAA,CAAAI,WAAA,mBAAA8C,MAAA,CAAAf,cAAA,QAA8C;IAE5CnC,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAoC,UAAA,SAAAc,MAAA,CAAAvC,aAAA,QAA0B;;;;;;IAuBlCX,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAKmC;IAFjCD,EAAA,CAAAe,UAAA,mBAAAoC,kEAAA;MAAAnD,EAAA,CAAAiB,aAAA,CAAAmC,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAgC,OAAA,CAAA/B,iBAAA,EAAmB;IAAA,EAAC;IAH/BtB,EAAA,CAAAG,YAAA,EAKmC;IAIrCH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAsD,SAAA,eAG8F;IAChGtD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAoB;IAGhBD,EAAA,CAAAe,UAAA,oBAAAwC,oEAAAC,MAAA;MAAAxD,EAAA,CAAAiB,aAAA,CAAAmC,IAAA;MAAA,MAAAK,OAAA,GAAAzD,EAAA,CAAAoB,aAAA;MAAA,OAAUpB,EAAA,CAAAqB,WAAA,CAAAoC,OAAA,CAAAC,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAFnCxD,EAAA,CAAAG,YAAA,EAIyS;IACzSH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,MAAA,+DACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAZFH,EAAA,CAAAQ,SAAA,IAAsF;IAAtFR,EAAA,CAAAoC,UAAA,QAAAuB,MAAA,CAAAC,UAAA,KAAAD,MAAA,CAAAE,WAAA,kBAAAF,MAAA,CAAAE,WAAA,CAAAC,YAAA,0CAAA9D,EAAA,CAAA+D,aAAA,CAAsF;;;;;;IAqB5F/D,EAAA,CAAAC,cAAA,iBAIiN;IAD/MD,EAAA,CAAAe,UAAA,mBAAAiD,sEAAA;MAAAhE,EAAA,CAAAiB,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA6C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExBnE,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAWTH,EAAA,CAAAC,cAAA,iBAI8I;IAD5ID,EAAA,CAAAe,UAAA,mBAAAqD,sEAAA;MAAApE,EAAA,CAAAiB,aAAA,CAAAoD,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAiD,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAEpBvE,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAOPH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAwE,cAAA,EAA2H;IAA3HxE,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAsD,SAAA,iBAAkG;IAEpGtD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAZTH,EAAA,CAAAC,cAAA,iBAI8L;IAC5LD,EAAA,CAAAuB,UAAA,IAAAkD,oDAAA,mBAAgD;IAChDzE,EAAA,CAAAuB,UAAA,IAAAmD,oDAAA,mBAMO;IACT1E,EAAA,CAAAG,YAAA,EAAS;;;;IAVPH,EAAA,CAAAoC,UAAA,aAAAuC,MAAA,CAAAC,SAAA,CAAsB;IAEf5E,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAoC,UAAA,UAAAuC,MAAA,CAAAC,SAAA,CAAgB;IAChB5E,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAoC,UAAA,SAAAuC,MAAA,CAAAC,SAAA,CAAe;;;;;IAY5B5E,EAAA,CAAAC,cAAA,cAAgI;IAChFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAjBH,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAA6E,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAG7D/E,EAAA,CAAAC,cAAA,cAAsH;IAC1ED,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAA6E,iBAAA,CAAAG,MAAA,CAAAC,KAAA,CAAW;;;;;;AD/R/D,OAAM,MAAOC,0BAA0B;EAcrCC,YACUC,EAAe,EACfC,WAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAhBhB,KAAA1B,WAAW,GAAgB,IAAI;IAC/B,KAAA2B,kBAAkB,GAAW,CAAC;IAC9B,KAAAZ,SAAS,GAAG,KAAK;IACjB,KAAAG,OAAO,GAAG,EAAE;IACZ,KAAAE,KAAK,GAAG,EAAE;IACV,KAAAQ,YAAY,GAAgB,IAAI;IAChC,KAAA7B,UAAU,GAAgC,IAAI;IAE9C;IACA,KAAAtD,WAAW,GAAG,CAAC;IACf,KAAAoF,UAAU,GAAG,CAAC;IAQZ,IAAI,CAACC,WAAW,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC/BC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,WAAW,EAAE,CAAC,EAAE,EAAElG,UAAU,CAAC+F,QAAQ,CAAC;MACtCI,WAAW,EAAE,CAAC,EAAE,EAAE,CAACnG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACoG,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC/EC,UAAU,EAAE,CAAC,EAAE,EAAErG,UAAU,CAAC+F,QAAQ,CAAC;MACrCO,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACvG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACgG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1DQ,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC5C,WAAW,GAAG,IAAI,CAACwB,WAAW,CAACqB,cAAc,EAAE;IACpD,IAAI,CAAC,IAAI,CAAC7C,WAAW,EAAE;MACrB,IAAI,CAAC0B,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,IAAI,CAAC9C,WAAW,CAAC+C,iBAAiB,EAAE;MACtC,IAAI,CAACrB,MAAM,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAACrF,iBAAiB,EAAE;IACxB,IAAI,CAACuF,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChD,WAAW,EAAE;MACpB,IAAI,CAAC8B,WAAW,CAACmB,UAAU,CAAC;QAC1BjB,SAAS,EAAE,IAAI,CAAChC,WAAW,CAACgC,SAAS,IAAI,EAAE;QAC3CG,QAAQ,EAAE,IAAI,CAACnC,WAAW,CAACmC,QAAQ,IAAI,EAAE;QACzCC,WAAW,EAAE,IAAI,CAACpC,WAAW,CAACoC,WAAW,IAAI,EAAE;QAC/CC,WAAW,EAAE,IAAI,CAACrC,WAAW,CAACqC,WAAW,IAAI,EAAE;QAC/CE,UAAU,EAAE,IAAI,CAACvC,WAAW,CAACuC,UAAU,IAAI,EAAE;QAC7CC,QAAQ,EAAE,IAAI,CAACxC,WAAW,CAACwC,QAAQ,IAAI,EAAE;QACzCC,GAAG,EAAE,IAAI,CAACzC,WAAW,CAACyC,GAAG,IAAI,EAAE;QAC/BC,OAAO,EAAE,IAAI,CAAC1C,WAAW,CAAC0C,OAAO,IAAI,EAAE;QACvCC,MAAM,EAAE,IAAI,CAAC3C,WAAW,CAAC2C,MAAM,EAAEO,IAAI,CAAC,IAAI,CAAC,IAAI;OAChD,CAAC;;EAEN;EAEAzF,iBAAiBA,CAAA;IACf,MAAM0F,UAAU,GAAG,IAAI,CAACrB,WAAW,CAACsB,KAAK;IACzC,MAAMC,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACnG,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEL,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAACjC,YAAY,IAAK,IAAI,CAAC5B,WAAW,EAAEC,YAAY,IAAI,IAAI,CAACD,WAAW,CAACC,YAAY,KAAK,qBAAsB,EAAE;MACpH4D,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAMC,kBAAkB,GAAIP,iBAAiB,GAAGF,cAAc,CAACU,MAAM,GAAI,EAAE;IAC3E,MAAMC,kBAAkB,GAAIR,iBAAiB,GAAGF,cAAc,CAACS,MAAM,GAAI,EAAE;IAC3E,MAAME,eAAe,GAAGJ,eAAe,GAAG,EAAE;IAE5C,IAAI,CAAClC,kBAAkB,GAAGuC,IAAI,CAACC,KAAK,CAACL,kBAAkB,GAAGE,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEApE,cAAcA,CAACuE,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACzC,YAAY,GAAGyC,IAAI;MAExB;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAAC3E,UAAU,GAAGyE,MAAM,CAACG,MAAM;QAC/B,IAAI,CAAClH,iBAAiB,EAAE;MAC1B,CAAC;MACD+G,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAEA3D,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjE,WAAW,GAAG,IAAI,CAACoF,UAAU,EAAE;MACtC,IAAI,CAACpF,WAAW,EAAE;;EAEtB;EAEA6D,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC7D,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAoI,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/C,WAAW,CAACgD,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAChE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,KAAK,GAAG,EAAE;IACf,IAAI,CAACF,OAAO,GAAG,EAAE;IAEjB,MAAM8D,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrD,WAAW,CAACsB,KAAK,CAAC,CAACK,OAAO,CAAC2B,GAAG,IAAG;MAChD,MAAMhC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAACgC,GAAG,CAAC;MACzC,IAAIA,GAAG,KAAK,QAAQ,IAAIhC,KAAK,EAAE;QAC7B;QACA,MAAMiC,WAAW,GAAGjC,KAAK,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAC5B,IAAI,EAAE,CAAC,CAAC6B,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAIjC,KAAK,EAAE;QAChB4B,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEhC,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACxB,YAAY,EAAE;MACrBoD,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC9D,YAAY,CAAC;;IAG7C;IACA,MAAMiE,WAAW,GAAQ,EAAE;IAC3BX,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrD,WAAW,CAACsB,KAAK,CAAC,CAACK,OAAO,CAAC2B,GAAG,IAAG;MAChD,MAAMhC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAACgC,GAAG,CAAC;MACzC,IAAIA,GAAG,KAAK,QAAQ,IAAIhC,KAAK,EAAE;QAC7B;QACA,MAAMiC,WAAW,GAAGjC,KAAK,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAC5B,IAAI,EAAE,CAAC,CAAC6B,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GK,WAAW,CAACT,GAAG,CAAC,GAAGC,WAAW;OAC/B,MAAM,IAAIjC,KAAK,EAAE;QAChByC,WAAW,CAACT,GAAG,CAAC,GAAGhC,KAAK;;IAE5B,CAAC,CAAC;IAEF,IAAI,CAAC5B,WAAW,CAACsE,eAAe,CAACD,WAAW,CAAC,CAACE,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAClF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACG,OAAO,GAAG,iCAAiC;QAEhD;QACA,MAAMlB,WAAW,GAAG,IAAI,CAACwB,WAAW,CAACqB,cAAc,EAAE;QACrD,IAAI7C,WAAW,EAAE;UACf,MAAMkG,WAAW,GAAG;YAAE,GAAGlG,WAAW;YAAE,GAAGiG,QAAQ,CAACE;UAAI,CAAE;UACxDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACC,SAAS,CAACM,WAAW,CAAC,CAAC;;QAG3D;QACAI,UAAU,CAAC,MAAK;UACd,MAAMC,QAAQ,GAAG,IAAI,CAAC/E,WAAW,CAACgF,WAAW,EAAE;UAC/C,IAAID,QAAQ,KAAK,OAAO,EAAE;YACxB,IAAI,CAAC7E,MAAM,CAACoB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;WAC3C,MAAM;YACL,IAAI,CAACpB,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;QAExC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD1B,KAAK,EAAGqF,GAAG,IAAI;QACb,IAAI,CAAC1F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACK,KAAK,GAAGqF,GAAG,CAACrF,KAAK,EAAEF,OAAO,IAAI,kDAAkD;MACvF;KACD,CAAC;EACJ;EAEAwF,UAAUA,CAAA;IACR;IACA,IAAIC,OAAO,CAAC,qGAAqG,CAAC,EAAE;MAClH,IAAI,CAACjF,MAAM,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;;EAE/B;EAEQiC,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrD,WAAW,CAAC8E,QAAQ,CAAC,CAACnD,OAAO,CAAC2B,GAAG,IAAG;MACnD,IAAI,CAACtD,WAAW,CAAC+E,GAAG,CAACzB,GAAG,CAAC,EAAE0B,aAAa,EAAE;IAC5C,CAAC,CAAC;EACJ;EAEA;EACAhK,aAAaA,CAACiK,SAAiB;IAC7B,MAAMrD,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC+E,GAAG,CAACE,SAAS,CAAC;IAC7C,IAAIrD,KAAK,EAAEsD,MAAM,IAAItD,KAAK,CAACuD,OAAO,EAAE;MAClC,IAAIvD,KAAK,CAACsD,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAIrD,KAAK,CAACsD,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAIrD,KAAK,CAACsD,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEAzI,cAAcA,CAACyI,SAAiB;IAC9B,MAAMrD,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC+E,GAAG,CAACE,SAAS,CAAC;IAC7C,OAAO,CAAC,EAAErD,KAAK,EAAEoB,OAAO,IAAIpB,KAAK,CAACuD,OAAO,CAAC;EAC5C;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACvF,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,4DAA4D;KACpE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,kDAAkD;KAC1D,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,gDAAgD;KACxD,MAAM;MACL,OAAO,yDAAyD;;EAEpE;EAEAwF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACxF,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;;;;uBAzPWN,0BAA0B,EAAAlF,EAAA,CAAAiL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAAiL,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArL,EAAA,CAAAiL,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvL,EAAA,CAAAiL,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA1BvG,0BAA0B;MAAAwG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZvChM,EAAA,CAAAC,cAAA,aAAwH;UAKhHD,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA2D;UACzDD,EAAA,CAAAE,MAAA,sFACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGJH,EAAA,CAAAC,cAAA,aAAmC;UAEsCD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpFH,EAAA,CAAAC,cAAA,eAAqE;UAAAD,EAAA,CAAAE,MAAA,IAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvGH,EAAA,CAAAC,cAAA,cAAoF;UAClFD,EAAA,CAAAsD,SAAA,cAIM;UACRtD,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAC,cAAA,eAA4H;UAKpHD,EAAA,CAAAuB,UAAA,KAAA2K,0CAAA,mBASM;UACRlM,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,gBAAoE;UAApCD,EAAA,CAAAe,UAAA,sBAAAoL,8DAAA;YAAA,OAAYF,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAErD1I,EAAA,CAAAuB,UAAA,KAAA6K,0CAAA,oBAyEM;UAGNpM,EAAA,CAAAuB,UAAA,KAAA8K,0CAAA,mBAqEM;UAGNrM,EAAA,CAAAuB,UAAA,KAAA+K,0CAAA,mBA0CM;UAGNtM,EAAA,CAAAC,cAAA,eAAyG;UAErGD,EAAA,CAAAuB,UAAA,KAAAgL,6CAAA,qBAMS;UACXvM,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA4B;UAGxBD,EAAA,CAAAe,UAAA,mBAAAyL,6DAAA;YAAA,OAASP,GAAA,CAAA1B,UAAA,EAAY;UAAA,EAAC;UAEtBvK,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAuB,UAAA,KAAAkL,6CAAA,qBAMS;UAETzM,EAAA,CAAAuB,UAAA,KAAAmL,6CAAA,qBAaS;UACX1M,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAuB,UAAA,KAAAoL,0CAAA,kBAEM;UAEN3M,EAAA,CAAAuB,UAAA,KAAAqL,0CAAA,kBAEM;UACR5M,EAAA,CAAAG,YAAA,EAAO;;;UA9RkEH,EAAA,CAAAQ,SAAA,IAAyB;UAAzBR,EAAA,CAAAS,kBAAA,KAAAwL,GAAA,CAAAzG,kBAAA,MAAyB;UAK5FxF,EAAA,CAAAQ,SAAA,GAAoC;UAApCR,EAAA,CAAA6M,WAAA,UAAAZ,GAAA,CAAAzG,kBAAA,MAAoC,qBAAAyG,GAAA,CAAAjB,gBAAA;UAKtChL,EAAA,CAAAQ,SAAA,GACF;UADER,EAAA,CAAAS,kBAAA,MAAAwL,GAAA,CAAAlB,sBAAA,QACF;UAWuB/K,EAAA,CAAAQ,SAAA,GAAY;UAAZR,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA8M,eAAA,KAAAC,GAAA,EAAY;UAW/B/M,EAAA,CAAAQ,SAAA,GACF;UADER,EAAA,CAAAgN,kBAAA,WAAAf,GAAA,CAAA3L,WAAA,UAAA2L,GAAA,CAAAvG,UAAA,MACF;UAKE1F,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAoC,UAAA,cAAA6J,GAAA,CAAAtG,WAAA,CAAyB;UAEvB3F,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,OAAuB;UA4EvBN,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,OAAuB;UAwEvBN,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,OAAuB;UAgDtBN,EAAA,CAAAQ,SAAA,GAAqB;UAArBR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,KAAqB;UAiBrBN,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,GAAA2L,GAAA,CAAAvG,UAAA,CAA8B;UAQ9B1F,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAA3L,WAAA,KAAA2L,GAAA,CAAAvG,UAAA,CAAgC;UAiBjC1F,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAAlH,OAAA,CAAa;UAIb/E,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAoC,UAAA,SAAA6J,GAAA,CAAAhH,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}