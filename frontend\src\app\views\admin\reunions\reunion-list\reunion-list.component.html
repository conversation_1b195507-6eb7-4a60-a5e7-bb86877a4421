<div class="container mx-auto px-4 py-6 page-container page-enter">
  <div class="flex flex-col mb-8">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-bold text-gray-800 page-title">{{ pageTitle }}</h1>

      <!-- Bouton de recherche -->
      <div class="relative">
        <button (click)="toggleSearchBar()" class="search-button px-4 py-2 bg-purple-200 text-purple-800 rounded-md hover:bg-purple-300 transition-colors transform hover:scale-105 duration-200 flex items-center shadow-sm border border-purple-300">
          <svg class="h-5 w-5 mr-2 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Rechercher
        </button>
      </div>
    </div>

    <!-- Barre de recherche -->
    <div *ngIf="showSearchBar" class="mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn"
         [ngClass]="{'animate__animated animate__fadeInDown': showSearchBar}">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <div class="relative">
            <div class="flex items-center">
              <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                id="searchTerm"
                [(ngModel)]="searchTerm"
                (input)="searchReunions()"
                class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300"
                placeholder="Rechercher par titre ou description"
              >
              <button *ngIf="searchTerm" (click)="clearSearch()" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div class="md:w-1/3">
          <div class="relative">
            <div class="flex items-center">
              <select
                id="planningFilter"
                [(ngModel)]="selectedPlanning"
                (change)="searchReunions()"
                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300 appearance-none"
              >
                <option value="">Tous les plannings</option>
                <option *ngFor="let planning of uniquePlannings" [value]="planning.id">{{ planning.titre }}</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg class="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="!loading && reunions.length > 0" class="mt-2 text-sm text-gray-600 flex items-center">
      <svg class="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
      <span>Les réunions à <span class="font-semibold text-red-600">présence obligatoire</span> sont affichées en premier</span>
    </div>

    <!-- Résultats de recherche -->
    <div *ngIf="searchTerm || selectedPlanning" class="mt-2 text-sm text-gray-600">
      <span *ngIf="filteredReunions.length === 0" class="text-red-500">
        Aucune réunion ne correspond à votre recherche.
      </span>
      <span *ngIf="filteredReunions.length > 0">
        {{ filteredReunions.length }} réunion(s) trouvée(s)
      </span>
    </div>
  </div>

  <div *ngIf="loading" class="text-center py-12">
    <div class="loading-spinner rounded-full h-16 w-16 border-4 border-purple-200 border-t-purple-600 mx-auto"></div>
    <p class="mt-4 text-gray-600 animate-pulse">Chargement de vos réunions...</p>
  </div>

  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg">
    <div class="flex items-center">
      <svg class="h-6 w-6 mr-3 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p>Erreur lors du chargement des réunions: {{ error.message }}</p>
    </div>
  </div>

  <div *ngIf="!loading && reunions.length === 0" class="text-center py-12 empty-container" [class.animated]="!loading">
    <div class="bg-white rounded-lg shadow-md p-8 max-w-md mx-auto">
      <svg class="mx-auto h-16 w-16 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      <h3 class="mt-4 text-xl font-medium text-gray-900">Aucune réunion prévue</h3>
      <p class="mt-2 text-gray-500">Vous pouvez créer des réunions depuis la page détail d'un planning.</p>
    </div>
  </div>

  <div *ngIf="!loading && reunions.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div *ngFor="let reunion of (searchTerm || selectedPlanning ? filteredReunions : reunions); let i = index"
         class="bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card"
         [class.animated]="animateItems"
         [class.border-l-4]="hasPresenceObligatoire(reunion)"
         [class.border-red-500]="hasPresenceObligatoire(reunion)"
         [style.animation-delay]="i * 100 + 'ms'">
      <div class="flex justify-between items-start">
        <div class="flex-1">
          <div class="flex items-center">
            <h3 class="text-lg font-semibold text-gray-800 hover:text-purple-600 transition-colors">
              <a [routerLink]="['/reunions/reunionDetails', reunion._id]" class="hover:text-purple-600">{{ reunion.titre }}</a>
            </h3>
            <span *ngIf="hasPresenceObligatoire(reunion)"
                  class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse">
              Présence Obligatoire
            </span>
          </div>
          <p class="text-sm mt-1" [innerHTML]="reunion.description | highlightPresence"></p>
          <div class="mt-3 flex items-center text-sm text-gray-500">
            <svg class="h-4 w-4 mr-2 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ reunion.date | date:'mediumDate' }} • {{ reunion.heureDebut }} - {{ reunion.heureFin }}</span>
          </div>
        </div>
        <div class="flex items-start space-x-2">
          <span [class]="'px-3 py-1 text-xs rounded-full font-medium ' + getStatutClass(reunion.statut)">
            {{ reunion.statut | titlecase }}
          </span>
          <button (click)="deleteReunion(reunion._id || reunion.id); $event.stopPropagation();"
                  class="text-red-500 hover:text-red-700 transition-colors duration-300 p-1 rounded-full hover:bg-red-50"
                  title="Supprimer la réunion">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Creator Info -->
      <div class="mt-3 text-sm text-gray-600 flex items-center">
        <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <span><strong>Créateur:</strong> {{ reunion.createur.username }}</span>
      </div>

      <!-- Participants -->
      <div *ngIf="reunion.participants.length > 0" class="mt-3 text-sm text-gray-600">
        <div class="flex items-center">
          <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <strong>Participants:&nbsp;</strong>{{ reunion.participants.length }}
        </div>
      </div>

      <!-- Planning Info -->
      <div class="mt-3 text-sm text-gray-600 flex items-center">
        <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <span><strong>Planning:</strong> {{ reunion.planning.titre }}</span>
      </div>

      <!-- Lien Visio -->
      <div *ngIf="reunion.lienVisio" class="mt-3 text-sm">
        <a href="{{ reunion.lienVisio }}" class="text-purple-600 hover:text-purple-800 flex items-center transition-colors" target="_blank">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          Rejoindre la visioconférence
        </a>
      </div>

      <div class="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
        <div class="flex items-center text-sm text-gray-500">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {{ reunion.lieu || 'Lieu non spécifié' }}
        </div>
        <div class="flex items-center space-x-2">
          <a (click)="editReunion(reunion._id || reunion.id)"
             class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-all duration-300 transform hover:scale-105 flex items-center">
            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Modifier
          </a>
        </div>
      </div>
    </div>
  </div>
</div>