{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DashboardComponent } from './dashboard.component';\nimport { CreateUserModalComponent } from './create-user-modal/create-user-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, DashboardRoutingModule, ReactiveFormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent, CreateUserModalComponent],\n    imports: [CommonModule, DashboardRoutingModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "DashboardRoutingModule", "DashboardComponent", "CreateUserModalComponent", "DashboardModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { DashboardRoutingModule } from './dashboard-routing.module';\r\nimport { DashboardComponent } from './dashboard.component';\r\nimport { CreateUserModalComponent } from './create-user-modal/create-user-modal.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DashboardComponent,\r\n    CreateUserModalComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    DashboardRoutingModule,\r\n    ReactiveFormsModule\r\n  ]\r\n})\r\nexport class DashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,wBAAwB,QAAQ,iDAAiD;;AAa1F,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBALxBL,YAAY,EACZE,sBAAsB,EACtBD,mBAAmB;IAAA;EAAA;;;2EAGVI,eAAe;IAAAC,YAAA,GATxBH,kBAAkB,EAClBC,wBAAwB;IAAAG,OAAA,GAGxBP,YAAY,EACZE,sBAAsB,EACtBD,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}