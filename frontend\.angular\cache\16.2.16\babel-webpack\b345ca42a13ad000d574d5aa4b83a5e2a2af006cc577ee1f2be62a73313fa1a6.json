{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { CallInterfaceComponent } from './call-interface/call-interface.component';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\nimport * as i0 from \"@angular/core\";\nexport class MessagesModule {\n  static {\n    this.ɵfac = function MessagesModule_Factory(t) {\n      return new (t || MessagesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MessagesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [UserStatusService, MessageService],\n      imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule, VoiceMessageModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MessagesModule, {\n    declarations: [MessageChatComponent, MessagesListComponent, UserListComponent, MessageLayoutComponent, CallInterfaceComponent],\n    imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule, VoiceMessageModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MessagesRoutingModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "MessageChatComponent", "MessagesListComponent", "UserListComponent", "MessageLayoutComponent", "CallInterfaceComponent", "UserStatusService", "MessageService", "VoiceMessageModule", "MessagesModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { MessagesRoutingModule } from './messages-routing.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ApolloModule } from 'apollo-angular';\r\nimport { MessageChatComponent } from './message-chat/message-chat.component';\r\nimport { MessagesListComponent } from './messages-list/messages-list.component';\r\nimport { UserListComponent } from './user-list/user-list.component';\r\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\r\nimport { CallInterfaceComponent } from './call-interface/call-interface.component';\r\n\r\nimport { UserStatusService } from 'src/app/services/user-status.service';\r\nimport { MessageService } from 'src/app/services/message.service';\r\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    MessageChatComponent,\r\n    MessagesListComponent,\r\n    UserListComponent,\r\n    MessageLayoutComponent,\r\n    CallInterfaceComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    MessagesRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ApolloModule,\r\n    RouterModule,\r\n    VoiceMessageModule,\r\n  ],\r\n  providers: [UserStatusService, MessageService],\r\n})\r\nexport class MessagesModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,kBAAkB,QAAQ,uDAAuD;;AAqB1F,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACH,iBAAiB,EAAEC,cAAc,CAAC;MAAAG,OAAA,GAR5Cf,YAAY,EACZE,qBAAqB,EACrBC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZJ,YAAY,EACZY,kBAAkB;IAAA;EAAA;;;2EAITC,cAAc;IAAAE,YAAA,GAjBvBV,oBAAoB,EACpBC,qBAAqB,EACrBC,iBAAiB,EACjBC,sBAAsB,EACtBC,sBAAsB;IAAAK,OAAA,GAGtBf,YAAY,EACZE,qBAAqB,EACrBC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZJ,YAAY,EACZY,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}