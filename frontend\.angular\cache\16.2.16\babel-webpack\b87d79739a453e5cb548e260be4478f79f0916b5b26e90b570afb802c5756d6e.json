{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"src/app/services/toast.service\";\nimport * as i4 from \"@angular/common\";\nfunction CreateUserModalComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateUserModalComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateUserModalComponent_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, role_r4), \" \");\n  }\n}\nfunction CreateUserModalComponent_i_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 22);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20\": a0\n  };\n};\nexport class CreateUserModalComponent {\n  constructor(fb, authService, toastService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.closeModal = new EventEmitter();\n    this.userCreated = new EventEmitter();\n    this.loading = false;\n    this.roles = ['student', 'teacher', 'admin'];\n    this.createUserForm = this.fb.group({\n      fullName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      role: ['student', Validators.required]\n    });\n  }\n  onSubmit() {\n    if (this.createUserForm.invalid) {\n      console.log('Form is invalid:', this.createUserForm.errors);\n      return;\n    }\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.toastService.showError('Authentication token not found');\n      this.loading = false;\n      return;\n    }\n    const userData = this.createUserForm.value;\n    console.log('Submitting user data:', userData);\n    // The password will be generated on the server side\n    this.authService.createUser(userData, token).subscribe({\n      next: response => {\n        console.log('User created successfully:', response);\n        const message = response.emailSent ? `User created successfully! Login credentials have been sent to ${userData.email}.` : 'User created successfully. Please check the console for the generated password.';\n        this.toastService.showSuccess(message);\n        this.userCreated.emit(response.user);\n        this.closeModal.emit(true);\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error creating user:', error);\n        this.toastService.showError(error.error?.message || 'Failed to create user');\n        this.loading = false;\n      }\n    });\n  }\n  close() {\n    this.closeModal.emit(true);\n  }\n  static {\n    this.ɵfac = function CreateUserModalComponent_Factory(t) {\n      return new (t || CreateUserModalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateUserModalComponent,\n      selectors: [[\"app-create-user-modal\"]],\n      outputs: {\n        closeModal: \"closeModal\",\n        userCreated: \"userCreated\"\n      },\n      decls: 32,\n      vars: 13,\n      consts: [[1, \"fixed\", \"inset-0\", \"bg-black/50\", \"dark:bg-black/70\", \"backdrop-blur-sm\", \"z-40\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-xl\", \"w-full\", \"max-w-md\", \"relative\"], [1, \"p-6\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"absolute\", \"top-4\", \"right-4\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-lg\"], [1, \"p-6\"], [1, \"mb-6\", \"p-4\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"rounded-lg\"], [1, \"text-sm\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"space-y-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [\"type\", \"text\", \"formControlName\", \"fullName\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngClass\"], [\"class\", \"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\", 4, \"ngIf\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngClass\"], [\"formControlName\", \"role\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"font-medium\", \"hover:opacity-90\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", \"justify-center\", 3, \"disabled\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"mt-1\", \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [3, \"value\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n      template: function CreateUserModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5, \" Create New User \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CreateUserModalComponent_Template_button_click_6_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(7, \"i\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"p\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵtext(12, \" A secure password will be automatically generated and sent to the user's email address. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function CreateUserModalComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(14, \"div\")(15, \"label\", 12);\n          i0.ɵɵtext(16, \" Full Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 13);\n          i0.ɵɵtemplate(18, CreateUserModalComponent_div_18_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\")(20, \"label\", 12);\n          i0.ɵɵtext(21, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 15);\n          i0.ɵɵtemplate(23, CreateUserModalComponent_div_23_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\")(25, \"label\", 12);\n          i0.ɵɵtext(26, \" Role \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"select\", 16);\n          i0.ɵɵtemplate(28, CreateUserModalComponent_option_28_Template, 3, 4, \"option\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"button\", 18);\n          i0.ɵɵtemplate(30, CreateUserModalComponent_i_30_Template, 1, 0, \"i\", 19);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formGroup\", ctx.createUserForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ((tmp_1_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_1_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ((tmp_3_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_3_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.createUserForm.invalid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Creating...\" : \"Create User\", \" \");\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i4.TitleCasePipe],\n      styles: [\"\\n\\n.modal-overlay[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInUp 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    transform: translateY(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 0.5rem;\\n  transition: border-color 0.2s, box-shadow 0.2s;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);\\n}\\n\\n.form-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 0.5rem;\\n  background-color: white;\\n  transition: border-color 0.2s, box-shadow 0.2s;\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #6d78c9);\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n  cursor: pointer;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.3);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #6b7280;\\n  border: 1px solid #d1d5db;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n  cursor: pointer;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n}\\n\\n\\n\\n.spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%], .dark[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  background-color: #1e1e1e;\\n  border-color: #2a2a2a;\\n  color: #e0e0e0;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\\n  border-color: #6d78c9;\\n  box-shadow: 0 0 0 3px rgba(109, 120, 201, 0.1);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #a0a0a0;\\n  border-color: #2a2a2a;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #2a2a2a;\\n  border-color: #3a3a3a;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "role_r4", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵelement", "CreateUserModalComponent", "constructor", "fb", "authService", "toastService", "closeModal", "userCreated", "loading", "roles", "createUserForm", "group", "fullName", "required", "email", "role", "onSubmit", "invalid", "console", "log", "errors", "token", "localStorage", "getItem", "showError", "userData", "value", "createUser", "subscribe", "next", "response", "message", "emailSent", "showSuccess", "emit", "user", "error", "close", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "ToastService", "selectors", "outputs", "decls", "vars", "consts", "template", "CreateUserModalComponent_Template", "rf", "ctx", "ɵɵlistener", "CreateUserModalComponent_Template_button_click_6_listener", "CreateUserModalComponent_Template_form_ngSubmit_13_listener", "ɵɵtemplate", "CreateUserModalComponent_div_18_Template", "CreateUserModalComponent_div_23_Template", "CreateUserModalComponent_option_28_Template", "CreateUserModalComponent_i_30_Template", "ɵɵpureFunction1", "_c0", "tmp_1_0", "get", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\create-user-modal\\create-user-modal.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\create-user-modal\\create-user-modal.component.html"], "sourcesContent": ["import { Component, EventEmitter, Output } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\n\r\n@Component({\r\n  selector: 'app-create-user-modal',\r\n  templateUrl: './create-user-modal.component.html',\r\n  styleUrls: ['./create-user-modal.component.css']\r\n})\r\nexport class CreateUserModalComponent {\r\n  @Output() closeModal = new EventEmitter<boolean>();\r\n  @Output() userCreated = new EventEmitter<any>();\r\n\r\n  createUserForm: FormGroup;\r\n  loading = false;\r\n  roles = ['student', 'teacher', 'admin'];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private toastService: ToastService\r\n  ) {\r\n    this.createUserForm = this.fb.group({\r\n      fullName: ['', [Validators.required]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      role: ['student', Validators.required]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.createUserForm.invalid) {\r\n      console.log('Form is invalid:', this.createUserForm.errors);\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const token = localStorage.getItem('token');\r\n\r\n    if (!token) {\r\n      this.toastService.showError('Authentication token not found');\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    const userData = this.createUserForm.value;\r\n    console.log('Submitting user data:', userData);\r\n\r\n    // The password will be generated on the server side\r\n    this.authService.createUser(userData, token).subscribe({\r\n      next: (response: any) => {\r\n        console.log('User created successfully:', response);\r\n        const message = response.emailSent\r\n          ? `User created successfully! Login credentials have been sent to ${userData.email}.`\r\n          : 'User created successfully. Please check the console for the generated password.';\r\n        this.toastService.showSuccess(message);\r\n        this.userCreated.emit(response.user);\r\n        this.closeModal.emit(true);\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error creating user:', error);\r\n        this.toastService.showError(error.error?.message || 'Failed to create user');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  close() {\r\n    this.closeModal.emit(true);\r\n  }\r\n} ", "<!-- <PERSON><PERSON> Backdrop -->\r\n<div class=\"fixed inset-0 bg-black/50 dark:bg-black/70 backdrop-blur-sm z-40\"></div>\r\n\r\n<!-- Modal Content -->\r\n<div class=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\r\n  <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-xl w-full max-w-md relative\">\r\n    <!-- <PERSON>dal Header -->\r\n    <div class=\"p-6 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n      <h3 class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n        Create New User\r\n      </h3>\r\n      <button \r\n        (click)=\"close()\"\r\n        class=\"absolute top-4 right-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\r\n      >\r\n        <i class=\"fas fa-times text-lg\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Modal Body -->\r\n    <div class=\"p-6\">\r\n      <!-- Password Info Message -->\r\n      <div class=\"mb-6 p-4 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 rounded-lg\">\r\n        <p class=\"text-sm text-[#4f5fad] dark:text-[#6d78c9]\">\r\n          <i class=\"fas fa-info-circle mr-2\"></i>\r\n          A secure password will be automatically generated and sent to the user's email address.\r\n        </p>\r\n      </div>\r\n\r\n      <form [formGroup]=\"createUserForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-4\">\r\n        <!-- Full Name -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Full Name\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            formControlName=\"fullName\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            [ngClass]=\"{'border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20': createUserForm.get('fullName')?.invalid && createUserForm.get('fullName')?.touched}\"\r\n          />\r\n          <div *ngIf=\"createUserForm.get('fullName')?.invalid && createUserForm.get('fullName')?.touched\" class=\"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n            Full name is required\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Email -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Email\r\n          </label>\r\n          <input\r\n            type=\"email\"\r\n            formControlName=\"email\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            [ngClass]=\"{'border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20': createUserForm.get('email')?.invalid && createUserForm.get('email')?.touched}\"\r\n          />\r\n          <div *ngIf=\"createUserForm.get('email')?.invalid && createUserForm.get('email')?.touched\" class=\"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n            Please enter a valid email address\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Role -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Role\r\n          </label>\r\n          <select\r\n            formControlName=\"role\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n          >\r\n            <option *ngFor=\"let role of roles\" [value]=\"role\">\r\n              {{ role | titlecase }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <button\r\n          type=\"submit\"\r\n          [disabled]=\"createUserForm.invalid || loading\"\r\n          class=\"w-full px-4 py-2.5 text-sm rounded-lg bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\r\n        >\r\n          <i *ngIf=\"loading\" class=\"fas fa-spinner fa-spin mr-2\"></i>\r\n          {{ loading ? 'Creating...' : 'Create User' }}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": "AAAA,SAAoBA,YAAY,QAAgB,eAAe;AAC/D,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;ICwCzDC,EAAA,CAAAC,cAAA,cAAwJ;IACtJD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAkJ;IAChJD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,iBAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IAC/CL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAQ,WAAA,OAAAH,OAAA,OACF;;;;;IAUFL,EAAA,CAAAS,SAAA,YAA2D;;;;;;;;ADzErE,OAAM,MAAOC,wBAAwB;EAQnCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,YAA0B;IAF1B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IAVZ,KAAAC,UAAU,GAAG,IAAIjB,YAAY,EAAW;IACxC,KAAAkB,WAAW,GAAG,IAAIlB,YAAY,EAAO;IAG/C,KAAAmB,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAOrC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAClCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,CAAC,CAAC;MACrCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,IAAI,EAAE,CAAC,SAAS,EAAEzB,UAAU,CAACuB,QAAQ;KACtC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,cAAc,CAACO,OAAO,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACT,cAAc,CAACU,MAAM,CAAC;MAC3D;;IAGF,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACF,KAAK,EAAE;MACV,IAAI,CAAChB,YAAY,CAACmB,SAAS,CAAC,gCAAgC,CAAC;MAC7D,IAAI,CAAChB,OAAO,GAAG,KAAK;MACpB;;IAGF,MAAMiB,QAAQ,GAAG,IAAI,CAACf,cAAc,CAACgB,KAAK;IAC1CR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEM,QAAQ,CAAC;IAE9C;IACA,IAAI,CAACrB,WAAW,CAACuB,UAAU,CAACF,QAAQ,EAAEJ,KAAK,CAAC,CAACO,SAAS,CAAC;MACrDC,IAAI,EAAGC,QAAa,IAAI;QACtBZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,QAAQ,CAAC;QACnD,MAAMC,OAAO,GAAGD,QAAQ,CAACE,SAAS,GAC9B,kEAAkEP,QAAQ,CAACX,KAAK,GAAG,GACnF,iFAAiF;QACrF,IAAI,CAACT,YAAY,CAAC4B,WAAW,CAACF,OAAO,CAAC;QACtC,IAAI,CAACxB,WAAW,CAAC2B,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;QACpC,IAAI,CAAC7B,UAAU,CAAC4B,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4B,KAAK,EAAGA,KAAU,IAAI;QACpBlB,OAAO,CAACkB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC/B,YAAY,CAACmB,SAAS,CAACY,KAAK,CAACA,KAAK,EAAEL,OAAO,IAAI,uBAAuB,CAAC;QAC5E,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA6B,KAAKA,CAAA;IACH,IAAI,CAAC/B,UAAU,CAAC4B,IAAI,CAAC,IAAI,CAAC;EAC5B;;;uBA5DWjC,wBAAwB,EAAAV,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnD,EAAA,CAAA+C,iBAAA,CAAAK,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAxB3C,wBAAwB;MAAA4C,SAAA;MAAAC,OAAA;QAAAxC,UAAA;QAAAC,WAAA;MAAA;MAAAwC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTrC7D,EAAA,CAAAS,SAAA,aAAoF;UAGpFT,EAAA,CAAAC,cAAA,aAAqE;UAK7DD,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAGC;UAFCD,EAAA,CAAA+D,UAAA,mBAAAC,0DAAA;YAAA,OAASF,GAAA,CAAAhB,KAAA,EAAO;UAAA,EAAC;UAGjB9C,EAAA,CAAAS,SAAA,WAAoC;UACtCT,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,aAAiB;UAIXD,EAAA,CAAAS,SAAA,aAAuC;UACvCT,EAAA,CAAAE,MAAA,iGACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,gBAA6E;UAA1CD,EAAA,CAAA+D,UAAA,sBAAAE,4DAAA;YAAA,OAAYH,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAExDzB,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAKE;UACFT,EAAA,CAAAkE,UAAA,KAAAC,wCAAA,kBAEM;UACRnE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAKE;UACFT,EAAA,CAAAkE,UAAA,KAAAE,wCAAA,kBAEM;UACRpE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,cACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAAkE,UAAA,KAAAG,2CAAA,qBAES;UACXrE,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAkE,UAAA,KAAAI,sCAAA,gBAA2D;UAC3DtE,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;UAxDLH,EAAA,CAAAM,SAAA,IAA4B;UAA5BN,EAAA,CAAAI,UAAA,cAAA0D,GAAA,CAAA3C,cAAA,CAA4B;UAU5BnB,EAAA,CAAAM,SAAA,GAAkP;UAAlPN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuE,eAAA,IAAAC,GAAA,IAAAC,OAAA,GAAAX,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAA/C,OAAA,OAAA+C,OAAA,GAAAX,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,OAAA,GAAkP;UAE9O3E,EAAA,CAAAM,SAAA,GAAwF;UAAxFN,EAAA,CAAAI,UAAA,WAAAwE,OAAA,GAAAd,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,+BAAAE,OAAA,CAAAlD,OAAA,OAAAkD,OAAA,GAAAd,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,+BAAAE,OAAA,CAAAD,OAAA,EAAwF;UAc5F3E,EAAA,CAAAM,SAAA,GAA4O;UAA5ON,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuE,eAAA,KAAAC,GAAA,IAAAK,OAAA,GAAAf,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,4BAAAG,OAAA,CAAAnD,OAAA,OAAAmD,OAAA,GAAAf,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,4BAAAG,OAAA,CAAAF,OAAA,GAA4O;UAExO3E,EAAA,CAAAM,SAAA,GAAkF;UAAlFN,EAAA,CAAAI,UAAA,WAAA0E,OAAA,GAAAhB,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,4BAAAI,OAAA,CAAApD,OAAA,OAAAoD,OAAA,GAAAhB,GAAA,CAAA3C,cAAA,CAAAuD,GAAA,4BAAAI,OAAA,CAAAH,OAAA,EAAkF;UAc7D3E,EAAA,CAAAM,SAAA,GAAQ;UAARN,EAAA,CAAAI,UAAA,YAAA0D,GAAA,CAAA5C,KAAA,CAAQ;UASnClB,EAAA,CAAAM,SAAA,GAA8C;UAA9CN,EAAA,CAAAI,UAAA,aAAA0D,GAAA,CAAA3C,cAAA,CAAAO,OAAA,IAAAoC,GAAA,CAAA7C,OAAA,CAA8C;UAG1CjB,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAI,UAAA,SAAA0D,GAAA,CAAA7C,OAAA,CAAa;UACjBjB,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAO,kBAAA,MAAAuD,GAAA,CAAA7C,OAAA,sCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}