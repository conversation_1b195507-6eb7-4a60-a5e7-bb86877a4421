{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HighlightPresencePipe } from './highlight-presence.pipe';\nimport * as i0 from \"@angular/core\";\nexport class PipesModule {\n  static {\n    this.ɵfac = function PipesModule_Factory(t) {\n      return new (t || PipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PipesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PipesModule, {\n    declarations: [HighlightPresencePipe],\n    imports: [CommonModule],\n    exports: [HighlightPresencePipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "HighlightPresencePipe", "PipesModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\pipes\\pipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HighlightPresencePipe } from './highlight-presence.pipe';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HighlightPresencePipe\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    HighlightPresencePipe\r\n  ]\r\n})\r\nexport class PipesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,2BAA2B;;AAajE,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBANpBF,YAAY;IAAA;EAAA;;;2EAMHE,WAAW;IAAAC,YAAA,GATpBF,qBAAqB;IAAAG,OAAA,GAGrBJ,YAAY;IAAAK,OAAA,GAGZJ,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}