{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FrontLayoutComponent } from './layouts/front-layout/front-layout.component';\nimport { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';\nimport { AuthAdminLayoutComponent } from './layouts/auth-admin-layout/auth-admin-layout.component';\nimport { guardadminGuard } from './views/guards/guardadmin.guard';\nimport { guarduserGuard } from './views/guards/guarduser.guard';\nimport { noguarduserGuard } from './views/guards/noguarduser.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n// Espace Front\n{\n  path: '',\n  component: FrontLayoutComponent,\n  children: [{\n    path: '',\n    loadChildren: () => import('./views/front/home/<USER>').then(m => m.HomeModule)\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./views/front/profile/profile.module').then(m => m.ProfileModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'messages',\n    loadChildren: () => import('./views/front/messages/messages.module').then(m => m.MessagesModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'plannings',\n    loadChildren: () => import('./views/front/plannings/plannings.module').then(m => m.PlanningsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'reunions',\n    loadChildren: () => import('./views/front/reunions/reunions.module').then(m => m.ReunionsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'notifications',\n    loadChildren: () => import('./views/front/notifications/notifications.module').then(m => m.NotificationsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'change-password',\n    loadChildren: () => import('./views/front/change-password/change-password.module').then(m => m.ChangePasswordModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'signup',\n    loadChildren: () => import('./views/front/signup/signup.module').then(m => m.SignupModule),\n    canActivateChild: [noguarduserGuard]\n  }, {\n    path: 'login',\n    loadChildren: () => import('./views/front/login/login.module').then(m => m.LoginModule),\n    canActivateChild: [noguarduserGuard]\n  }, {\n    path: 'verify-email',\n    loadChildren: () => import('./views/front/verify-email/verify-email.module').then(m => m.VerifyEmailModule)\n  }, {\n    path: 'reset-password',\n    loadChildren: () => import('./views/front/reset-password/reset-password.module').then(m => m.ResetPasswordModule)\n  }, {\n    path: 'forgot-password',\n    loadChildren: () => import('./views/front/forgot-password/forgot-password.module').then(m => m.ForgotPasswordModule)\n  }, {\n    path: 'projects',\n    loadChildren: () => import('./views/front/projects/projects.module').then(m => m.ProjectsModule),\n    canActivateChild: [guarduserGuard] // Protection pour utilisateurs authentifiés\n  }, {\n    path: 'equipes',\n    loadChildren: () => import('./views/front/equipes/equipes.module').then(m => m.EquipesModule),\n    canActivateChild: [guarduserGuard] // Protection pour utilisateurs authentifiés\n  }, {\n    path: 'complete-profile',\n    loadChildren: () => import('./views/front/profile-completion/profile-completion.module').then(m => m.ProfileCompletionModule),\n    canActivate: [guarduserGuard] // Must be logged in to complete profile\n  }]\n},\n//  Espace Admin\n{\n  path: 'admin',\n  component: AdminLayoutComponent,\n  canActivate: [guardadminGuard],\n  children: [{\n    path: '',\n    loadChildren: () => import('./views/admin/dashboard/dashboard.module').then(m => m.DashboardModule)\n  }, {\n    path: 'dashboard',\n    loadChildren: () => import('./views/admin/dashboard/dashboard.module').then(m => m.DashboardModule)\n  }, {\n    path: 'userdetails/:id',\n    loadChildren: () => import('./views/admin/userdetails/userdetails.module').then(m => m.UserdetailsModule)\n  }, {\n    path: 'plannings',\n    loadChildren: () => import('./views/admin/plannings/plannings.module').then(m => m.PlanningsModule)\n  }, {\n    path: 'reunions',\n    loadChildren: () => import('./views/admin/reunions/reunions.module').then(m => m.ReunionsModule)\n  }, {\n    path: 'projects',\n    loadChildren: () => import('./views/admin/projects/projects.module').then(m => m.ProjectsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./views/admin/profile/profile.module').then(m => m.ProfileModule)\n  }, {\n    path: 'equipes',\n    loadChildren: () => import('./views/admin/equipes/equipes.module').then(m => m.EquipesModule)\n  }]\n},\n//  Espace Auth-admin\n{\n  path: 'admin/login',\n  component: AuthAdminLayoutComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FrontLayoutComponent", "AdminLayoutComponent", "AuthAdminLayoutComponent", "guardad<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "noguarduser<PERSON><PERSON>", "routes", "path", "component", "children", "loadChildren", "then", "m", "HomeModule", "ProfileModule", "canActivateChild", "MessagesModule", "PlanningsModule", "ReunionsModule", "NotificationsModule", "ChangePasswordModule", "SignupModule", "LoginModule", "VerifyEmailModule", "ResetPasswordModule", "ForgotPasswordModule", "ProjectsModule", "EquipesModule", "ProfileCompletionModule", "canActivate", "DashboardModule", "UserdetailsModule", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { FrontLayoutComponent } from './layouts/front-layout/front-layout.component';\r\nimport { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';\r\nimport { AuthAdminLayoutComponent } from './layouts/auth-admin-layout/auth-admin-layout.component';\r\nimport { guardadminGuard } from './views/guards/guardadmin.guard';\r\nimport { guarduserGuard } from './views/guards/guarduser.guard';\r\nimport { noguarduserGuard } from './views/guards/noguarduser.guard';\r\nimport { ProfileCompletionGuard } from './guards/profile-completion.guard';\r\nconst routes: Routes = [\r\n  // Espace Front\r\n  {\r\n    path: '',\r\n    component: FrontLayoutComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        loadChildren: () =>\r\n          import('./views/front/home/<USER>').then((m) => m.HomeModule),\r\n      },\r\n      {\r\n        path: 'profile',\r\n        loadChildren: () =>\r\n          import('./views/front/profile/profile.module').then(\r\n            (m) => m.ProfileModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'messages',\r\n        loadChildren: () =>\r\n          import('./views/front/messages/messages.module').then(\r\n            (m) => m.MessagesModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'plannings',\r\n        loadChildren: () =>\r\n          import('./views/front/plannings/plannings.module').then(\r\n            (m) => m.PlanningsModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'reunions',\r\n        loadChildren: () =>\r\n          import('./views/front/reunions/reunions.module').then(\r\n            (m) => m.ReunionsModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'notifications',\r\n        loadChildren: () =>\r\n          import('./views/front/notifications/notifications.module').then(\r\n            (m) => m.NotificationsModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'change-password',\r\n        loadChildren: () =>\r\n          import('./views/front/change-password/change-password.module').then(\r\n            (m) => m.ChangePasswordModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'signup',\r\n        loadChildren: () =>\r\n          import('./views/front/signup/signup.module').then(\r\n            (m) => m.SignupModule\r\n          ),\r\n        canActivateChild: [noguarduserGuard],\r\n      },\r\n      {\r\n        path: 'login',\r\n        loadChildren: () =>\r\n          import('./views/front/login/login.module').then((m) => m.LoginModule),\r\n        canActivateChild: [noguarduserGuard],\r\n      },\r\n      {\r\n        path: 'verify-email',\r\n        loadChildren: () =>\r\n          import('./views/front/verify-email/verify-email.module').then(\r\n            (m) => m.VerifyEmailModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'reset-password',\r\n        loadChildren: () =>\r\n          import('./views/front/reset-password/reset-password.module').then(\r\n            (m) => m.ResetPasswordModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'forgot-password',\r\n        loadChildren: () =>\r\n          import('./views/front/forgot-password/forgot-password.module').then(\r\n            (m) => m.ForgotPasswordModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'projects',\r\n        loadChildren: () =>\r\n          import('./views/front/projects/projects.module').then(\r\n            (m) => m.ProjectsModule\r\n          ),\r\n        canActivateChild: [guarduserGuard], // Protection pour utilisateurs authentifiés\r\n      },\r\n      {\r\n        path: 'equipes',\r\n        loadChildren: () =>\r\n          import('./views/front/equipes/equipes.module').then(\r\n            (m) => m.EquipesModule\r\n          ),\r\n        canActivateChild: [guarduserGuard], // Protection pour utilisateurs authentifiés\r\n      },\r\n      {\r\n        path: 'complete-profile',\r\n        loadChildren: () =>\r\n          import('./views/front/profile-completion/profile-completion.module').then(\r\n            (m) => m.ProfileCompletionModule\r\n          ),\r\n        canActivate: [guarduserGuard], // Must be logged in to complete profile\r\n      },\r\n    ],\r\n  },\r\n  //  Espace Admin\r\n  {\r\n    path: 'admin',\r\n    component: AdminLayoutComponent,\r\n    canActivate: [guardadminGuard],\r\n    children: [\r\n      {\r\n        path: '',\r\n        loadChildren: () =>\r\n          import('./views/admin/dashboard/dashboard.module').then(\r\n            (m) => m.DashboardModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'dashboard',\r\n        loadChildren: () =>\r\n          import('./views/admin/dashboard/dashboard.module').then(\r\n            (m) => m.DashboardModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'userdetails/:id',\r\n        loadChildren: () =>\r\n          import('./views/admin/userdetails/userdetails.module').then(\r\n            (m) => m.UserdetailsModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'plannings',\r\n        loadChildren: () =>\r\n          import('./views/admin/plannings/plannings.module').then(\r\n            (m) => m.PlanningsModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'reunions',\r\n        loadChildren: () =>\r\n          import('./views/admin/reunions/reunions.module').then(\r\n            (m) => m.ReunionsModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'projects',\r\n        loadChildren: () =>\r\n          import('./views/admin/projects/projects.module').then(\r\n            (m) => m.ProjectsModule\r\n          ),\r\n        canActivateChild: [guarduserGuard],\r\n      },\r\n      {\r\n        path: 'profile',\r\n        loadChildren: () =>\r\n          import('./views/admin/profile/profile.module').then(\r\n            (m) => m.ProfileModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'equipes',\r\n        loadChildren: () =>\r\n          import('./views/admin/equipes/equipes.module').then(\r\n            (m) => m.EquipesModule\r\n          ),\r\n      },\r\n    ],\r\n  },\r\n  //  Espace Auth-admin\r\n  { path: 'admin/login', component: AuthAdminLayoutComponent },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,wBAAwB,QAAQ,yDAAyD;AAClG,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,gBAAgB,QAAQ,kCAAkC;;;AAEnE,MAAMC,MAAM,GAAW;AACrB;AACA;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAER,oBAAoB;EAC/BS,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;GACpE,EACD;IACEN,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACE,aAAa,CACvB;IACHC,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACI,cAAc,CACxB;IACHD,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACK,eAAe,CACzB;IACHF,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACM,cAAc,CACxB;IACHH,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,eAAe;IACrBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kDAAkD,CAAC,CAACC,IAAI,CAC5DC,CAAC,IAAKA,CAAC,CAACO,mBAAmB,CAC7B;IACHJ,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAChEC,CAAC,IAAKA,CAAC,CAACQ,oBAAoB,CAC9B;IACHL,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,QAAQ;IACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACS,YAAY,CACtB;IACHN,gBAAgB,EAAE,CAACV,gBAAgB;GACpC,EACD;IACEE,IAAI,EAAE,OAAO;IACbG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,WAAW,CAAC;IACvEP,gBAAgB,EAAE,CAACV,gBAAgB;GACpC,EACD;IACEE,IAAI,EAAE,cAAc;IACpBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gDAAgD,CAAC,CAACC,IAAI,CAC1DC,CAAC,IAAKA,CAAC,CAACW,iBAAiB;GAE/B,EACD;IACEhB,IAAI,EAAE,gBAAgB;IACtBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oDAAoD,CAAC,CAACC,IAAI,CAC9DC,CAAC,IAAKA,CAAC,CAACY,mBAAmB;GAEjC,EACD;IACEjB,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAChEC,CAAC,IAAKA,CAAC,CAACa,oBAAoB;GAElC,EACD;IACElB,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACc,cAAc,CACxB;IACHX,gBAAgB,EAAE,CAACX,cAAc,CAAC,CAAE;GACrC,EACD;IACEG,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACe,aAAa,CACvB;IACHZ,gBAAgB,EAAE,CAACX,cAAc,CAAC,CAAE;GACrC,EACD;IACEG,IAAI,EAAE,kBAAkB;IACxBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4DAA4D,CAAC,CAACC,IAAI,CACtEC,CAAC,IAAKA,CAAC,CAACgB,uBAAuB,CACjC;IACHC,WAAW,EAAE,CAACzB,cAAc,CAAC,CAAE;GAChC;CAEJ;AACD;AACA;EACEG,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEP,oBAAoB;EAC/B4B,WAAW,EAAE,CAAC1B,eAAe,CAAC;EAC9BM,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACkB,eAAe;GAE7B,EACD;IACEvB,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACkB,eAAe;GAE7B,EACD;IACEvB,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CACxDC,CAAC,IAAKA,CAAC,CAACmB,iBAAiB;GAE/B,EACD;IACExB,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACK,eAAe;GAE7B,EACD;IACEV,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACM,cAAc;GAE5B,EACD;IACEX,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACc,cAAc,CACxB;IACHX,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACE,aAAa;GAE3B,EACD;IACEP,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACe,aAAa;GAE3B;CAEJ;AACD;AACA;EAAEpB,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEN;AAAwB,CAAE,CAC7D;AAMD,OAAM,MAAO8B,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjC,YAAY,CAACkC,OAAO,CAAC3B,MAAM,CAAC,EAC5BP,YAAY;IAAA;EAAA;;;2EAEXiC,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAApC,YAAA;IAAAqC,OAAA,GAFjBrC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}