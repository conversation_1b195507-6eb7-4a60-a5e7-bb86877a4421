<div class="container mx-auto px-4 py-6 max-w-3xl">
  <!-- En-tête avec gradient coloré -->
  <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0">
    <h1 class="text-2xl font-bold flex items-center">
      <i class="fas fa-plus-circle mr-3 text-purple-200"></i>
      {{ isEditMode ? 'Modifier la Réunion' : 'Nouvelle Réunion' }}
    </h1>
    <p class="text-purple-100 mt-2">
      {{ isEditMode ? 'Modifiez les détails de votre réunion' : 'Créez une nouvelle réunion pour votre équipe' }}
    </p>
  </div>

  <form [formGroup]="reunionForm" (ngSubmit)="onSubmit()" class="bg-white rounded-b-lg shadow-lg p-6 border-t-0">
    <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {{ error.message || 'Une erreur est survenue' }}
    </div>
    <div *ngIf="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      {{ successMessage }}
    </div>

    <div class="grid grid-cols-1 gap-6">
      <!-- Titre -->
      <div class="relative">
        <label for="titre" class="block text-sm font-medium text-purple-700 mb-2">
          <i class="fas fa-tag mr-2 text-purple-500"></i>
          Titre *
        </label>
        <input id="titre" type="text" formControlName="titre"
               class="mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3"
               placeholder="Nom de votre réunion...">
        <div *ngIf="reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched"
             class="text-red-500 text-sm mt-2 flex items-center">
          <i class="fas fa-exclamation-circle mr-1"></i>
          Le titre est obligatoire
        </div>
      </div>

      <!-- Description -->
      <div class="relative">
        <label for="description" class="block text-sm font-medium text-indigo-700 mb-2">
          <i class="fas fa-align-left mr-2 text-indigo-500"></i>
          Description
        </label>
        <textarea id="description" formControlName="description" rows="3"
                  class="mt-1 block w-full rounded-lg border-2 border-indigo-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:ring-2 transition-all duration-200 px-4 py-3"
                  placeholder="Décrivez votre réunion..."></textarea>
      </div>

      <!-- Date and Time -->
      <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200">
        <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
          <i class="fas fa-calendar-clock mr-2 text-blue-600"></i>
          Planification
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label for="date" class="block text-sm font-medium text-blue-700 mb-2">
              <i class="fas fa-calendar mr-2 text-blue-500"></i>
              Date *
            </label>
            <input id="date" type="date" formControlName="date"
                   class="mt-1 block w-full rounded-lg border-2 border-blue-200 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-200 px-4 py-3">
            <div *ngIf="reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched"
                 class="text-red-500 text-sm mt-2 flex items-center">
              <i class="fas fa-exclamation-circle mr-1"></i>
              La date est obligatoire
            </div>
          </div>

          <div>
            <label for="heureDebut" class="block text-sm font-medium text-green-700 mb-2">
              <i class="fas fa-play mr-2 text-green-500"></i>
              Heure de début *
            </label>
            <input id="heureDebut" type="time" formControlName="heureDebut"
                   class="mt-1 block w-full rounded-lg border-2 border-green-200 shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 transition-all duration-200 px-4 py-3">
            <div *ngIf="reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched"
                 class="text-red-500 text-sm mt-2 flex items-center">
              <i class="fas fa-exclamation-circle mr-1"></i>
              L'heure de début est obligatoire
            </div>
          </div>

          <div>
            <label for="heureFin" class="block text-sm font-medium text-red-700 mb-2">
              <i class="fas fa-stop mr-2 text-red-500"></i>
              Heure de fin *
            </label>
            <input id="heureFin" type="time" formControlName="heureFin"
                   class="mt-1 block w-full rounded-lg border-2 border-red-200 shadow-sm focus:border-red-500 focus:ring-red-500 focus:ring-2 transition-all duration-200 px-4 py-3">
            <div *ngIf="reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched"
                 class="text-red-500 text-sm mt-2 flex items-center">
              <i class="fas fa-exclamation-circle mr-1"></i>
              L'heure de fin est obligatoire
            </div>
          </div>
        </div>
      </div>

      <!-- Lieu / Lien visio -->
      <div class="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200">
        <h3 class="text-lg font-semibold text-orange-800 mb-4 flex items-center">
          <i class="fas fa-map-marker-alt mr-2 text-orange-600"></i>
          Localisation
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="lieu" class="block text-sm font-medium text-orange-700 mb-2">
              <i class="fas fa-map-marker-alt mr-2 text-orange-500"></i>
              Lieu / Salle
            </label>
            <input id="lieu" type="text" formControlName="lieu"
                   class="mt-1 block w-full rounded-lg border-2 border-orange-200 shadow-sm focus:border-orange-500 focus:ring-orange-500 focus:ring-2 transition-all duration-200 px-4 py-3"
                   placeholder="Salle 101, Bureau A, Google Meet...">
          </div>

          <div>
            <label for="lienVisio" class="block text-sm font-medium text-cyan-700 mb-2">
              <i class="fas fa-video mr-2 text-cyan-500"></i>
              Lien Visio
              <span *ngIf="isCheckingLienVisio" class="ml-2 text-xs text-cyan-500">
                <svg class="inline h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Vérification...
              </span>
            </label>
            <input id="lienVisio" type="url" formControlName="lienVisio"
                   [class]="'mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 ' +
                           (lienVisioError ? 'border-2 border-red-300 focus:border-red-500' : 'border-2 border-cyan-200 focus:border-cyan-500')"
                   placeholder="https://meet.google.com/...">

            <!-- Message d'erreur pour l'unicité du lien -->
            <div *ngIf="lienVisioError" class="text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              {{ lienVisioError }}
            </div>

            <!-- Message de succès -->
            <div *ngIf="!lienVisioError && !isCheckingLienVisio && reunionForm.get('lienVisio')?.value && reunionForm.get('lienVisio')?.value.trim() !== ''"
                 class="text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200">
              <i class="fas fa-check-circle mr-2"></i>
              Lien disponible
            </div>
          </div>
        </div>
      </div>

      <!-- Planning - affiché seulement si pas de planningId dans l'URL -->
      <div *ngIf="!planningIdFromUrl" class="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
        <h3 class="text-lg font-semibold text-purple-800 mb-4 flex items-center">
          <i class="fas fa-calendar-alt mr-2 text-purple-600"></i>
          Planning
        </h3>
        <label for="planning" class="block text-sm font-medium text-purple-700 mb-2">
          <i class="fas fa-list mr-2 text-purple-500"></i>
          Sélectionnez un planning *
        </label>
        <select id="planning" formControlName="planning"
                class="mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3">
          <option value="">Choisissez un planning...</option>
          <option *ngFor="let planning of plannings" [value]="planning._id">{{ planning.titre }}</option>
        </select>
        <div *ngIf="reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched"
             class="text-red-500 text-sm mt-2 flex items-center">
          <i class="fas fa-exclamation-circle mr-1"></i>
          Le planning est obligatoire
        </div>
      </div>

      <!-- Planning info - affiché quand planningId est dans l'URL -->
      <div *ngIf="planningIdFromUrl && selectedPlanning" class="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm">
        <h3 class="text-lg font-semibold text-purple-800 mb-3 flex items-center">
          <i class="fas fa-calendar-check mr-2 text-purple-600"></i>
          Planning sélectionné
        </h3>
        <div class="flex items-center justify-between">
          <span class="font-semibold text-purple-800 text-lg">
            <i class="fas fa-calendar-alt mr-2 text-purple-600"></i>
            {{ selectedPlanning.titre }}
          </span>
          <span class="text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full border border-red-200">
            <i class="fas fa-clock mr-1"></i>
            {{ selectedPlanning.dateDebut | date:'dd/MM/yyyy' }} -
            {{ selectedPlanning.dateFin | date:'dd/MM/yyyy' }}
          </span>
        </div>
        <div *ngIf="selectedPlanning.description" class="text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300">
          <i class="fas fa-info-circle mr-1"></i>
          {{ selectedPlanning.description }}
        </div>
      </div>

      <!-- Participants -->
      <div class="bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200">
        <h3 class="text-lg font-semibold text-emerald-800 mb-4 flex items-center">
          <i class="fas fa-users mr-2 text-emerald-600"></i>
          Participants
        </h3>
        <label class="block text-sm font-medium text-emerald-700 mb-2">
          <i class="fas fa-user-friends mr-2 text-emerald-500"></i>
          Sélectionnez les participants
        </label>
        <select formControlName="participants" multiple
                class="mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]">
          <ng-container *ngIf="users$ | async as users">
            <option *ngFor="let user of users" [value]="user._id" class="py-2">
              <i class="fas fa-user mr-2"></i>{{ user.username }}
            </option>
          </ng-container>
        </select>
        <p class="text-xs text-emerald-600 mt-2">
          <i class="fas fa-info-circle mr-1"></i>
          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants
        </p>
      </div>
    </div>

    <!-- Boutons d'action avec design amélioré -->
    <div class="mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200">
      <button type="button" (click)="goReunion()"
              class="px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center">
        <i class="fas fa-times mr-2"></i>
        Annuler
      </button>
      <button type="submit" [disabled]="!canSubmit() || isSubmitting"
              class="px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg">
        <i class="fas fa-save mr-2" *ngIf="!isSubmitting && !isCheckingLienVisio"></i>
        <i class="fas fa-spinner fa-spin mr-2" *ngIf="isSubmitting"></i>
        <i class="fas fa-search mr-2" *ngIf="isCheckingLienVisio"></i>
        {{ isSubmitting ? 'Enregistrement...' : (isCheckingLienVisio ? 'Vérification...' : 'Créer la réunion') }}
      </button>
    </div>
  </form>
</div>