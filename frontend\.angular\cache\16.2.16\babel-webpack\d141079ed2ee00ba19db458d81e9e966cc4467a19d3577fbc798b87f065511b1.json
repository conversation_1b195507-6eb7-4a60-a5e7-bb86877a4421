{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { PlanningEditComponent } from \"@app/views/front/plannings/planning-edit/planning-edit.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PlanningListComponent\n}, {\n  path: 'nouveau',\n  component: PlanningFormComponent\n}, {\n  path: 'edit/:id',\n  component: PlanningEditComponent\n}, {\n  path: ':id',\n  component: PlanningDetailComponent // <-- put this last\n}];\n\nexport class PlanningsRoutingModule {\n  static {\n    this.ɵfac = function PlanningsRoutingModule_Factory(t) {\n      return new (t || PlanningsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlanningsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlanningsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "PlanningEditComponent", "routes", "path", "component", "PlanningsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\plannings-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { PlanningListComponent } from './planning-list/planning-list.component';\r\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\r\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\r\nimport {PlanningEditComponent} from \"@app/views/front/plannings/planning-edit/planning-edit.component\";\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '', component: PlanningListComponent\r\n  },\r\n  {\r\n    path: 'nouveau', component: PlanningFormComponent\r\n  },\r\n  {\r\n    path: 'edit/:id', component: PlanningEditComponent\r\n  },\r\n  {\r\n    path: ':id', component: PlanningDetailComponent  // <-- put this last\r\n  }\r\n];\r\n\r\n\r\n\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class PlanningsRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAAQC,qBAAqB,QAAO,kEAAkE;;;AAEtG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEN;CACtB,EACD;EACEK,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEJ;CAC7B,EACD;EACEG,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH;CAC9B,EACD;EACEE,IAAI,EAAE,KAAK;EAAEC,SAAS,EAAEL,uBAAuB,CAAE;CAClD,CACF;;AASD,OAAM,MAAOM,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBR,YAAY,CAACS,QAAQ,CAACJ,MAAM,CAAC,EAC7BL,YAAY;IAAA;EAAA;;;2EAEXQ,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFvBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}