{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionEditComponent } from \"./reunion-edit/reunion-edit.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ReunionListComponent\n}, {\n  path: 'nouvelleReunion',\n  component: ReunionFormComponent\n}, {\n  path: 'reunionDetails/:id',\n  component: ReunionDetailComponent\n}, {\n  path: 'modifier/:id',\n  component: ReunionEditComponent\n}];\nexport class ReunionsRoutingModule {\n  static {\n    this.ɵfac = function ReunionsRoutingModule_Factory(t) {\n      return new (t || ReunionsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReunionsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReunionsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ReunionListComponent", "ReunionFormComponent", "ReunionDetailComponent", "ReunionEditComponent", "routes", "path", "component", "ReunionsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunions-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\r\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\r\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\r\nimport {ReunionEditComponent} from \"./reunion-edit/reunion-edit.component\";\r\n\r\nconst routes: Routes = [\r\n\r\n    { path: '', component: ReunionListComponent},\r\n    { path: 'nouvelleReunion', component: ReunionFormComponent},\r\n    { path: 'reunionDetails/:id', component: ReunionDetailComponent },\r\n    { path: 'modifier/:id', component: ReunionEditComponent }\r\n\r\n  ];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ReunionsRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAAQC,oBAAoB,QAAO,uCAAuC;;;AAE1E,MAAMC,MAAM,GAAW,CAEnB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEN;AAAoB,CAAC,EAC5C;EAAEK,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEL;AAAoB,CAAC,EAC3D;EAAEI,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAEJ;AAAsB,CAAE,EACjE;EAAEG,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEH;AAAoB,CAAE,CAE1D;AAMH,OAAM,MAAOI,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBR,YAAY,CAACS,QAAQ,CAACJ,MAAM,CAAC,EAC7BL,YAAY;IAAA;EAAA;;;2EAEXQ,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFtBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}