<div
  class="futuristic-notifications-container main-grid-container"
  [class.dark]="isDarkMode$ | async"
>
  <!-- Background decorative elements -->
  <div class="background-elements background-grid">
    <!-- Grid pattern and scan line will be added via CSS -->
  </div>

  <div class="futuristic-notifications-card content-card relative z-10">
    <div class="futuristic-notifications-header">
      <h2 class="futuristic-title">
        <i class="fas fa-bell mr-2"></i>
        Notifications
      </h2>

      <!-- Barre d'actions normale -->
      <div class="flex space-x-2" *ngIf="!showSelectionBar">
        <!-- Bouton de rafraîchissement -->
        <button
          (click)="loadNotifications()"
          class="futuristic-action-button"
          title="Rafraîchir"
        >
          <i class="fas fa-sync-alt"></i>
        </button>

        <!-- Case à cocher "Tout sélectionner" (déplacée après le bouton de rafraîchissement) -->
        <div *ngIf="hasNotifications() | async" class="select-all-checkbox">
          <label class="futuristic-checkbox">
            <input
              type="checkbox"
              [checked]="allSelected"
              (click)="toggleSelectAll($event)"
            />
            <span class="checkmark"></span>
          </label>
        </div>

        <!-- Bouton de filtrage des notifications non lues -->
        <button
          (click)="toggleUnreadFilter()"
          class="futuristic-action-button"
          [class.active]="showOnlyUnread"
          title="Filtrer les non lues"
        >
          <i class="fas fa-filter"></i>
        </button>

        <!-- Bouton pour activer/désactiver le son -->
        <button
          (click)="toggleSound()"
          class="futuristic-action-button"
          [class.active]="!isSoundMuted"
          title="{{ isSoundMuted ? 'Activer le son' : 'Désactiver le son' }}"
        >
          <i
            class="fas"
            [ngClass]="isSoundMuted ? 'fa-volume-mute' : 'fa-volume-up'"
          ></i>
        </button>

        <!-- Bouton pour marquer toutes les notifications comme lues -->
        <button
          *ngIf="(unreadCount$ | async) || 0"
          (click)="markAllAsRead()"
          class="futuristic-primary-button"
        >
          <i class="fas fa-check-double mr-1"></i> Tout marquer comme lu
        </button>

        <!-- Bouton pour supprimer toutes les notifications -->
        <button
          *ngIf="hasNotifications() | async"
          (click)="deleteAllNotifications()"
          class="futuristic-danger-button"
          title="Supprimer toutes les notifications"
        >
          <i class="fas fa-trash-alt mr-1"></i> Tout supprimer
        </button>
      </div>

      <!-- Barre d'actions pour les notifications sélectionnées -->
      <div class="flex space-x-2 selection-actions" *ngIf="showSelectionBar">
        <span class="selection-count"
          >{{ selectedNotifications.size }} sélectionné(s)</span
        >

        <!-- Bouton pour marquer les notifications sélectionnées comme lues -->
        <button
          (click)="markSelectedAsRead()"
          class="futuristic-primary-button"
        >
          <i class="fas fa-check mr-1"></i> Marquer comme lu
        </button>

        <!-- Bouton pour supprimer les notifications sélectionnées -->
        <button
          (click)="deleteSelectedNotifications()"
          class="futuristic-danger-button"
        >
          <i class="fas fa-trash-alt mr-1"></i> Supprimer
        </button>

        <!-- Bouton pour annuler la sélection -->
        <button
          (click)="
            selectedNotifications.clear();
            showSelectionBar = false;
            allSelected = false
          "
          class="futuristic-cancel-button"
        >
          <i class="fas fa-times mr-1"></i> Annuler
        </button>
      </div>
    </div>

    <!-- État de chargement futuriste -->
    <div *ngIf="loading" class="futuristic-loading-container">
      <div class="futuristic-loading-circle"></div>
      <p class="futuristic-loading-text">Chargement des notifications...</p>
    </div>

    <!-- État d'erreur futuriste -->
    <div *ngIf="error" class="futuristic-error-message">
      <div class="flex items-center">
        <i class="fas fa-exclamation-triangle futuristic-error-icon"></i>
        <div>
          <h3 class="futuristic-error-title">Erreur de chargement</h3>
          <p class="futuristic-error-text">{{ getErrorMessage() }}</p>
        </div>
        <button
          (click)="loadNotifications()"
          class="futuristic-retry-button ml-auto"
        >
          Réessayer
        </button>
      </div>
    </div>

    <!-- État vide futuriste -->
    <div
      *ngIf="!loading && !(hasNotifications() | async)"
      class="futuristic-empty-state"
    >
      <div class="futuristic-empty-icon">
        <i class="fas fa-bell-slash"></i>
      </div>
      <h3 class="futuristic-empty-title">Aucune notification</h3>
      <p class="futuristic-empty-text">Vous êtes à jour !</p>
      <button (click)="loadNotifications()" class="futuristic-check-button">
        Vérifier les nouvelles notifications
      </button>
    </div>

    <!-- Liste des notifications futuriste -->
    <div
      *ngIf="!loading && (hasNotifications() | async)"
      class="futuristic-notifications-list"
      #notificationContainer
      (scroll)="onScroll(notificationContainer)"
    >
      <ng-container *ngFor="let notification of filteredNotifications$ | async">
        <div
          [class.futuristic-notification-unread]="!notification.isRead"
          [class.futuristic-notification-read]="notification.isRead"
          [class.futuristic-notification-selected]="isSelected(notification.id)"
          class="futuristic-notification-card"
        >
          <!-- Case à cocher pour la sélection (déplacée en haut à gauche) -->
          <div class="notification-checkbox">
            <label class="futuristic-checkbox">
              <input
                type="checkbox"
                [checked]="isSelected(notification.id)"
                (click)="toggleSelection(notification.id, $event)"
              />
              <span class="checkmark"></span>
            </label>
          </div>

          <!-- Avatar de l'expéditeur simplifié -->
          <div class="notification-avatar">
            <img
              [src]="
                notification.senderId?.image ||
                'assets/images/default-avatar.png'
              "
              alt="Avatar"
              onerror="this.src='assets/images/default-avatar.png'"
            />
          </div>

          <!-- Contenu principal de la notification -->
          <div class="notification-main-content">
            <!-- Contenu de notification simplifié -->
            <div class="notification-content">
              <div class="notification-header">
                <div class="notification-header-top">
                  <span class="notification-sender">{{
                    notification.senderId?.username || "Système"
                  }}</span>

                  <!-- Heure de la notification (placée à droite du nom d'utilisateur) -->
                  <div class="notification-time">
                    {{ notification.timestamp | date : "shortTime" }}
                  </div>
                </div>
              </div>

              <!-- Contenu du message (déplacé après l'en-tête) -->
              <div class="notification-text-container">
                <span class="notification-text">{{
                  notification.content
                }}</span>
              </div>

              <!-- Aperçu du message simplifié -->
              <div
                *ngIf="notification.message?.content"
                class="notification-message-preview"
              >
                {{ notification.message?.content }}
              </div>

              <!-- Indicateur de pièces jointes -->
              <div
                *ngIf="notification.message?.attachments?.length"
                class="notification-attachments-indicator"
              >
                <i class="fas fa-paperclip"></i>
                {{ notification.message?.attachments?.length }} pièce(s)
                jointe(s)
              </div>
            </div>

            <!-- Indicateur de non-lu (petit point bleu) -->
            <div *ngIf="!notification.isRead" class="unread-indicator"></div>
          </div>

          <!-- Actions de notification -->
          <div class="notification-actions">
            <!-- Bouton pour afficher les pièces jointes -->
            <button
              *ngIf="notification.message?.attachments?.length"
              (click)="
                getNotificationAttachments(notification.id);
                $event.stopPropagation()
              "
              class="notification-action-button notification-attachment-button"
              title="Voir les pièces jointes"
            >
              <i class="fas fa-paperclip"></i>
            </button>

            <!-- Bouton pour rejoindre la conversation -->
            <button
              *ngIf="
                notification.type === 'NEW_MESSAGE' ||
                notification.type === 'GROUP_INVITE' ||
                notification.type === 'MESSAGE_REACTION'
              "
              (click)="joinConversation(notification); $event.stopPropagation()"
              class="notification-action-button notification-join-button"
              title="Rejoindre la conversation"
              [disabled]="loading"
            >
              <i class="fas fa-comments" *ngIf="!loading"></i>
              <i class="fas fa-spinner fa-spin" *ngIf="loading"></i>
            </button>

            <!-- Bouton pour voir les détails de la notification -->
            <button
              (click)="
                openNotificationDetails(notification); $event.stopPropagation()
              "
              class="notification-action-button notification-details-button"
              title="Voir les détails (ne marque PAS comme lu automatiquement)"
            >
              <i class="fas fa-info-circle"></i>
            </button>

            <!-- Bouton marquer comme lu -->
            <button
              *ngIf="!notification.isRead"
              (click)="markAsRead(notification.id); $event.stopPropagation()"
              class="notification-action-button notification-read-button"
              title="Marquer cette notification comme lue"
            >
              <i class="fas fa-check"></i>
            </button>

            <!-- Bouton pour supprimer la notification -->
            <button
              (click)="
                deleteNotification(notification.id); $event.stopPropagation()
              "
              class="notification-action-button notification-delete-button"
              title="Supprimer cette notification"
            >
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
      </ng-container>

      <!-- Indicateur de chargement des anciennes notifications -->
      <div *ngIf="loadingMore" class="futuristic-loading-more">
        <div class="futuristic-loading-circle-small"></div>
        <p class="futuristic-loading-text-small">
          Chargement des notifications plus anciennes...
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour afficher les pièces jointes -->
<div
  class="futuristic-modal-overlay"
  [style.display]="showAttachmentsModal ? 'flex' : 'none'"
  (click)="closeAttachmentsModal()"
>
  <div class="futuristic-modal-container" (click)="$event.stopPropagation()">
    <div class="futuristic-modal-header">
      <h3 class="futuristic-modal-title">
        <i class="fas fa-paperclip mr-2"></i>
        Pièces jointes
      </h3>
      <button class="futuristic-modal-close" (click)="closeAttachmentsModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="futuristic-modal-body">
      <div *ngIf="loadingAttachments" class="futuristic-loading-container">
        <div class="futuristic-loading-circle"></div>
        <p class="futuristic-loading-text">Chargement des pièces jointes...</p>
      </div>

      <div
        *ngIf="!loadingAttachments && currentAttachments.length === 0"
        class="futuristic-empty-state"
      >
        <div class="futuristic-empty-icon">
          <i class="fas fa-file-alt"></i>
        </div>
        <h3 class="futuristic-empty-title">Aucune pièce jointe</h3>
        <p class="futuristic-empty-text">
          Aucune pièce jointe n'a été trouvée pour cette notification.
        </p>
      </div>

      <div
        *ngIf="!loadingAttachments && currentAttachments.length > 0"
        class="futuristic-attachments-list"
      >
        <div
          *ngFor="let attachment of currentAttachments"
          class="futuristic-attachment-item"
        >
          <!-- Image -->
          <div
            *ngIf="isImage(attachment.type)"
            class="futuristic-attachment-preview"
          >
            <img
              [src]="attachment.url"
              alt="Image"
              (click)="openAttachment(attachment.url)"
            />
          </div>

          <!-- Document -->
          <div
            *ngIf="!isImage(attachment.type)"
            class="futuristic-attachment-icon"
          >
            <i [class]="getFileIcon(attachment.type)"></i>
          </div>

          <div class="futuristic-attachment-info">
            <div class="futuristic-attachment-name">
              {{ attachment.name || "Pièce jointe" }}
            </div>
            <div class="futuristic-attachment-meta">
              <span class="futuristic-attachment-type">{{
                getFileTypeLabel(attachment.type)
              }}</span>
              <span
                *ngIf="attachment.size"
                class="futuristic-attachment-size"
                >{{ formatFileSize(attachment.size) }}</span
              >
            </div>
          </div>

          <div class="futuristic-attachment-actions">
            <button
              class="futuristic-attachment-button"
              (click)="openAttachment(attachment.url)"
              title="Ouvrir"
            >
              <i class="fas fa-external-link-alt"></i>
            </button>
            <button
              class="futuristic-attachment-button"
              (click)="downloadAttachment(attachment)"
              title="Télécharger"
            >
              <i class="fas fa-download"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour afficher les détails de notification -->
<div
  class="futuristic-modal-overlay"
  [style.display]="showNotificationDetailsModal ? 'flex' : 'none'"
  (click)="closeNotificationDetailsModal()"
>
  <div class="futuristic-modal-container" (click)="$event.stopPropagation()">
    <div class="futuristic-modal-header">
      <h3 class="futuristic-modal-title">
        <i class="fas fa-info-circle mr-2"></i>
        Détails de la notification
      </h3>
      <button
        class="futuristic-modal-close"
        (click)="closeNotificationDetailsModal()"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="futuristic-modal-body" *ngIf="currentNotification">
      <!-- Informations de l'expéditeur -->
      <div class="notification-detail-section">
        <h4 class="notification-detail-title">
          <i class="fas fa-user mr-2"></i>
          Expéditeur
        </h4>
        <div class="notification-sender-info">
          <img
            [src]="
              currentNotification.senderId?.image ||
              'assets/images/default-avatar.png'
            "
            alt="Avatar"
            class="notification-sender-avatar"
            onerror="this.src='assets/images/default-avatar.png'"
          />
          <div class="notification-sender-details">
            <span class="notification-sender-name">
              {{ currentNotification.senderId?.username || "Système" }}
            </span>
            <span class="notification-timestamp">
              {{ currentNotification.timestamp | date : "medium" }}
            </span>
          </div>
        </div>
      </div>

      <!-- Contenu de la notification -->
      <div class="notification-detail-section">
        <h4 class="notification-detail-title">
          <i class="fas fa-message mr-2"></i>
          Message
        </h4>
        <div class="notification-content-detail">
          {{ currentNotification.content }}
        </div>
        <div
          *ngIf="currentNotification.message?.content"
          class="notification-message-detail"
        >
          <strong>Message original :</strong>
          {{ currentNotification.message?.content }}
        </div>
      </div>

      <!-- Type et statut -->
      <div class="notification-detail-section">
        <h4 class="notification-detail-title">
          <i class="fas fa-tag mr-2"></i>
          Informations
        </h4>
        <div class="notification-info-grid">
          <div class="notification-info-item">
            <span class="notification-info-label">Type :</span>
            <span class="notification-info-value">{{
              currentNotification.type
            }}</span>
          </div>
          <div class="notification-info-item">
            <span class="notification-info-label">Statut :</span>
            <span
              class="notification-info-value"
              [class.text-green-500]="currentNotification.isRead"
              [class.text-orange-500]="!currentNotification.isRead"
            >
              {{ currentNotification.isRead ? "Lu" : "Non lu" }}
            </span>
          </div>
          <div
            class="notification-info-item"
            *ngIf="currentNotification.readAt"
          >
            <span class="notification-info-label">Lu le :</span>
            <span class="notification-info-value">{{
              currentNotification.readAt | date : "medium"
            }}</span>
          </div>
          <div
            class="notification-info-item"
            *ngIf="!currentNotification.isRead"
            style="
              background: rgba(255, 140, 0, 0.1);
              border: 1px solid rgba(255, 140, 0, 0.3);
            "
          >
            <span class="notification-info-label">
              <i class="fas fa-info-circle mr-1"></i>
              Note :
            </span>
            <span
              class="notification-info-value"
              style="color: #ff8c00; font-style: italic"
            >
              Ouvrir les détails ne marque pas automatiquement comme lu
            </span>
          </div>
        </div>
      </div>

      <!-- Pièces jointes -->
      <div
        class="notification-detail-section"
        *ngIf="currentAttachments.length > 0"
      >
        <h4 class="notification-detail-title">
          <i class="fas fa-paperclip mr-2"></i>
          Pièces jointes ({{ currentAttachments.length }})
        </h4>
        <div class="notification-attachments-grid">
          <div
            *ngFor="let attachment of currentAttachments"
            class="notification-attachment-item"
          >
            <!-- Image -->
            <div
              *ngIf="isImage(attachment.type)"
              class="notification-attachment-preview"
            >
              <img
                [src]="attachment.url"
                alt="Image"
                (click)="openAttachment(attachment.url)"
              />
            </div>

            <!-- Document -->
            <div
              *ngIf="!isImage(attachment.type)"
              class="notification-attachment-icon"
            >
              <i [class]="getFileIcon(attachment.type)"></i>
            </div>

            <div class="notification-attachment-info">
              <div class="notification-attachment-name">
                {{ attachment.name || "Pièce jointe" }}
              </div>
              <div class="notification-attachment-meta">
                <span class="notification-attachment-type">{{
                  getFileTypeLabel(attachment.type)
                }}</span>
                <span
                  *ngIf="attachment.size"
                  class="notification-attachment-size"
                  >{{ formatFileSize(attachment.size) }}</span
                >
              </div>
            </div>

            <div class="notification-attachment-actions">
              <button
                class="notification-attachment-button"
                (click)="openAttachment(attachment.url)"
                title="Ouvrir"
              >
                <i class="fas fa-external-link-alt"></i>
              </button>
              <button
                class="notification-attachment-button"
                (click)="downloadAttachment(attachment)"
                title="Télécharger"
              >
                <i class="fas fa-download"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="notification-detail-actions">
        <button
          *ngIf="
            currentNotification.type === 'NEW_MESSAGE' ||
            currentNotification.type === 'GROUP_INVITE' ||
            currentNotification.type === 'MESSAGE_REACTION'
          "
          (click)="
            joinConversation(currentNotification);
            closeNotificationDetailsModal()
          "
          class="futuristic-primary-button"
          [disabled]="loading"
        >
          <i class="fas fa-comments mr-2" *ngIf="!loading"></i>
          <i class="fas fa-spinner fa-spin mr-2" *ngIf="loading"></i>
          Rejoindre la conversation
        </button>

        <button
          *ngIf="!currentNotification.isRead"
          (click)="markAsRead(currentNotification.id)"
          class="futuristic-secondary-button"
        >
          <i class="fas fa-check mr-2"></i>
          Marquer comme lu
        </button>

        <button
          (click)="
            deleteNotification(currentNotification.id);
            closeNotificationDetailsModal()
          "
          class="futuristic-danger-button"
        >
          <i class="fas fa-trash-alt mr-2"></i>
          Supprimer
        </button>
      </div>
    </div>
  </div>
</div>
