{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ThemeService {\n  constructor() {\n    this.darkMode = new BehaviorSubject(false);\n    this.darkMode$ = this.darkMode.asObservable();\n    this.currentTheme = new BehaviorSubject({\n      name: 'Bleu Gris',\n      color: 'blue-gray',\n      isDark: false\n    });\n    this.currentTheme$ = this.currentTheme.asObservable();\n    this.availableThemes = [{\n      name: 'Bleu Gris Clair',\n      color: 'blue-gray',\n      isDark: false\n    }, {\n      name: 'Bleu Gris Sombre',\n      color: 'blue-gray',\n      isDark: true\n    }, {\n      name: '<PERSON>',\n      color: 'pink',\n      isDark: false\n    }, {\n      name: 'Rose Sombre',\n      color: 'pink',\n      isDark: true\n    }, {\n      name: '<PERSON><PERSON>',\n      color: 'cyan',\n      isDark: false\n    }, {\n      name: '<PERSON><PERSON>',\n      color: 'cyan',\n      isDark: true\n    }, {\n      name: '<PERSON>',\n      color: 'purple',\n      isDark: false\n    }, {\n      name: '<PERSON> Sombre',\n      color: 'purple',\n      isDark: true\n    }];\n    // Check if user has a theme preference in localStorage\n    const savedTheme = localStorage.getItem('currentTheme');\n    const savedDarkMode = localStorage.getItem('darkMode');\n    if (savedTheme) {\n      const theme = JSON.parse(savedTheme);\n      this.currentTheme.next(theme);\n      this.darkMode.next(theme.isDark);\n      this.applyTheme(theme);\n    } else if (savedDarkMode) {\n      const isDark = savedDarkMode === 'true';\n      const theme = {\n        name: isDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray',\n        isDark\n      };\n      this.darkMode.next(isDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    } else {\n      // Check if user prefers dark mode at OS level\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const theme = {\n        name: prefersDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\n        color: 'blue-gray',\n        isDark: prefersDark\n      };\n      this.darkMode.next(prefersDark);\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n    }\n  }\n  toggleDarkMode() {\n    const currentTheme = this.currentTheme.value;\n    const newTheme = {\n      ...currentTheme,\n      isDark: !currentTheme.isDark\n    };\n    newTheme.name = newTheme.isDark ? newTheme.name.replace('Clair', 'Sombre') : newTheme.name.replace('Sombre', 'Clair');\n    this.setTheme(newTheme);\n  }\n  setTheme(theme) {\n    this.currentTheme.next(theme);\n    this.darkMode.next(theme.isDark);\n    localStorage.setItem('currentTheme', JSON.stringify(theme));\n    localStorage.setItem('darkMode', String(theme.isDark));\n    this.applyTheme(theme);\n  }\n  applyTheme(theme) {\n    // Remove all theme classes\n    document.documentElement.classList.remove('dark', 'theme-blue-gray', 'theme-pink', 'theme-cyan', 'theme-purple');\n    // Apply dark mode\n    if (theme.isDark) {\n      document.documentElement.classList.add('dark');\n    }\n    // Apply color theme\n    document.documentElement.classList.add(`theme-${theme.color}`);\n    console.log(`Theme applied: ${theme.name} (${theme.color})`);\n  }\n  getCurrentTheme() {\n    return this.currentTheme.value;\n  }\n  isDarkMode() {\n    return this.darkMode.value;\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(t) {\n      return new (t || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ThemeService", "constructor", "darkMode", "darkMode$", "asObservable", "currentTheme", "name", "color", "isDark", "currentTheme$", "availableThemes", "savedTheme", "localStorage", "getItem", "savedDarkMode", "theme", "JSON", "parse", "next", "applyTheme", "prefersDark", "window", "matchMedia", "matches", "toggleDarkMode", "value", "newTheme", "replace", "setTheme", "setItem", "stringify", "String", "document", "documentElement", "classList", "remove", "add", "console", "log", "getCurrentTheme", "isDarkMode", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\nexport type ThemeColor = 'blue-gray' | 'pink' | 'cyan' | 'purple';\r\n\r\nexport interface Theme {\r\n  name: string;\r\n  color: ThemeColor;\r\n  isDark: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ThemeService {\r\n  private darkMode = new BehaviorSubject<boolean>(false);\r\n  darkMode$ = this.darkMode.asObservable();\r\n\r\n  private currentTheme = new BehaviorSubject<Theme>({\r\n    name: 'Bleu Gris',\r\n    color: 'blue-gray',\r\n    isDark: false,\r\n  });\r\n  currentTheme$ = this.currentTheme.asObservable();\r\n\r\n  availableThemes: Theme[] = [\r\n    { name: 'Bleu Gris Clair', color: 'blue-gray', isDark: false },\r\n    { name: 'Bleu Gris Sombre', color: 'blue-gray', isDark: true },\r\n    { name: '<PERSON>', color: 'pink', isDark: false },\r\n    { name: 'Rose Sombre', color: 'pink', isDark: true },\r\n    { name: '<PERSON><PERSON>', color: 'cyan', isDark: false },\r\n    { name: '<PERSON><PERSON>', color: 'cyan', isDark: true },\r\n    { name: '<PERSON>', color: 'purple', isDark: false },\r\n    { name: 'Violet Sombre', color: 'purple', isDark: true },\r\n  ];\r\n\r\n  constructor() {\r\n    // Check if user has a theme preference in localStorage\r\n    const savedTheme = localStorage.getItem('currentTheme');\r\n    const savedDarkMode = localStorage.getItem('darkMode');\r\n\r\n    if (savedTheme) {\r\n      const theme = JSON.parse(savedTheme);\r\n      this.currentTheme.next(theme);\r\n      this.darkMode.next(theme.isDark);\r\n      this.applyTheme(theme);\r\n    } else if (savedDarkMode) {\r\n      const isDark = savedDarkMode === 'true';\r\n      const theme = {\r\n        name: isDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\r\n        color: 'blue-gray' as ThemeColor,\r\n        isDark,\r\n      };\r\n      this.darkMode.next(isDark);\r\n      this.currentTheme.next(theme);\r\n      this.applyTheme(theme);\r\n    } else {\r\n      // Check if user prefers dark mode at OS level\r\n      const prefersDark = window.matchMedia(\r\n        '(prefers-color-scheme: dark)'\r\n      ).matches;\r\n      const theme = {\r\n        name: prefersDark ? 'Bleu Gris Sombre' : 'Bleu Gris Clair',\r\n        color: 'blue-gray' as ThemeColor,\r\n        isDark: prefersDark,\r\n      };\r\n      this.darkMode.next(prefersDark);\r\n      this.currentTheme.next(theme);\r\n      this.applyTheme(theme);\r\n    }\r\n  }\r\n\r\n  toggleDarkMode(): void {\r\n    const currentTheme = this.currentTheme.value;\r\n    const newTheme = { ...currentTheme, isDark: !currentTheme.isDark };\r\n    newTheme.name = newTheme.isDark\r\n      ? newTheme.name.replace('Clair', 'Sombre')\r\n      : newTheme.name.replace('Sombre', 'Clair');\r\n\r\n    this.setTheme(newTheme);\r\n  }\r\n\r\n  setTheme(theme: Theme): void {\r\n    this.currentTheme.next(theme);\r\n    this.darkMode.next(theme.isDark);\r\n    localStorage.setItem('currentTheme', JSON.stringify(theme));\r\n    localStorage.setItem('darkMode', String(theme.isDark));\r\n    this.applyTheme(theme);\r\n  }\r\n\r\n  private applyTheme(theme: Theme): void {\r\n    // Remove all theme classes\r\n    document.documentElement.classList.remove(\r\n      'dark',\r\n      'theme-blue-gray',\r\n      'theme-pink',\r\n      'theme-cyan',\r\n      'theme-purple'\r\n    );\r\n\r\n    // Apply dark mode\r\n    if (theme.isDark) {\r\n      document.documentElement.classList.add('dark');\r\n    }\r\n\r\n    // Apply color theme\r\n    document.documentElement.classList.add(`theme-${theme.color}`);\r\n\r\n    console.log(`Theme applied: ${theme.name} (${theme.color})`);\r\n  }\r\n\r\n  getCurrentTheme(): Theme {\r\n    return this.currentTheme.value;\r\n  }\r\n\r\n  isDarkMode(): boolean {\r\n    return this.darkMode.value;\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAatC,OAAM,MAAOC,YAAY;EAsBvBC,YAAA;IArBQ,KAAAC,QAAQ,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;IACtD,KAAAI,SAAS,GAAG,IAAI,CAACD,QAAQ,CAACE,YAAY,EAAE;IAEhC,KAAAC,YAAY,GAAG,IAAIN,eAAe,CAAQ;MAChDO,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,CAAC;IACF,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACD,YAAY,EAAE;IAEhD,KAAAM,eAAe,GAAY,CACzB;MAAEJ,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE;IAAK,CAAE,EAC9D;MAAEF,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE;IAAI,CAAE,EAC9D;MAAEF,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAK,CAAE,EACpD;MAAEF,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE,EACpD;MAAEF,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAK,CAAE,EACpD;MAAEF,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE,EACpD;MAAEF,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAK,CAAE,EACxD;MAAEF,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAI,CAAE,CACzD;IAGC;IACA,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAIF,UAAU,EAAE;MACd,MAAMI,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;MACpC,IAAI,CAACN,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACb,QAAQ,CAACgB,IAAI,CAACH,KAAK,CAACP,MAAM,CAAC;MAChC,IAAI,CAACW,UAAU,CAACJ,KAAK,CAAC;KACvB,MAAM,IAAID,aAAa,EAAE;MACxB,MAAMN,MAAM,GAAGM,aAAa,KAAK,MAAM;MACvC,MAAMC,KAAK,GAAG;QACZT,IAAI,EAAEE,MAAM,GAAG,kBAAkB,GAAG,iBAAiB;QACrDD,KAAK,EAAE,WAAyB;QAChCC;OACD;MACD,IAAI,CAACN,QAAQ,CAACgB,IAAI,CAACV,MAAM,CAAC;MAC1B,IAAI,CAACH,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;KACvB,MAAM;MACL;MACA,MAAMK,WAAW,GAAGC,MAAM,CAACC,UAAU,CACnC,8BAA8B,CAC/B,CAACC,OAAO;MACT,MAAMR,KAAK,GAAG;QACZT,IAAI,EAAEc,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;QAC1Db,KAAK,EAAE,WAAyB;QAChCC,MAAM,EAAEY;OACT;MACD,IAAI,CAAClB,QAAQ,CAACgB,IAAI,CAACE,WAAW,CAAC;MAC/B,IAAI,CAACf,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;MAC7B,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;;EAE1B;EAEAS,cAAcA,CAAA;IACZ,MAAMnB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACoB,KAAK;IAC5C,MAAMC,QAAQ,GAAG;MAAE,GAAGrB,YAAY;MAAEG,MAAM,EAAE,CAACH,YAAY,CAACG;IAAM,CAAE;IAClEkB,QAAQ,CAACpB,IAAI,GAAGoB,QAAQ,CAAClB,MAAM,GAC3BkB,QAAQ,CAACpB,IAAI,CAACqB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,GACxCD,QAAQ,CAACpB,IAAI,CAACqB,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC;IAE5C,IAAI,CAACC,QAAQ,CAACF,QAAQ,CAAC;EACzB;EAEAE,QAAQA,CAACb,KAAY;IACnB,IAAI,CAACV,YAAY,CAACa,IAAI,CAACH,KAAK,CAAC;IAC7B,IAAI,CAACb,QAAQ,CAACgB,IAAI,CAACH,KAAK,CAACP,MAAM,CAAC;IAChCI,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,KAAK,CAAC,CAAC;IAC3DH,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEE,MAAM,CAAChB,KAAK,CAACP,MAAM,CAAC,CAAC;IACtD,IAAI,CAACW,UAAU,CAACJ,KAAK,CAAC;EACxB;EAEQI,UAAUA,CAACJ,KAAY;IAC7B;IACAiB,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,MAAM,CACvC,MAAM,EACN,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACZ,cAAc,CACf;IAED;IACA,IAAIpB,KAAK,CAACP,MAAM,EAAE;MAChBwB,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,GAAG,CAAC,MAAM,CAAC;;IAGhD;IACAJ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,GAAG,CAAC,SAASrB,KAAK,CAACR,KAAK,EAAE,CAAC;IAE9D8B,OAAO,CAACC,GAAG,CAAC,kBAAkBvB,KAAK,CAACT,IAAI,KAAKS,KAAK,CAACR,KAAK,GAAG,CAAC;EAC9D;EAEAgC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClC,YAAY,CAACoB,KAAK;EAChC;EAEAe,UAAUA,CAAA;IACR,OAAO,IAAI,CAACtC,QAAQ,CAACuB,KAAK;EAC5B;;;uBAvGWzB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAyC,OAAA,EAAZzC,YAAY,CAAA0C,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}