{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class ProfileCompletionGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    // Check if user is logged in\n    if (!this.authService.isLoggedIn()) {\n      this.router.navigate(['/login']);\n      return false;\n    }\n    // Check if user needs to complete profile\n    if (this.authService.shouldCompleteProfile()) {\n      this.router.navigate(['/complete-profile']);\n      return false;\n    }\n    return true;\n  }\n  static {\n    this.ɵfac = function ProfileCompletionGuard_Factory(t) {\n      return new (t || ProfileCompletionGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileCompletionGuard,\n      factory: ProfileCompletionGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ProfileCompletionGuard", "constructor", "authService", "router", "canActivate", "isLoggedIn", "navigate", "shouldCompleteProfile", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\guards\\profile-completion.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProfileCompletionGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): boolean {\n    // Check if user is logged in\n    if (!this.authService.isLoggedIn()) {\n      this.router.navigate(['/login']);\n      return false;\n    }\n\n    // Check if user needs to complete profile\n    if (this.authService.shouldCompleteProfile()) {\n      this.router.navigate(['/complete-profile']);\n      return false;\n    }\n\n    return true;\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,sBAAsB;EAEjCC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT;IACA,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,UAAU,EAAE,EAAE;MAClC,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,OAAO,KAAK;;IAGd;IACA,IAAI,IAAI,CAACJ,WAAW,CAACK,qBAAqB,EAAE,EAAE;MAC5C,IAAI,CAACJ,MAAM,CAACG,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;MAC3C,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;;;uBArBWN,sBAAsB,EAAAQ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAtBb,sBAAsB;MAAAc,OAAA,EAAtBd,sBAAsB,CAAAe,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}