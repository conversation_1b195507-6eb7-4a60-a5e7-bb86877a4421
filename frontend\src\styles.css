/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular calendar styles are imported in angular.json */

/* Dark mode styles are now included directly in this file */

/* Import theme variables */
@import "./assets/css/theme-variables.css";

/* Styles de base pour le mode clair et sombre */
@layer base {
  body {
    @apply bg-[#edf1f4] text-[#6d6870] transition-colors duration-200;
  }

  /* Styles pour le mode sombre */
  .dark body {
    @apply bg-dark-bg-primary text-dark-text-primary;
  }

  /* Styles pour les éléments communs */
  .card {
    @apply bg-white shadow-md rounded-xl transition-colors duration-200;
  }

  .dark .card {
    @apply bg-dark-bg-secondary border border-dark-bg-tertiary shadow-lg;
  }

  /* Styles pour les boutons */
  .btn-primary {
    @apply bg-[#4f5fad] hover:bg-[#3d4a85] text-white transition-colors duration-200;
  }

  .dark .btn-primary {
    @apply bg-dark-accent-primary hover:bg-dark-accent-primary/80;
  }

  /* Styles pour les inputs */
  input,
  textarea,
  select {
    @apply bg-white border border-[#bdc6cc] rounded-md transition-colors duration-200;
  }

  .dark input,
  .dark textarea,
  .dark select {
    @apply bg-dark-bg-tertiary border-dark-bg-tertiary text-dark-text-primary;
  }
}

/* Variables futuristes globales */
:root {
  /* Variables communes */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  /* Variables pour le mode clair */
  --bg: #ffffff;
  --medium-bg: #f5f7fa;
  --light-bg: #ffffff;
  --accent-color: #4f5fad;
  --accent-color-dark: #3d4a85;
  --accent-color-light: #6d78c9;
  --secondary-color: #3d4a85;
  --secondary-color-dark: #2d3a75;
  --secondary-color-light: #6d78c9;
  --text-light: #6d6870;
  --text-dim: #9ca3af;
  --text-dark: #1f2937;
  --accent-transparent: rgba(79, 95, 173, 0.2);
  --glow-effect: 0 0 10px rgba(79, 95, 173, 0.4);
  --glow-effect-strong: 0 0 15px rgba(79, 95, 173, 0.6);

  /* Dégradés */
  --primary-gradient: linear-gradient(
    135deg,
    var(--accent-color),
    var(--secondary-color)
  );
  --glow-gradient: radial-gradient(
    circle,
    var(--accent-color-light) 0%,
    transparent 70%
  );
}

/* Variables pour le mode sombre */
.dark {
  --bg: #0a0e17;
  --medium-bg: #111827;
  --light-bg: #1f2937;
  --text-light: #f9fafb;
  --text-dim: #9ca3af;
  --text-dark: #1f2937;
}

/* Thème Bleu-Gris */
.theme-blue-gray {
  --accent-color: #4f5fad;
  --accent-color-dark: #3d4a85;
  --accent-color-light: #6d78c9;
  --secondary-color: #3d4a85;
  --secondary-color-dark: #2d3a75;
  --secondary-color-light: #6d78c9;
  --accent-transparent: rgba(79, 95, 173, 0.2);
  --glow-effect: 0 0 10px rgba(79, 95, 173, 0.4);
  --glow-effect-strong: 0 0 15px rgba(79, 95, 173, 0.6);
}

.dark.theme-blue-gray {
  --accent-color: #00f7ff;
  --accent-color-dark: #00c4cc;
  --accent-color-light: #7df9ff;
  --secondary-color: #9d4edd;
  --secondary-color-dark: #7b2cbf;
  --secondary-color-light: #c77dff;
  --accent-transparent: rgba(0, 247, 255, 0.2);
  --glow-effect: 0 0 10px rgba(0, 247, 255, 0.5);
  --glow-effect-strong: 0 0 15px rgba(0, 247, 255, 0.8);
}

/* Thème Rose */
.theme-pink {
  --accent-color: #ec4899;
  --accent-color-dark: #db2777;
  --accent-color-light: #f472b6;
  --secondary-color: #be185d;
  --secondary-color-dark: #9d174d;
  --secondary-color-light: #ec4899;
  --accent-transparent: rgba(236, 72, 153, 0.2);
  --glow-effect: 0 0 10px rgba(236, 72, 153, 0.4);
  --glow-effect-strong: 0 0 15px rgba(236, 72, 153, 0.6);
}

.dark.theme-pink {
  --accent-color: #ff69b4;
  --accent-color-dark: #ff1493;
  --accent-color-light: #ffb6c1;
  --secondary-color: #ff69b4;
  --secondary-color-dark: #ff1493;
  --secondary-color-light: #ffb6c1;
  --accent-transparent: rgba(255, 105, 180, 0.2);
  --glow-effect: 0 0 10px rgba(255, 105, 180, 0.5);
  --glow-effect-strong: 0 0 15px rgba(255, 105, 180, 0.8);
}

/* Thème Cyan */
.theme-cyan {
  --accent-color: #06b6d4;
  --accent-color-dark: #0891b2;
  --accent-color-light: #22d3ee;
  --secondary-color: #0891b2;
  --secondary-color-dark: #0e7490;
  --secondary-color-light: #06b6d4;
  --accent-transparent: rgba(6, 182, 212, 0.2);
  --glow-effect: 0 0 10px rgba(6, 182, 212, 0.4);
  --glow-effect-strong: 0 0 15px rgba(6, 182, 212, 0.6);
}

.dark.theme-cyan {
  --accent-color: #00ffff;
  --accent-color-dark: #00e6e6;
  --accent-color-light: #7fffd4;
  --secondary-color: #00ffff;
  --secondary-color-dark: #00e6e6;
  --secondary-color-light: #7fffd4;
  --accent-transparent: rgba(0, 255, 255, 0.2);
  --glow-effect: 0 0 10px rgba(0, 255, 255, 0.5);
  --glow-effect-strong: 0 0 15px rgba(0, 255, 255, 0.8);
}

/* Thème Violet */
.theme-purple {
  --accent-color: #8b5cf6;
  --accent-color-dark: #7c3aed;
  --accent-color-light: #a78bfa;
  --secondary-color: #7c3aed;
  --secondary-color-dark: #6d28d9;
  --secondary-color-light: #8b5cf6;
  --accent-transparent: rgba(139, 92, 246, 0.2);
  --glow-effect: 0 0 10px rgba(139, 92, 246, 0.4);
  --glow-effect-strong: 0 0 15px rgba(139, 92, 246, 0.6);
}

.dark.theme-purple {
  --accent-color: #9d4edd;
  --accent-color-dark: #7b2cbf;
  --accent-color-light: #c77dff;
  --secondary-color: #9d4edd;
  --secondary-color-dark: #7b2cbf;
  --secondary-color-light: #c77dff;
  --accent-transparent: rgba(157, 78, 221, 0.2);
  --glow-effect: 0 0 10px rgba(157, 78, 221, 0.5);
  --glow-effect-strong: 0 0 15px rgba(157, 78, 221, 0.8);
}

/* Styles des champs de saisie futuristes - Mode clair */
.futuristic-input-field {
  background-color: rgba(79, 95, 173, 0.05);
  border: 1px solid rgba(79, 95, 173, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  transition: all var(--transition-fast);
}

.futuristic-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: var(--glow-effect);
}

.futuristic-input-field::placeholder {
  color: var(--text-dim);
}

/* Styles des champs de saisie futuristes - Mode sombre */
.dark .futuristic-input-field {
  background-color: rgba(0, 247, 255, 0.05);
  border: 1px solid rgba(0, 247, 255, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  transition: all var(--transition-fast);
}

.dark .futuristic-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: var(--glow-effect);
}

.dark .futuristic-input-field::placeholder {
  color: var(--text-dim);
}

/* Styles des boutons d'action futuristes - Mode clair */
.futuristic-action-button {
  background-color: rgba(79, 95, 173, 0.1);
  color: var(--accent-color);
  border: none;
  border-radius: var(--border-radius-full);
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.futuristic-action-button:hover {
  background-color: rgba(79, 95, 173, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glow-effect);
}

.futuristic-action-button:active {
  transform: translateY(0);
}

/* Styles des boutons d'action futuristes - Mode sombre */
.dark .futuristic-action-button {
  background-color: rgba(0, 247, 255, 0.1);
  color: var(--accent-color);
  border: none;
  border-radius: var(--border-radius-full);
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dark .futuristic-action-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glow-effect);
}

.dark .futuristic-action-button:active {
  transform: translateY(0);
}
/* Styles de la barre de défilement - Mode clair */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: var(--medium-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color-light);
  box-shadow: var(--glow-effect);
}

/* Styles de la barre de défilement - Mode sombre */
.dark ::-webkit-scrollbar-track {
  background: var(--medium-bg);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color-light);
  box-shadow: var(--glow-effect);
}

/* Animation pour les bulles de message futuristes */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
  filter: blur(5px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  filter: blur(0);
  transition: opacity 400ms, transform 400ms, filter 400ms;
}

/* Indicateur de frappe futuriste */
.typing-indicator {
  display: inline-flex;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.2s infinite ease-in-out;
  box-shadow: 0 0 5px var(--accent-color);
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
    box-shadow: 0 0 8px var(--accent-color);
  }
}
/* Animation pour les nouvelles notifications futuristes */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 247, 255, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 10px 3px rgba(0, 247, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 247, 255, 0);
  }
}

/* Animation pour l'effet de brillance sur les bordures */
@keyframes shine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shine {
  animation: shine 3s linear infinite;
}

.notification-pulse {
  animation: pulse 1.5s infinite;
  color: var(--accent-color);
}

/* Style pour le badge de notification */
.notification-badge {
  font-weight: bold;
  z-index: 20;
  transform-origin: center;
  min-width: 22px !important;
  min-height: 22px !important;
  padding: 0 5px !important;
  font-size: 0.8rem !important;
  line-height: 1.2 !important;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  animation: notificationPulse 2s infinite;
  position: absolute !important;
  top: -10px !important;
  right: -10px !important;
  border: 2px solid white !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 105, 0.3),
    0 0 10px rgba(255, 107, 105, 0.5) !important;
}

@keyframes notificationPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 105, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 10px 3px rgba(255, 107, 105, 0.5);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 105, 0);
    transform: scale(1);
  }
}

/* Style spécifique pour le badge de notification dans la barre latérale */
.relative.z-10.flex.items-center .notification-badge {
  position: relative !important;
  top: 0 !important;
  right: 0 !important;
  margin-left: 8px !important;
  margin-right: -5px !important;
}

/* Transition pour le sidebar */
.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* Style pour les messages non lus futuristes */
.unread-message {
  @apply border-l-4;
  background-color: rgba(0, 247, 255, 0.05);
  border-color: var(--accent-color);
  box-shadow: inset 0 0 10px rgba(0, 247, 255, 0.1);
}

/* Style pour les indicateurs de statut futuristes - Mode clair */
.status-indicator {
  @apply absolute bottom-0 right-0 w-3 h-3 rounded-full border-2;
  border-color: var(--medium-bg);
}

.status-online {
  background-color: #4caf50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.6);
}

.status-offline {
  background-color: var(--text-dim);
}

/* Style pour les indicateurs de statut futuristes - Mode sombre */
.dark .status-indicator {
  @apply absolute bottom-0 right-0 w-3 h-3 rounded-full border-2;
  border-color: var(--medium-bg);
}

.dark .status-online {
  background-color: #00ff9d;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.8);
}

.dark .status-offline {
  background-color: var(--text-dim);
}

/* Styles personnalisés pour Lightbox2 */
.lb-outerContainer {
  background-color: rgba(10, 14, 23, 0.9) !important;
  border-radius: var(--border-radius-lg) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3) !important;
}

.lb-dataContainer {
  background-color: rgba(10, 14, 23, 0.9) !important;
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  border-top: none !important;
  box-shadow: 0 10px 30px rgba(0, 247, 255, 0.3) !important;
}

.lb-image {
  border-radius: var(--border-radius-md) !important;
  border: 2px solid rgba(0, 247, 255, 0.2) !important;
}

.lb-nav a.lb-prev,
.lb-nav a.lb-next {
  opacity: 0.5 !important;
}

.lb-nav a.lb-prev:hover,
.lb-nav a.lb-next:hover {
  opacity: 1 !important;
}

.lb-cancel {
  background-color: var(--accent-color) !important;
  border-radius: 50% !important;
}

.lb-data .lb-caption {
  color: var(--accent-color) !important;
  font-size: 1rem !important;
}

.lb-data .lb-number {
  color: var(--text-dim) !important;
}

.lb-data .lb-close {
  filter: invert(1) hue-rotate(180deg) brightness(1.5) !important;
}

.lb-data .lb-close:hover {
  filter: invert(1) hue-rotate(180deg) brightness(2) !important;
}
