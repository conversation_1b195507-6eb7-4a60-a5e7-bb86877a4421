{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angular-calendar\";\nexport class PlanningsModule {\n  static {\n    this.ɵfac = function PlanningsModule_Factory(t) {\n      return new (t || PlanningsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlanningsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule.forRoot({\n        provide: DateAdapter,\n        useFactory: adapterFactory\n      }), PipesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlanningsModule, {\n    declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningEditComponent],\n    imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, i1.CalendarModule, PipesModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "PlanningsRoutingModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "FormsModule", "ReactiveFormsModule", "PlanningEditComponent", "CalendarModule", "DateAdapter", "adapterFactory", "PipesModule", "PlanningsModule", "forRoot", "provide", "useFactory", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\plannings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { PlanningsRoutingModule } from './plannings-routing.module';\r\nimport { PlanningListComponent } from './planning-list/planning-list.component';\r\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\r\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\r\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\r\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\r\nimport { PipesModule } from '../../../pipes/pipes.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    PlanningListComponent,\r\n    PlanningDetailComponent,\r\n    PlanningFormComponent,\r\n    PlanningEditComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    PlanningsRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule.forRoot({\r\n      provide: DateAdapter,\r\n      useFactory: adapterFactory,\r\n    }),\r\n    PipesModule,\r\n  ],\r\n})\r\nexport class PlanningsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,WAAW,QAAQ,6BAA6B;;;AAqBzD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAXxBZ,YAAY,EACZC,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EACnBE,cAAc,CAACK,OAAO,CAAC;QACrBC,OAAO,EAAEL,WAAW;QACpBM,UAAU,EAAEL;OACb,CAAC,EACFC,WAAW;IAAA;EAAA;;;2EAGFC,eAAe;IAAAI,YAAA,GAjBxBd,qBAAqB,EACrBC,uBAAuB,EACvBC,qBAAqB,EACrBG,qBAAqB;IAAAU,OAAA,GAGrBjB,YAAY,EACZC,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EAAAY,EAAA,CAAAV,cAAA,EAKnBG,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}