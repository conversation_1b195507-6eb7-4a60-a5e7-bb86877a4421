{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/toast.service\";\nimport * as i6 from \"@angular/common\";\nfunction PlanningEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction PlanningEditComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le titre est obligatoire\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_20_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Au moins 3 caract\\u00E8res requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtemplate(2, PlanningEditComponent_div_20_span_2_Template, 2, 0, \"span\", 52);\n    i0.ɵɵtemplate(3, PlanningEditComponent_div_20_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningEditComponent_option_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r8._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r8.username, \" \");\n  }\n}\nfunction PlanningEditComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \" Veuillez s\\u00E9lectionner au moins un participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_i_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction PlanningEditComponent_i_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 55);\n  }\n}\nexport class PlanningEditComponent {\n  constructor(fb, planningService, userService, route, router, toastService) {\n    this.fb = fb;\n    this.planningService = planningService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.toastService = toastService;\n    this.users$ = this.userService.getAllUsers();\n    this.error = '';\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.planningId = this.route.snapshot.paramMap.get('id');\n    this.initForm();\n    this.loadPlanning();\n  }\n  initForm() {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      lieu: [''],\n      participants: [[], Validators.required] // FormArray for multiple participants\n    });\n  }\n\n  loadPlanning() {\n    this.planningService.getPlanningById(this.planningId).subscribe({\n      next: response => {\n        const planning = response.planning;\n        this.planningForm.patchValue({\n          titre: planning.titre,\n          description: planning.description,\n          dateDebut: planning.dateDebut,\n          dateFin: planning.dateFin,\n          lieu: planning.lieu\n        });\n        const participantsArray = this.planningForm.get('participants');\n        participantsArray.clear();\n        planning.participants.forEach(p => {\n          participantsArray.push(this.fb.control(p._id));\n        });\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du planning:', err);\n        if (err.status === 403) {\n          this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour accéder à ce planning\");\n        } else if (err.status === 404) {\n          this.toastService.showError(\"Le planning demandé n'existe pas ou a été supprimé\");\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors du chargement du planning';\n          this.toastService.showError(errorMessage);\n        }\n      }\n    });\n  }\n  onSubmit() {\n    if (this.planningForm.invalid) {\n      console.log('Formulaire invalide, soumission annulée');\n      // Marquer tous les champs comme \"touched\" pour afficher les erreurs\n      this.markFormGroupTouched();\n      this.toastService.showWarning('Veuillez corriger les erreurs avant de soumettre le formulaire');\n      return;\n    }\n    this.isLoading = true;\n    const formValue = this.planningForm.value;\n    console.log('Données du formulaire à soumettre:', formValue);\n    // Vérifier que les dates sont au bon format\n    let dateDebut = formValue.dateDebut;\n    let dateFin = formValue.dateFin;\n    // S'assurer que les dates sont des objets Date\n    if (typeof dateDebut === 'string') {\n      dateDebut = new Date(dateDebut);\n    }\n    if (typeof dateFin === 'string') {\n      dateFin = new Date(dateFin);\n    }\n    // Créer un objet avec seulement les propriétés à mettre à jour\n    // sans utiliser le type Planning complet pour éviter les erreurs de typage\n    const updatedPlanning = {\n      titre: formValue.titre,\n      description: formValue.description || '',\n      lieu: formValue.lieu || '',\n      dateDebut: dateDebut,\n      dateFin: dateFin,\n      participants: formValue.participants || []\n    };\n    console.log('Mise à jour du planning avec ID:', this.planningId);\n    console.log('Données formatées:', updatedPlanning);\n    try {\n      this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({\n        next: response => {\n          console.log('Planning mis à jour avec succès:', response);\n          this.isLoading = false;\n          // Afficher un toast de succès\n          this.toastService.showSuccess('Le planning a été modifié avec succès');\n          // Redirection vers la page de détail du planning\n          console.log('Redirection vers la page de détail du planning:', this.planningId);\n          // Utiliser setTimeout pour s'assurer que la redirection se produit après le traitement\n          setTimeout(() => {\n            this.router.navigate(['/plannings', this.planningId]).then(navigated => console.log('Redirection réussie:', navigated), err => console.error('Erreur de redirection:', err));\n          }, 100);\n        },\n        error: err => {\n          this.isLoading = false;\n          console.error('Erreur lors de la mise à jour du planning:', err);\n          // Gestion spécifique des erreurs d'autorisation\n          if (err.status === 403) {\n            this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour modifier ce planning\");\n          } else if (err.status === 401) {\n            this.toastService.showError('Vous devez être connecté pour effectuer cette action');\n          } else {\n            // Autres erreurs\n            const errorMessage = err.error?.message || 'Erreur lors de la mise à jour du planning';\n            this.toastService.showError(errorMessage, 8000);\n          }\n          // Afficher plus de détails sur l'erreur dans la console\n          if (err.error) {\n            console.error(\"Détails de l'erreur:\", err.error);\n          }\n        }\n      });\n    } catch (e) {\n      this.isLoading = false;\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      this.toastService.showError(`Exception lors de la mise à jour: ${errorMessage}`);\n      console.error('Exception lors de la mise à jour:', e);\n    }\n  }\n  // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\n  markFormGroupTouched() {\n    Object.keys(this.planningForm.controls).forEach(key => {\n      const control = this.planningForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function PlanningEditComponent_Factory(t) {\n      return new (t || PlanningEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningEditComponent,\n      selectors: [[\"app-planning-edit\"]],\n      decls: 71,\n      vars: 13,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-edit\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [\"novalidate\", \"\", 1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-pink-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-purple-200\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\", \"text-purple-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Nom de votre planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-purple-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle, bureau, lieu de l'\\u00E9v\\u00E9nement...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-orange-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-orange-500\", \"focus:border-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-week\", \"mr-2\", \"text-blue-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-day\", \"mr-2\", \"text-green-500\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-green-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-green-500\", \"focus:border-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-check\", \"mr-2\", \"text-red-500\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-red-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-red-500\", \"focus:border-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-indigo-50\", \"to-purple-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-indigo-200\"], [1, \"text-lg\", \"font-semibold\", \"text-indigo-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-edit\", \"mr-2\", \"text-indigo-500\"], [\"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, le contexte ou les d\\u00E9tails de ce planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-indigo-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", \"routerLink\", \"/plannings\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"mb-4\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [4, \"ngIf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n      template: function PlanningEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" Modifier le Planning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Modifiez les d\\u00E9tails de votre planning\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function PlanningEditComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(8, PlanningEditComponent_div_8_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"h3\", 9);\n          i0.ɵɵelement(12, \"i\", 10);\n          i0.ɵɵtext(13, \" Informations g\\u00E9n\\u00E9rales \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\")(16, \"label\", 12);\n          i0.ɵɵelement(17, \"i\", 13);\n          i0.ɵɵtext(18, \" Titre * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 14);\n          i0.ɵɵtemplate(20, PlanningEditComponent_div_20_Template, 4, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\")(22, \"label\", 16);\n          i0.ɵɵelement(23, \"i\", 17);\n          i0.ɵɵtext(24, \" Lieu / Salle \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"h3\", 20);\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵtext(29, \" P\\u00E9riode du planning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 11)(31, \"div\")(32, \"label\", 22);\n          i0.ɵɵelement(33, \"i\", 23);\n          i0.ɵɵtext(34, \" Date de d\\u00E9but * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\")(37, \"label\", 25);\n          i0.ɵɵelement(38, \"i\", 26);\n          i0.ɵɵtext(39, \" Date de fin * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 28)(42, \"h3\", 29);\n          i0.ɵɵelement(43, \"i\", 30);\n          i0.ɵɵtext(44, \" Description \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"label\", 31);\n          i0.ɵɵelement(46, \"i\", 32);\n          i0.ɵɵtext(47, \" D\\u00E9crivez votre planning \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"textarea\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 34)(50, \"h3\", 35);\n          i0.ɵɵelement(51, \"i\", 36);\n          i0.ɵɵtext(52, \" Participants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"label\", 37);\n          i0.ɵɵelement(54, \"i\", 38);\n          i0.ɵɵtext(55, \" S\\u00E9lectionnez les participants * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"select\", 39);\n          i0.ɵɵtemplate(57, PlanningEditComponent_option_57_Template, 2, 2, \"option\", 40);\n          i0.ɵɵpipe(58, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, PlanningEditComponent_div_59_Template, 3, 0, \"div\", 15);\n          i0.ɵɵelementStart(60, \"p\", 41);\n          i0.ɵɵelement(61, \"i\", 42);\n          i0.ɵɵtext(62, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(63, \"div\", 43)(64, \"button\", 44);\n          i0.ɵɵelement(65, \"i\", 45);\n          i0.ɵɵtext(66, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function PlanningEditComponent_Template_button_click_67_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(68, PlanningEditComponent_i_68_Template, 1, 0, \"i\", 47);\n          i0.ɵɵtemplate(69, PlanningEditComponent_i_69_Template, 1, 0, \"i\", 48);\n          i0.ɵɵtext(70);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"border-red-300\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(58, 11, ctx.users$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.planningForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Enregistrement...\" : \"Enregistrer les modifications\", \" \");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1lZGl0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcGxhbm5pbmdzL3BsYW5uaW5nLWVkaXQvcGxhbm5pbmctZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ɵɵelement", "ɵɵtemplate", "PlanningEditComponent_div_20_span_2_Template", "PlanningEditComponent_div_20_span_3_Template", "ɵɵproperty", "tmp_0_0", "ctx_r1", "planningForm", "get", "errors", "tmp_1_0", "user_r8", "_id", "username", "PlanningEditComponent", "constructor", "fb", "planningService", "userService", "route", "router", "toastService", "users$", "getAllUsers", "isLoading", "ngOnInit", "planningId", "snapshot", "paramMap", "initForm", "loadPlanning", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "dateDebut", "dateFin", "lieu", "participants", "getPlanningById", "subscribe", "next", "response", "planning", "patchValue", "participantsArray", "clear", "for<PERSON>ach", "p", "push", "control", "err", "console", "status", "showError", "errorMessage", "message", "onSubmit", "invalid", "log", "markFormGroupTouched", "showWarning", "formValue", "value", "Date", "updatedPlanning", "updatePlanning", "showSuccess", "setTimeout", "navigate", "then", "navigated", "e", "Error", "String", "Object", "keys", "controls", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "PlanningService", "i3", "DataService", "i4", "ActivatedRoute", "Router", "i5", "ToastService", "selectors", "decls", "vars", "consts", "template", "PlanningEditComponent_Template", "rf", "ctx", "ɵɵlistener", "PlanningEditComponent_Template_form_ngSubmit_7_listener", "PlanningEditComponent_div_8_Template", "PlanningEditComponent_div_20_Template", "PlanningEditComponent_option_57_Template", "PlanningEditComponent_div_59_Template", "PlanningEditComponent_Template_button_click_67_listener", "PlanningEditComponent_i_68_Template", "PlanningEditComponent_i_69_Template", "ɵɵclassProp", "tmp_2_0", "touched", "tmp_3_0", "ɵɵpipeBind1", "tmp_5_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\planning-edit\\planning-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\planning-edit\\planning-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { PlanningService } from '@app/services/planning.service';\r\nimport { DataService } from '@app/services/data.service';\r\nimport { ToastService } from '@app/services/toast.service';\r\n\r\n@Component({\r\n  selector: 'app-planning-edit',\r\n  templateUrl: './planning-edit.component.html',\r\n  styleUrls: ['./planning-edit.component.css'],\r\n})\r\nexport class PlanningEditComponent implements OnInit {\r\n  planningForm!: FormGroup;\r\n  users$ = this.userService.getAllUsers();\r\n  planningId!: string;\r\n  error: string = '';\r\n  isLoading: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private planningService: PlanningService,\r\n    private userService: DataService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private toastService: ToastService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.planningId = this.route.snapshot.paramMap.get('id')!;\r\n    this.initForm();\r\n    this.loadPlanning();\r\n  }\r\n\r\n  initForm(): void {\r\n    this.planningForm = this.fb.group({\r\n      titre: ['', [Validators.required, Validators.minLength(3)]],\r\n      description: [''],\r\n      dateDebut: ['', Validators.required],\r\n      dateFin: ['', Validators.required],\r\n      lieu: [''],\r\n      participants: [[], Validators.required], // FormArray for multiple participants\r\n    });\r\n  }\r\n\r\n  loadPlanning(): void {\r\n    this.planningService.getPlanningById(this.planningId).subscribe({\r\n      next: (response: any) => {\r\n        const planning = response.planning;\r\n\r\n        this.planningForm.patchValue({\r\n          titre: planning.titre,\r\n          description: planning.description,\r\n          dateDebut: planning.dateDebut,\r\n          dateFin: planning.dateFin,\r\n          lieu: planning.lieu,\r\n        });\r\n\r\n        const participantsArray = this.planningForm.get(\r\n          'participants'\r\n        ) as FormArray;\r\n        participantsArray.clear();\r\n\r\n        planning.participants.forEach((p: any) => {\r\n          participantsArray.push(this.fb.control(p._id));\r\n        });\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement du planning:', err);\r\n        if (err.status === 403) {\r\n          this.toastService.showError(\r\n            \"Accès refusé : vous n'avez pas les droits pour accéder à ce planning\"\r\n          );\r\n        } else if (err.status === 404) {\r\n          this.toastService.showError(\r\n            \"Le planning demandé n'existe pas ou a été supprimé\"\r\n          );\r\n        } else {\r\n          const errorMessage =\r\n            err.error?.message || 'Erreur lors du chargement du planning';\r\n          this.toastService.showError(errorMessage);\r\n        }\r\n      },\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.planningForm.invalid) {\r\n      console.log('Formulaire invalide, soumission annulée');\r\n\r\n      // Marquer tous les champs comme \"touched\" pour afficher les erreurs\r\n      this.markFormGroupTouched();\r\n\r\n      this.toastService.showWarning(\r\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\r\n      );\r\n      return;\r\n    }\r\n    this.isLoading = true;\r\n    const formValue = this.planningForm.value;\r\n    console.log('Données du formulaire à soumettre:', formValue);\r\n\r\n    // Vérifier que les dates sont au bon format\r\n    let dateDebut = formValue.dateDebut;\r\n    let dateFin = formValue.dateFin;\r\n\r\n    // S'assurer que les dates sont des objets Date\r\n    if (typeof dateDebut === 'string') {\r\n      dateDebut = new Date(dateDebut);\r\n    }\r\n\r\n    if (typeof dateFin === 'string') {\r\n      dateFin = new Date(dateFin);\r\n    }\r\n\r\n    // Créer un objet avec seulement les propriétés à mettre à jour\r\n    // sans utiliser le type Planning complet pour éviter les erreurs de typage\r\n    const updatedPlanning = {\r\n      titre: formValue.titre,\r\n      description: formValue.description || '',\r\n      lieu: formValue.lieu || '',\r\n      dateDebut: dateDebut,\r\n      dateFin: dateFin,\r\n      participants: formValue.participants || [],\r\n    };\r\n\r\n    console.log('Mise à jour du planning avec ID:', this.planningId);\r\n    console.log('Données formatées:', updatedPlanning);\r\n\r\n    try {\r\n      this.planningService\r\n        .updatePlanning(this.planningId, updatedPlanning)\r\n        .subscribe({\r\n          next: (response: any) => {\r\n            console.log('Planning mis à jour avec succès:', response);\r\n            this.isLoading = false;\r\n\r\n            // Afficher un toast de succès\r\n            this.toastService.showSuccess(\r\n              'Le planning a été modifié avec succès'\r\n            );\r\n\r\n            // Redirection vers la page de détail du planning\r\n            console.log(\r\n              'Redirection vers la page de détail du planning:',\r\n              this.planningId\r\n            );\r\n\r\n            // Utiliser setTimeout pour s'assurer que la redirection se produit après le traitement\r\n            setTimeout(() => {\r\n              this.router.navigate(['/plannings', this.planningId]).then(\r\n                (navigated) => console.log('Redirection réussie:', navigated),\r\n                (err) => console.error('Erreur de redirection:', err)\r\n              );\r\n            }, 100);\r\n          },\r\n          error: (err: any) => {\r\n            this.isLoading = false;\r\n            console.error('Erreur lors de la mise à jour du planning:', err);\r\n\r\n            // Gestion spécifique des erreurs d'autorisation\r\n            if (err.status === 403) {\r\n              this.toastService.showError(\r\n                \"Accès refusé : vous n'avez pas les droits pour modifier ce planning\"\r\n              );\r\n            } else if (err.status === 401) {\r\n              this.toastService.showError(\r\n                'Vous devez être connecté pour effectuer cette action'\r\n              );\r\n            } else {\r\n              // Autres erreurs\r\n              const errorMessage =\r\n                err.error?.message ||\r\n                'Erreur lors de la mise à jour du planning';\r\n              this.toastService.showError(errorMessage, 8000);\r\n            }\r\n\r\n            // Afficher plus de détails sur l'erreur dans la console\r\n            if (err.error) {\r\n              console.error(\"Détails de l'erreur:\", err.error);\r\n            }\r\n          },\r\n        });\r\n    } catch (e) {\r\n      this.isLoading = false;\r\n      const errorMessage = e instanceof Error ? e.message : String(e);\r\n      this.toastService.showError(\r\n        `Exception lors de la mise à jour: ${errorMessage}`\r\n      );\r\n      console.error('Exception lors de la mise à jour:', e);\r\n    }\r\n  }\r\n\r\n  // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\r\n  markFormGroupTouched() {\r\n    Object.keys(this.planningForm.controls).forEach((key) => {\r\n      const control = this.planningForm.get(key);\r\n      if (control) {\r\n        control.markAsTouched();\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\r\n  <!-- En-tête avec gradient coloré -->\r\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\r\n    <h1 class=\"text-2xl font-bold flex items-center\">\r\n      <i class=\"fas fa-edit mr-3 text-purple-200\"></i>\r\n      Modifier le Planning\r\n    </h1>\r\n    <p class=\"text-purple-100 mt-2\">Modifiez les détails de votre planning</p>\r\n  </div>\r\n\r\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"onSubmit()\" novalidate class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\r\n    <!-- Message d'erreur -->\r\n    <div *ngIf=\"error\" class=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\r\n      {{ error }}\r\n    </div>\r\n    <div class=\"grid grid-cols-1 gap-6\">\r\n      <!-- Section Informations générales -->\r\n      <div class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\">\r\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-4 flex items-center\">\r\n          <i class=\"fas fa-info-circle mr-2 text-purple-600\"></i>\r\n          Informations générales\r\n        </h3>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <!-- Titre -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-purple-700 mb-2\">\r\n              <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\r\n              Titre *\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"titre\"\r\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-purple-200 rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 focus:ring-2 transition-all duration-200\"\r\n              [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\r\n              placeholder=\"Nom de votre planning...\"\r\n            />\r\n            <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Le titre est obligatoire</span>\r\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">Au moins 3 caractères requis</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Lieu -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-orange-700 mb-2\">\r\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\r\n              Lieu / Salle\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"lieu\"\r\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-orange-200 rounded-lg shadow-sm focus:ring-orange-500 focus:border-orange-500 focus:ring-2 transition-all duration-200\"\r\n              placeholder=\"Salle, bureau, lieu de l'événement...\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Section Période -->\r\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\r\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\r\n          <i class=\"fas fa-calendar-week mr-2 text-blue-600\"></i>\r\n          Période du planning\r\n        </h3>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <!-- Date début -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-green-700 mb-2\">\r\n              <i class=\"fas fa-calendar-day mr-2 text-green-500\"></i>\r\n              Date de début *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              formControlName=\"dateDebut\"\r\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-green-200 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 focus:ring-2 transition-all duration-200\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Date fin -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-red-700 mb-2\">\r\n              <i class=\"fas fa-calendar-check mr-2 text-red-500\"></i>\r\n              Date de fin *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              formControlName=\"dateFin\"\r\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-red-200 rounded-lg shadow-sm focus:ring-red-500 focus:border-red-500 focus:ring-2 transition-all duration-200\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Section Description -->\r\n      <div class=\"bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-200\">\r\n        <h3 class=\"text-lg font-semibold text-indigo-800 mb-4 flex items-center\">\r\n          <i class=\"fas fa-align-left mr-2 text-indigo-600\"></i>\r\n          Description\r\n        </h3>\r\n        <label class=\"block text-sm font-medium text-indigo-700 mb-2\">\r\n          <i class=\"fas fa-edit mr-2 text-indigo-500\"></i>\r\n          Décrivez votre planning\r\n        </label>\r\n        <textarea\r\n          formControlName=\"description\"\r\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-indigo-200 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 focus:ring-2 transition-all duration-200\"\r\n          rows=\"4\"\r\n          placeholder=\"Décrivez les objectifs, le contexte ou les détails de ce planning...\"\r\n        ></textarea>\r\n      </div>\r\n\r\n      <!-- Section Participants -->\r\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\r\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\r\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\r\n          Participants\r\n        </h3>\r\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\r\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\r\n          Sélectionnez les participants *\r\n        </label>\r\n        <select\r\n          formControlName=\"participants\"\r\n          multiple\r\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\"\r\n        >\r\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\" class=\"py-2\">\r\n            {{ user.username }}\r\n          </option>\r\n        </select>\r\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\r\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n          Veuillez sélectionner au moins un participant\r\n        </div>\r\n        <p class=\"text-xs text-emerald-600 mt-2\">\r\n          <i class=\"fas fa-info-circle mr-1\"></i>\r\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Boutons d'action avec design amélioré -->\r\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\r\n      <button\r\n        type=\"button\"\r\n        routerLink=\"/plannings\"\r\n        class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\r\n        <i class=\"fas fa-times mr-2\"></i>\r\n        Annuler\r\n      </button>\r\n      <button\r\n        type=\"button\"\r\n        (click)=\"onSubmit()\"\r\n        [disabled]=\"isLoading || planningForm.invalid\"\r\n        class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\"\r\n      >\r\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isLoading\"></i>\r\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isLoading\"></i>\r\n        {{ isLoading ? 'Enregistrement...' : 'Enregistrer les modifications' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICW1EC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAwBUP,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7FH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,wCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHpGH,EAAA,CAAAC,cAAA,cAA0I;IACxID,EAAA,CAAAQ,SAAA,YAA8C;IAC9CR,EAAA,CAAAS,UAAA,IAAAC,4CAAA,mBAA6F;IAC7FV,EAAA,CAAAS,UAAA,IAAAE,4CAAA,mBAAkG;IACpGX,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAY,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDjB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAY,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAwFjEjB,EAAA,CAAAC,cAAA,iBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFmCH,EAAA,CAAAY,UAAA,UAAAO,OAAA,CAAAC,GAAA,CAAkB;IAC5DpB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAc,OAAA,CAAAE,QAAA,MACF;;;;;IAEFrB,EAAA,CAAAC,cAAA,cAAwJ;IACtJD,EAAA,CAAAQ,SAAA,YAA8C;IAC9CR,EAAA,CAAAE,MAAA,2DACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAuBNH,EAAA,CAAAQ,SAAA,YAAmD;;;;;IACnDR,EAAA,CAAAQ,SAAA,YAA6D;;;ADlJrE,OAAM,MAAOc,qBAAqB;EAOhCC,YACUC,EAAe,EACfC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,YAA0B;IAL1B,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAXtB,KAAAC,MAAM,GAAG,IAAI,CAACJ,WAAW,CAACK,WAAW,EAAE;IAEvC,KAAAxB,KAAK,GAAW,EAAE;IAClB,KAAAyB,SAAS,GAAY,KAAK;EASvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,IAAI,CAAE;IACzD,IAAI,CAACqB,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,QAAQA,CAAA;IACN,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACS,EAAE,CAACe,KAAK,CAAC;MAChCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAAC0C,QAAQ,EAAE1C,UAAU,CAAC2C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAAC0C,QAAQ,CAAC;MACpCI,OAAO,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAAC0C,QAAQ,CAAC;MAClCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC0C,QAAQ,CAAC,CAAE;KAC1C,CAAC;EACJ;;EAEAH,YAAYA,CAAA;IACV,IAAI,CAACb,eAAe,CAACuB,eAAe,CAAC,IAAI,CAACd,UAAU,CAAC,CAACe,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAa,IAAI;QACtB,MAAMC,QAAQ,GAAGD,QAAQ,CAACC,QAAQ;QAElC,IAAI,CAACrC,YAAY,CAACsC,UAAU,CAAC;UAC3Bb,KAAK,EAAEY,QAAQ,CAACZ,KAAK;UACrBG,WAAW,EAAES,QAAQ,CAACT,WAAW;UACjCC,SAAS,EAAEQ,QAAQ,CAACR,SAAS;UAC7BC,OAAO,EAAEO,QAAQ,CAACP,OAAO;UACzBC,IAAI,EAAEM,QAAQ,CAACN;SAChB,CAAC;QAEF,MAAMQ,iBAAiB,GAAG,IAAI,CAACvC,YAAY,CAACC,GAAG,CAC7C,cAAc,CACF;QACdsC,iBAAiB,CAACC,KAAK,EAAE;QAEzBH,QAAQ,CAACL,YAAY,CAACS,OAAO,CAAEC,CAAM,IAAI;UACvCH,iBAAiB,CAACI,IAAI,CAAC,IAAI,CAAClC,EAAE,CAACmC,OAAO,CAACF,CAAC,CAACrC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ,CAAC;MACDb,KAAK,EAAGqD,GAAG,IAAI;QACbC,OAAO,CAACtD,KAAK,CAAC,wCAAwC,EAAEqD,GAAG,CAAC;QAC5D,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,sEAAsE,CACvE;SACF,MAAM,IAAIH,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,oDAAoD,CACrD;SACF,MAAM;UACL,MAAMC,YAAY,GAChBJ,GAAG,CAACrD,KAAK,EAAE0D,OAAO,IAAI,uCAAuC;UAC/D,IAAI,CAACpC,YAAY,CAACkC,SAAS,CAACC,YAAY,CAAC;;MAE7C;KACD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnD,YAAY,CAACoD,OAAO,EAAE;MAC7BN,OAAO,CAACO,GAAG,CAAC,yCAAyC,CAAC;MAEtD;MACA,IAAI,CAACC,oBAAoB,EAAE;MAE3B,IAAI,CAACxC,YAAY,CAACyC,WAAW,CAC3B,gEAAgE,CACjE;MACD;;IAEF,IAAI,CAACtC,SAAS,GAAG,IAAI;IACrB,MAAMuC,SAAS,GAAG,IAAI,CAACxD,YAAY,CAACyD,KAAK;IACzCX,OAAO,CAACO,GAAG,CAAC,oCAAoC,EAAEG,SAAS,CAAC;IAE5D;IACA,IAAI3B,SAAS,GAAG2B,SAAS,CAAC3B,SAAS;IACnC,IAAIC,OAAO,GAAG0B,SAAS,CAAC1B,OAAO;IAE/B;IACA,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAG,IAAI6B,IAAI,CAAC7B,SAAS,CAAC;;IAGjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/BA,OAAO,GAAG,IAAI4B,IAAI,CAAC5B,OAAO,CAAC;;IAG7B;IACA;IACA,MAAM6B,eAAe,GAAG;MACtBlC,KAAK,EAAE+B,SAAS,CAAC/B,KAAK;MACtBG,WAAW,EAAE4B,SAAS,CAAC5B,WAAW,IAAI,EAAE;MACxCG,IAAI,EAAEyB,SAAS,CAACzB,IAAI,IAAI,EAAE;MAC1BF,SAAS,EAAEA,SAAS;MACpBC,OAAO,EAAEA,OAAO;MAChBE,YAAY,EAAEwB,SAAS,CAACxB,YAAY,IAAI;KACzC;IAEDc,OAAO,CAACO,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAClC,UAAU,CAAC;IAChE2B,OAAO,CAACO,GAAG,CAAC,oBAAoB,EAAEM,eAAe,CAAC;IAElD,IAAI;MACF,IAAI,CAACjD,eAAe,CACjBkD,cAAc,CAAC,IAAI,CAACzC,UAAU,EAAEwC,eAAe,CAAC,CAChDzB,SAAS,CAAC;QACTC,IAAI,EAAGC,QAAa,IAAI;UACtBU,OAAO,CAACO,GAAG,CAAC,kCAAkC,EAAEjB,QAAQ,CAAC;UACzD,IAAI,CAACnB,SAAS,GAAG,KAAK;UAEtB;UACA,IAAI,CAACH,YAAY,CAAC+C,WAAW,CAC3B,uCAAuC,CACxC;UAED;UACAf,OAAO,CAACO,GAAG,CACT,iDAAiD,EACjD,IAAI,CAAClC,UAAU,CAChB;UAED;UACA2C,UAAU,CAAC,MAAK;YACd,IAAI,CAACjD,MAAM,CAACkD,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC5C,UAAU,CAAC,CAAC,CAAC6C,IAAI,CACvDC,SAAS,IAAKnB,OAAO,CAACO,GAAG,CAAC,sBAAsB,EAAEY,SAAS,CAAC,EAC5DpB,GAAG,IAAKC,OAAO,CAACtD,KAAK,CAAC,wBAAwB,EAAEqD,GAAG,CAAC,CACtD;UACH,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDrD,KAAK,EAAGqD,GAAQ,IAAI;UAClB,IAAI,CAAC5B,SAAS,GAAG,KAAK;UACtB6B,OAAO,CAACtD,KAAK,CAAC,4CAA4C,EAAEqD,GAAG,CAAC;UAEhE;UACA,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YACtB,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,qEAAqE,CACtE;WACF,MAAM,IAAIH,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YAC7B,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,sDAAsD,CACvD;WACF,MAAM;YACL;YACA,MAAMC,YAAY,GAChBJ,GAAG,CAACrD,KAAK,EAAE0D,OAAO,IAClB,2CAA2C;YAC7C,IAAI,CAACpC,YAAY,CAACkC,SAAS,CAACC,YAAY,EAAE,IAAI,CAAC;;UAGjD;UACA,IAAIJ,GAAG,CAACrD,KAAK,EAAE;YACbsD,OAAO,CAACtD,KAAK,CAAC,sBAAsB,EAAEqD,GAAG,CAACrD,KAAK,CAAC;;QAEpD;OACD,CAAC;KACL,CAAC,OAAO0E,CAAC,EAAE;MACV,IAAI,CAACjD,SAAS,GAAG,KAAK;MACtB,MAAMgC,YAAY,GAAGiB,CAAC,YAAYC,KAAK,GAAGD,CAAC,CAAChB,OAAO,GAAGkB,MAAM,CAACF,CAAC,CAAC;MAC/D,IAAI,CAACpD,YAAY,CAACkC,SAAS,CACzB,qCAAqCC,YAAY,EAAE,CACpD;MACDH,OAAO,CAACtD,KAAK,CAAC,mCAAmC,EAAE0E,CAAC,CAAC;;EAEzD;EAEA;EACAZ,oBAAoBA,CAAA;IAClBe,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtE,YAAY,CAACuE,QAAQ,CAAC,CAAC9B,OAAO,CAAE+B,GAAG,IAAI;MACtD,MAAM5B,OAAO,GAAG,IAAI,CAAC5C,YAAY,CAACC,GAAG,CAACuE,GAAG,CAAC;MAC1C,IAAI5B,OAAO,EAAE;QACXA,OAAO,CAAC6B,aAAa,EAAE;;IAE3B,CAAC,CAAC;EACJ;;;uBA7LWlE,qBAAqB,EAAAtB,EAAA,CAAAyF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3F,EAAA,CAAAyF,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7F,EAAA,CAAAyF,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/F,EAAA,CAAAyF,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAjG,EAAA,CAAAyF,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAAlG,EAAA,CAAAyF,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAArB9E,qBAAqB;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlC3G,EAAA,CAAAC,cAAA,aAAmD;UAI7CD,EAAA,CAAAQ,SAAA,WAAgD;UAChDR,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAgC;UAAAD,EAAA,CAAAE,MAAA,kDAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5EH,EAAA,CAAAC,cAAA,cAA2H;UAA1FD,EAAA,CAAA6G,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAA1C,QAAA,EAAU;UAAA,EAAC;UAEtDlE,EAAA,CAAAS,UAAA,IAAAsG,oCAAA,iBAEM;UACN/G,EAAA,CAAAC,cAAA,aAAoC;UAI9BD,EAAA,CAAAQ,SAAA,aAAuD;UACvDR,EAAA,CAAAE,MAAA,0CACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAI7CD,EAAA,CAAAQ,SAAA,aAA+C;UAC/CR,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAQ,SAAA,iBAME;UACFR,EAAA,CAAAS,UAAA,KAAAuG,qCAAA,kBAIM;UACRhH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAQ,SAAA,aAA0D;UAC1DR,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAQ,SAAA,iBAKE;UACJR,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA4F;UAExFD,EAAA,CAAAQ,SAAA,aAAuD;UACvDR,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAI7CD,EAAA,CAAAQ,SAAA,aAAuD;UACvDR,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAQ,SAAA,iBAIE;UACJR,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAQ,SAAA,aAAuD;UACvDR,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAQ,SAAA,iBAIE;UACJR,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAQ,SAAA,aAAsD;UACtDR,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,iBAA8D;UAC5DD,EAAA,CAAAQ,SAAA,aAAgD;UAChDR,EAAA,CAAAE,MAAA,sCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAQ,SAAA,oBAKY;UACdR,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAQ,SAAA,aAAkD;UAClDR,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,iBAA+D;UAC7DD,EAAA,CAAAQ,SAAA,aAAyD;UACzDR,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAS,UAAA,KAAAwG,wCAAA,qBAES;;UACXjH,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAS,UAAA,KAAAyG,qCAAA,kBAGM;UACNlH,EAAA,CAAAC,cAAA,aAAyC;UACvCD,EAAA,CAAAQ,SAAA,aAAuC;UACvCR,EAAA,CAAAE,MAAA,+EACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAC,cAAA,eAAgG;UAK5FD,EAAA,CAAAQ,SAAA,aAAiC;UACjCR,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAKC;UAHCD,EAAA,CAAA6G,UAAA,mBAAAM,wDAAA;YAAA,OAASP,GAAA,CAAA1C,QAAA,EAAU;UAAA,EAAC;UAIpBlE,EAAA,CAAAS,UAAA,KAAA2G,mCAAA,gBAAmD;UACnDpH,EAAA,CAAAS,UAAA,KAAA4G,mCAAA,gBAA6D;UAC7DrH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;UAtJPH,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,cAAAgG,GAAA,CAAA7F,YAAA,CAA0B;UAExBf,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAY,UAAA,SAAAgG,GAAA,CAAArG,KAAA,CAAW;UAqBPP,EAAA,CAAAI,SAAA,IAAiG;UAAjGJ,EAAA,CAAAsH,WAAA,qBAAAC,OAAA,GAAAX,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAuG,OAAA,CAAApD,OAAA,OAAAoD,OAAA,GAAAX,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAuG,OAAA,CAAAC,OAAA,EAAiG;UAG7FxH,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAY,UAAA,WAAA6G,OAAA,GAAAb,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAyG,OAAA,CAAAtD,OAAA,OAAAsD,OAAA,GAAAb,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAyG,OAAA,CAAAD,OAAA,EAA8E;UA2F7DxH,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA0H,WAAA,SAAAd,GAAA,CAAA9E,MAAA,EAAiB;UAItC9B,EAAA,CAAAI,SAAA,GAA4F;UAA5FJ,EAAA,CAAAY,UAAA,WAAA+G,OAAA,GAAAf,GAAA,CAAA7F,YAAA,CAAAC,GAAA,mCAAA2G,OAAA,CAAAxD,OAAA,OAAAwD,OAAA,GAAAf,GAAA,CAAA7F,YAAA,CAAAC,GAAA,mCAAA2G,OAAA,CAAAH,OAAA,EAA4F;UAuBlGxH,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAY,UAAA,aAAAgG,GAAA,CAAA5E,SAAA,IAAA4E,GAAA,CAAA7F,YAAA,CAAAoD,OAAA,CAA8C;UAGjBnE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAY,UAAA,UAAAgG,GAAA,CAAA5E,SAAA,CAAgB;UACLhC,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAY,UAAA,SAAAgG,GAAA,CAAA5E,SAAA,CAAe;UACvDhC,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAuG,GAAA,CAAA5E,SAAA,8DACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}