/* Create User Modal Styles */
.modal-overlay {
  backdrop-filter: blur(4px);
}

.modal-content {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Form styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #4f5fad;
  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus {
  outline: none;
  border-color: #4f5fad;
  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #4f5fad, #6d78c9);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode styles */
.dark .form-input,
.dark .form-select {
  background-color: #1e1e1e;
  border-color: #2a2a2a;
  color: #e0e0e0;
}

.dark .form-input:focus,
.dark .form-select:focus {
  border-color: #6d78c9;
  box-shadow: 0 0 0 3px rgba(109, 120, 201, 0.1);
}

.dark .btn-secondary {
  background: transparent;
  color: #a0a0a0;
  border-color: #2a2a2a;
}

.dark .btn-secondary:hover {
  background: #2a2a2a;
  border-color: #3a3a3a;
}
