{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AiChatbotComponent } from './components/ai-chatbot/ai-chatbot.component';\nimport { ToastContainerComponent } from './components/toast-container/toast-container.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [AiChatbotComponent, ToastContainerComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule],\n    exports: [AiChatbotComponent, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "HttpClientModule", "AiChatbotComponent", "ToastContainerComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\n\r\nimport { AiChatbotComponent } from './components/ai-chatbot/ai-chatbot.component';\r\nimport { ToastContainerComponent } from './components/toast-container/toast-container.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AiChatbotComponent,\r\n    ToastContainerComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    RouterModule,\r\n    HttpClientModule\r\n  ],\r\n  exports: [\r\n    AiChatbotComponent,\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    RouterModule\r\n  ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AAGvD,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,uBAAuB,QAAQ,wDAAwD;;AAsBhG,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAdrBP,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB,EAIhBJ,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;IAAA;EAAA;;;2EAGHI,YAAY;IAAAC,YAAA,GAlBrBH,kBAAkB,EAClBC,uBAAuB;IAAAG,OAAA,GAGvBT,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB;IAAAM,OAAA,GAGhBL,kBAAkB,EAClBL,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}