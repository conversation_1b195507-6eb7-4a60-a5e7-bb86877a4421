{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"src/app/services/logger.service\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 47);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵelement(4, \"div\", 50)(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_23_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      ctx_r15.toggleSearch();\n      return i0.ɵɵresetView(ctx_r15.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 56);\n    i0.ɵɵelementStart(4, \"span\", 57);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 58);\n    i0.ɵɵelement(7, \"i\", 59);\n    i0.ɵɵelementStart(8, \"span\", 57);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 60);\n    i0.ɵɵelementStart(11, \"button\", 58);\n    i0.ɵɵelement(12, \"i\", 61);\n    i0.ɵɵelementStart(13, \"span\", 57);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementStart(3, \"p\", 65);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 66);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelement(1, \"div\", 68);\n    i0.ɵɵelementStart(2, \"span\", 69);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 73);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"span\", 89);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.formatDateSeparator(message_r19.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"img\", 91);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.openUserProfile(message_r19.sender == null ? null : message_r19.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r19.sender == null ? null : message_r19.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r19.sender == null ? null : message_r19.sender.username);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r23.getUserColor(message_r19.sender == null ? null : message_r19.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r19.sender == null ? null : message_r19.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"div\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r24.formatMessageContent(message_r19.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 98);\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r34.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r34.formatMessageContent(message_r19.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"img\", 96);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.openImageViewer(message_r19));\n    })(\"load\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.onImageLoad($event, message_r19));\n    })(\"error\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.onImageError($event, message_r19));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r25.getImageUrl(message_r19), i0.ɵɵsanitizeUrl)(\"alt\", message_r19.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.content);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 104);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 106);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 107);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template, 1, 0, \"i\", 100);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template, 1, 0, \"i\", 101);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template, 1, 0, \"i\", 102);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template, 1, 0, \"i\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_1_Template, 4, 1, \"div\", 78);\n    i0.ɵɵelementStart(2, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const message_r19 = restoredCtx.$implicit;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.onMessageClick(message_r19, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const message_r19 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.onMessageContextMenu(message_r19, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_3_Template, 2, 2, \"div\", 80);\n    i0.ɵɵelementStart(4, \"div\", 81);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_5_Template, 2, 3, \"div\", 82);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_29_ng_container_1_div_6_Template, 2, 1, \"div\", 83);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_29_ng_container_1_div_7_Template, 3, 3, \"div\", 84);\n    i0.ɵɵelementStart(8, \"div\", 85)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, MessageChatComponent_div_29_ng_container_1_div_11_Template, 5, 4, \"div\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.shouldShowDateSeparator(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r19.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r19.sender == null ? null : message_r19.sender.id) !== ctx_r17.currentUserId && ctx_r17.shouldShowAvatar(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isGroupConversation() && (message_r19.sender == null ? null : message_r19.sender.id) !== ctx_r17.currentUserId && ctx_r17.shouldShowSenderName(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.getMessageType(message_r19) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.hasImage(message_r19));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r17.formatMessageTime(message_r19.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"img\", 109);\n    i0.ɵɵelementStart(2, \"div\", 110)(3, \"div\", 111);\n    i0.ɵɵelement(4, \"div\", 112)(5, \"div\", 113)(6, \"div\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r18.otherParticipant == null ? null : ctx_r18.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r18.otherParticipant == null ? null : ctx_r18.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_Template, 12, 14, \"ng-container\", 76);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_div_2_Template, 7, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages)(\"ngForTrackBy\", ctx_r8.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_i_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 115);\n  }\n}\nfunction MessageChatComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 116);\n  }\n}\nfunction MessageChatComponent_div_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const emoji_r53 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.insertEmoji(emoji_r53));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r53 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r53.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r53.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118)(2, \"h4\", 119);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 120);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_42_button_5_Template, 2, 2, \"button\", 121);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.getEmojisForCategory(ctx_r11.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 118)(2, \"h4\", 119);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 124)(5, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 126);\n    i0.ɵɵelement(7, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 128);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 129);\n    i0.ɵɵelement(12, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 128);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 131);\n    i0.ɵɵelement(17, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 128);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 133);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\";\nexport class MessageChatComponent {\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, logger, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.logger = logger;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n    this.MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n    this.MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n    this.currentPage = 1; // Page actuelle pour la pagination\n    this.isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n    this.hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n    this.subscriptions = new Subscription();\n    // Variables pour le sélecteur de thème\n    this.selectedTheme = 'theme-default'; // Thème par défaut\n    this.showThemeSelector = false; // Affichage du sélecteur de thème\n    // Variables pour le sélecteur d'émojis\n    this.showEmojiPicker = false;\n    // Variables pour les appels\n    this.incomingCall = null;\n    this.showCallModal = false;\n    this.commonEmojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n    this.TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    // Charger le thème sauvegardé\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n      this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);\n    }\n    // Récupérer les messages vocaux pour assurer leur persistance\n    this.loadVoiceMessages();\n    // S'abonner aux notifications en temps réel\n    this.subscribeToNotifications();\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1; // Réinitialiser à la page 1\n      this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires\n      this.logger.debug('MessageChat', `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`);\n      // Charger la conversation avec pagination (page 1, limit 10)\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage // Utiliser la page au lieu de l'offset\n      );\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  /**\n   * Charge les messages vocaux pour assurer leur persistance\n   */\n  loadVoiceMessages() {\n    this.logger.debug('MessageChat', 'Loading voice messages for persistence');\n    const sub = this.MessageService.getVoiceMessages().subscribe({\n      next: voiceMessages => {\n        this.logger.info('MessageChat', `Retrieved ${voiceMessages.length} voice messages`);\n        // Les messages vocaux sont maintenant chargés et disponibles dans le service\n        // Ils seront automatiquement associés aux conversations correspondantes\n        if (voiceMessages.length > 0) {\n          this.logger.debug('MessageChat', 'Voice messages loaded successfully');\n          // Forcer le rafraîchissement de la vue après le chargement des messages vocaux\n          setTimeout(() => {\n            this.cdr.detectChanges();\n            this.logger.debug('MessageChat', 'View refreshed after loading voice messages');\n          }, 100);\n        }\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error loading voice messages:', error);\n        // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue\n      }\n    });\n\n    this.subscriptions.add(sub);\n  }\n  /**\n   * Gère les erreurs et les affiche à l'utilisateur\n   * @param message Message d'erreur à afficher\n   * @param error Objet d'erreur\n   */\n  handleError(message, error) {\n    this.logger.error('MessageChat', message, error);\n    this.loading = false;\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // logique FileService\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  handleConversationLoaded(conversation) {\n    this.logger.info('MessageChat', `Handling loaded conversation: ${conversation.id}`);\n    this.logger.debug('MessageChat', `Conversation has ${conversation?.messages?.length || 0} messages and ${conversation?.participants?.length || 0} participants`);\n    // Log détaillé des messages pour le débogage\n    if (conversation?.messages && conversation.messages.length > 0) {\n      this.logger.debug('MessageChat', `First message details: id=${conversation.messages[0].id}, content=${conversation.messages[0].content?.substring(0, 20)}, sender=${conversation.messages[0].sender?.username}`);\n    }\n    this.conversation = conversation;\n    // Si la conversation n'a pas de messages, initialiser un tableau vide\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.logger.debug('MessageChat', 'No messages found in conversation');\n      // Récupérer les participants\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      // Initialiser un tableau vide pour les messages\n      this.messages = [];\n      this.logger.debug('MessageChat', 'Initialized empty messages array');\n    } else {\n      // Récupérer les messages de la conversation\n      const conversationMessages = [...(conversation?.messages || [])];\n      // Trier les messages par date (du plus ancien au plus récent)\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      // Log détaillé pour comprendre la structure des messages\n      if (conversationMessages.length > 0) {\n        const firstMessage = conversationMessages[0];\n        this.logger.debug('MessageChat', `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`);\n      }\n      // Utiliser directement tous les messages triés sans filtrage supplémentaire\n      this.messages = conversationMessages;\n      this.logger.debug('MessageChat', `Using all ${this.messages.length} messages from conversation`);\n      this.logger.debug('MessageChat', `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`);\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.logger.debug('MessageChat', `Other participant identified: ${this.otherParticipant?.username || 'Unknown'}`);\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.logger.debug('MessageChat', `Marking unread messages as read`);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.logger.debug('MessageChat', `Setting up subscriptions for conversation: ${this.conversation.id}`);\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n    this.logger.info('MessageChat', `Conversation loaded successfully`);\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          // Ajouter le nouveau message à la liste complète\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB; // Tri par ordre croissant pour l'affichage\n          });\n\n          this.logger.debug('MessageChat', `Added new message, now showing ${this.messages.length} messages`);\n          setTimeout(() => this.scrollToBottom(), 100);\n          // Marquer le message comme lu s'il vient d'un autre utilisateur\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {\n            this.logger.error('MessageChat', 'Error marking message as read:', error);\n          }\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    // Validate file size (e.g., 5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    // Validate file type\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  /**\n   * Gère l'événement de frappe de l'utilisateur\n   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n   */\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    // Stocker l'ID de conversation pour éviter les erreurs TypeScript\n    const conversationId = this.conversation.id;\n    // Annuler le timer précédent\n    clearTimeout(this.typingTimer);\n    // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.logger.debug('MessageChat', 'Starting typing indicator');\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {\n          this.logger.debug('MessageChat', 'Typing indicator started successfully');\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error starting typing indicator:', error);\n        }\n      });\n    }\n    // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.logger.debug('MessageChat', 'Stopping typing indicator due to inactivity');\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n          },\n          error: error => {\n            this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n          }\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  /**\n   * Affiche ou masque le sélecteur de thème\n   */\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n    // Fermer le sélecteur de thème lorsqu'on clique ailleurs\n    if (this.showThemeSelector) {\n      setTimeout(() => {\n        const clickHandler = event => {\n          const target = event.target;\n          if (!target.closest('.theme-selector')) {\n            this.showThemeSelector = false;\n            document.removeEventListener('click', clickHandler);\n          }\n        };\n        document.addEventListener('click', clickHandler);\n      }, 0);\n    }\n  }\n  /**\n   * Change le thème de la conversation\n   * @param theme Nom du thème à appliquer\n   */\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions\n    localStorage.setItem('chat-theme', theme);\n    this.logger.debug('MessageChat', `Theme changed to: ${theme}`);\n  }\n  sendMessage() {\n    this.logger.info('MessageChat', `Attempting to send message`);\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug('MessageChat', `Authentication check: token=${!!token}, userId=${this.currentUserId}`);\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      this.logger.warn('MessageChat', `Cannot send message: form invalid or missing user IDs`);\n      return;\n    }\n    // Arrêter l'indicateur de frappe lorsqu'un message est envoyé\n    this.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true // Marquer comme en attente\n    };\n    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n    if (this.selectedFile) {\n      // Déterminer le type de fichier\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        // Pour les images, ajouter un aperçu immédiat\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      // Définir le type de message en fonction du type de fichier\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    // Ajouter immédiatement le message temporaire à la liste\n    this.messages = [...this.messages, tempMessage];\n    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n    const fileToSend = this.selectedFile; // Sauvegarder une référence\n    this.messageForm.reset();\n    this.removeAttachment();\n    // Forcer le défilement vers le bas immédiatement\n    setTimeout(() => this.scrollToBottom(true), 50);\n    // Maintenant, envoyer le message au serveur\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT).subscribe({\n      next: message => {\n        this.logger.info('MessageChat', `Message sent successfully: ${message?.id || 'unknown'}`);\n        // Remplacer le message temporaire par le message réel\n        this.messages = this.messages.map(msg => msg.id === tempMessage.id ? message : msg);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.logger.error('MessageChat', `Error sending message:`, error);\n        // Marquer le message temporaire comme échoué\n        this.messages = this.messages.map(msg => {\n          if (msg.id === tempMessage.id) {\n            return {\n              ...msg,\n              isPending: false,\n              isError: true\n            };\n          }\n          return msg;\n        });\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) {\n      return 'Unknown time';\n    }\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      // Format heure:minute sans les secondes, comme dans l'image de référence\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message time:', error);\n      return 'Invalid time';\n    }\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  formatMessageDate(timestamp) {\n    if (!timestamp) {\n      return 'Unknown date';\n    }\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      // Format pour l'affichage comme dans l'image de référence\n      const options = {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      };\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      // Format pour les autres jours (comme dans l'image)\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message date:', error);\n      return 'Invalid date';\n    }\n  }\n  shouldShowDateHeader(index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = this.messages[index];\n      const prevMsg = this.messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) {\n        return true;\n      }\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error checking date header:', error);\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) {\n      return 'unknown-date';\n    }\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting date from timestamp:', error);\n      return 'invalid-date';\n    }\n  }\n  getMessageType(message) {\n    if (!message) {\n      return MessageType.TEXT;\n    }\n    try {\n      // Vérifier d'abord le type de message explicite\n      if (message.type) {\n        // Convertir les types en minuscules en leurs équivalents en majuscules\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      // Ensuite, vérifier les pièces jointes\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          // Gérer les différentes formes de types d'attachements\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        // Type par défaut pour les pièces jointes\n        return MessageType.FILE;\n      }\n      // Type par défaut\n      return MessageType.TEXT;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting message type:', error);\n      return MessageType.TEXT;\n    }\n  }\n  // Méthode auxiliaire pour vérifier si un message contient une image\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) {\n      return false;\n    }\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE_LOWER) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    // Chercher une pièce jointe de type message vocal ou audio\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) {\n      return 0;\n    }\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  // Méthode pour obtenir l'URL de l'image en toute sécurité\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  getMessageTypeClass(message) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === this.currentUserId || message.sender?._id === this.currentUserId || message.senderId === this.currentUserId;\n      // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)\n      // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)\n      // Couleurs et forme adaptées exactement à l'image de référence mobile\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      // Vérifier si le message contient une image\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            // Pour les images, on utilise un style sans bordure\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Vérifier le type de message\n      if (messageType === MessageType.IMAGE || messageType === MessageType.IMAGE_LOWER) {\n        // Pour les images, on utilise un style sans bordure\n        return `p-1 max-w-xs`;\n      } else if (messageType === MessageType.FILE || messageType === MessageType.FILE_LOWER) {\n        return `${baseClass} p-3`;\n      }\n      // Type par défaut (texte)\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting message type class:', error);\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n  // Méthode pour détecter le défilement vers le haut et charger plus de messages\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      // Afficher un indicateur de chargement en haut de la liste\n      this.showLoadingIndicator();\n      // Sauvegarder la hauteur actuelle et la position des messages\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n      // Charger plus de messages avec un délai réduit\n      this.loadMoreMessages();\n      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n      // en utilisant le premier message visible comme ancre\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Afficher un indicateur de chargement en haut de la liste\n  showLoadingIndicator() {\n    // Créer l'indicateur s'il n'existe pas déjà\n    if (!document.getElementById('message-loading-indicator')) {\n      const indicator = document.createElement('div');\n      indicator.id = 'message-loading-indicator';\n      indicator.className = 'text-center py-2 text-gray-500 text-sm';\n      indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n      if (this.messagesContainer?.nativeElement) {\n        this.messagesContainer.nativeElement.prepend(indicator);\n      }\n    }\n  }\n  // Masquer l'indicateur de chargement\n  hideLoadingIndicator() {\n    const indicator = document.getElementById('message-loading-indicator');\n    if (indicator && indicator.parentNode) {\n      indicator.parentNode.removeChild(indicator);\n    }\n  }\n  // Méthode pour charger plus de messages (style Facebook Messenger)\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page pour charger les messages plus anciens\n    this.currentPage++;\n    // Charger plus de messages depuis le serveur avec pagination\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages plus efficacement\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        // Faire défiler vers le bas si:\n        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n        // - ou si l'utilisateur est déjà proche du bas\n        if (force || isScrolledToBottom) {\n          // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  // Méthode pour ouvrir l'image en plein écran (style Messenger)\n  /**\n   * Active/désactive l'enregistrement vocal\n   */\n  toggleVoiceRecording() {\n    this.isRecordingVoice = !this.isRecordingVoice;\n    if (!this.isRecordingVoice) {\n      // Si on désactive l'enregistrement, réinitialiser la durée\n      this.voiceRecordingDuration = 0;\n    }\n  }\n  /**\n   * Gère la fin de l'enregistrement vocal\n   * @param audioBlob Blob audio enregistré\n   */\n  onVoiceRecordingComplete(audioBlob) {\n    this.logger.debug('MessageChat', 'Voice recording complete, size:', audioBlob.size);\n    if (!this.conversation?.id && !this.otherParticipant?.id) {\n      this.toastService.showError('No conversation or recipient selected');\n      this.isRecordingVoice = false;\n      return;\n    }\n    // Récupérer l'ID du destinataire\n    const receiverId = this.otherParticipant?.id || '';\n    // Envoyer le message vocal\n    this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n      next: message => {\n        this.logger.debug('MessageChat', 'Voice message sent:', message);\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n        this.scrollToBottom(true);\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error sending voice message:', error);\n        this.toastService.showError('Failed to send voice message');\n        this.isRecordingVoice = false;\n      }\n    });\n  }\n  /**\n   * Gère l'annulation de l'enregistrement vocal\n   */\n  onVoiceRecordingCancelled() {\n    this.logger.debug('MessageChat', 'Voice recording cancelled');\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    // Ouvrir l'image dans un nouvel onglet\n    window.open(imageUrl, '_blank');\n    this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue\n   * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n   * et que le défilement est maintenu\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Forcer la détection des changements pour les messages vocaux\n    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n    if (this.messages.some(msg => msg.type === MessageType.VOICE_MESSAGE)) {\n      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        this.cdr.detectChanges();\n      }, 0);\n    }\n  }\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  stopTypingIndicator() {\n    if (this.isCurrentlyTyping && this.conversation?.id) {\n      this.isCurrentlyTyping = false;\n      clearTimeout(this.typingTimer);\n      this.logger.debug('MessageChat', 'Stopping typing indicator');\n      // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript\n      const conversationId = this.conversation?.id;\n      if (conversationId) {\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n          },\n          error: error => {\n            this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n          }\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation\n    this.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    clearTimeout(this.typingTimeout);\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Bascule l'affichage du sélecteur d'émojis\n   */\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    if (this.showEmojiPicker) {\n      this.showThemeSelector = false;\n    }\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    // S'abonner aux nouvelles notifications\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.logger.debug('MessageChat', `Nouvelle notification reçue: ${notification.type}`);\n        // Si c'est une notification de message et que nous sommes dans la conversation concernée\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          // Marquer automatiquement comme lue\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Erreur lors de la réception des notifications:', error);\n      }\n    });\n    this.subscriptions.add(notificationSub);\n    // S'abonner aux appels entrants\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.logger.debug('MessageChat', `Appel entrant de: ${call.caller.username}`);\n          this.incomingCall = call;\n          this.showCallModal = true;\n          // Jouer la sonnerie\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n  }\n  /**\n   * Initie un appel audio ou vidéo avec l'autre participant\n   * @param type Type d'appel (AUDIO ou VIDEO)\n   */\n  initiateCall(type) {\n    if (!this.otherParticipant || !this.otherParticipant.id) {\n      console.error(\"Impossible d'initier un appel: participant invalide\");\n      return;\n    }\n    this.logger.info('MessageChat', `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`);\n    // Utiliser le service d'appel pour initier l'appel\n    this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel initié avec succès:', call);\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel. Veuillez réessayer.\");\n      }\n    });\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall() {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à accepter');\n      return;\n    }\n    this.logger.info('MessageChat', `Acceptation de l'appel de ${this.incomingCall.caller.username}`);\n    this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel accepté avec succès:', call);\n        this.showCallModal = false;\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors de l'acceptation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'accepter l'appel. Veuillez réessayer.\");\n        this.showCallModal = false;\n        this.incomingCall = null;\n      }\n    });\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall() {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');\n      return;\n    }\n    this.logger.info('MessageChat', `Rejet de l'appel de ${this.incomingCall.caller.username}`);\n    this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors du rejet de l'appel:\", error);\n        this.showCallModal = false;\n        this.incomingCall = null;\n      }\n    });\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall() {\n    // Utiliser une variable pour stocker la dernière valeur de l'observable\n    let activeCall = null;\n    // S'abonner à l'observable pour obtenir la valeur actuelle\n    const sub = this.MessageService.activeCall$.subscribe(call => {\n      activeCall = call;\n      if (!activeCall) {\n        this.logger.error('MessageChat', 'Aucun appel actif à terminer');\n        return;\n      }\n      this.logger.info('MessageChat', `Fin de l'appel`);\n      this.MessageService.endCall(activeCall.id).subscribe({\n        next: call => {\n          this.logger.info('MessageChat', 'Appel terminé avec succès:', call);\n        },\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors de la fin de l'appel:\", error);\n        }\n      });\n    });\n    // Se désabonner immédiatement après avoir obtenu la valeur\n    sub.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LoggerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 48,\n      vars: 46,\n      consts: [[2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid #10b981\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid #ffffff;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed #10b981;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [3, \"isVisible\", \"activeCall\", \"callType\", \"otherParticipant\", \"callEnded\", \"callAccepted\", \"callRejected\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid #ffffff\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed #10b981\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #10b981\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 15, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"main\", 23, 24);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_24_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_24_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_24_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_24_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 0, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 4, 0, \"div\", 26);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 7, 1, \"div\", 27);\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 3, 3, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"footer\", 29)(31, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_31_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(32, \"div\", 31)(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(36, \"i\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 36)(38, \"textarea\", 37);\n          i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_38_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"input\", function MessageChatComponent_Template_textarea_input_38_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_38_listener() {\n            return ctx.onInputFocus();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"button\", 38);\n          i0.ɵɵtemplate(40, MessageChatComponent_i_40_Template, 1, 0, \"i\", 39);\n          i0.ɵɵtemplate(41, MessageChatComponent_div_41_Template, 1, 0, \"div\", 40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 6, 1, \"div\", 41);\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 20, 0, \"div\", 42);\n          i0.ɵɵelementStart(44, \"input\", 43, 44);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_44_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 1, 0, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"app-call-interface\", 46);\n          i0.ɵɵlistener(\"callEnded\", function MessageChatComponent_Template_app_call_interface_callEnded_47_listener() {\n            return ctx.endCall();\n          })(\"callAccepted\", function MessageChatComponent_Template_app_call_interface_callAccepted_47_listener($event) {\n            return ctx.onCallAccepted($event);\n          })(\"callRejected\", function MessageChatComponent_Template_app_call_interface_callRejected_47_listener() {\n            return ctx.onCallRejected();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n          i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isVisible\", ctx.isInCall)(\"activeCall\", ctx.activeCall)(\"callType\", ctx.callType)(\"otherParticipant\", ctx.otherParticipant);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName],\n      styles: [_c2, _c2, \"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_23_Template_button_click_2_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r7", "username", "ctx_r21", "formatDateSeparator", "message_r19", "timestamp", "MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener", "_r30", "$implicit", "ctx_r28", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r23", "getUserColor", "ctx_r24", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r34", "currentUserId", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener", "_r38", "ctx_r36", "openImageViewer", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r39", "onImageLoad", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r41", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template", "ctx_r25", "getImageUrl", "MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_29_ng_container_1_div_1_Template", "MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r50", "ctx_r49", "onMessageClick", "MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r51", "onMessageContextMenu", "MessageChatComponent_div_29_ng_container_1_div_3_Template", "MessageChatComponent_div_29_ng_container_1_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_6_Template", "MessageChatComponent_div_29_ng_container_1_div_7_Template", "MessageChatComponent_div_29_ng_container_1_div_11_Template", "ɵɵelementContainerEnd", "ctx_r17", "shouldShowDateSeparator", "i_r20", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r18", "MessageChatComponent_div_29_ng_container_1_Template", "MessageChatComponent_div_29_div_2_Template", "ctx_r8", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_42_button_5_Template_button_click_0_listener", "_r55", "emoji_r53", "ctx_r54", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_42_button_5_Template", "ctx_r11", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_43_Template_button_click_5_listener", "_r57", "ctx_r56", "triggerFileInput", "MessageChatComponent_div_43_Template_button_click_10_listener", "ctx_r58", "MessageChatComponent_div_43_Template_button_click_15_listener", "ctx_r59", "openCamera", "MessageChatComponent_div_46_Template_div_click_0_listener", "_r61", "ctx_r60", "closeAllMenus", "MessageChatComponent", "constructor", "MessageService", "route", "authService", "fb", "statusService", "router", "toastService", "logger", "cdr", "conversation", "loading", "currentUsername", "selectedFile", "previewUrl", "isUploading", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showEmojiPicker", "incomingCall", "showCallModal", "commonEmojis", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "messageForm", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "debug", "loadVoiceMessages", "subscribeToNotifications", "routeSub", "params", "pipe", "getConversation", "subscribe", "next", "handleConversationLoaded", "error", "handleError", "add", "sub", "getVoiceMessages", "voiceMessages", "info", "length", "setTimeout", "detectChanges", "message", "showError", "getFileIcon", "mimeType", "startsWith", "includes", "getFileType", "typeMap", "key", "value", "Object", "entries", "participants", "substring", "find", "p", "_id", "conversationMessages", "sort", "a", "b", "timeA", "Date", "getTime", "timeB", "firstMessage", "senderId", "receiver", "receiverId", "scrollToBottom", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "conversationId", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "event", "userId", "clearTimeout", "typingTimeout", "unreadMessages", "msg", "isRead", "for<PERSON>ach", "onFileSelected", "file", "target", "files", "size", "validTypes", "type", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeAttachment", "fileInput", "nativeElement", "onTyping", "typingTimer", "startTyping", "stopTyping", "toggleThemeSelector", "clickHandler", "closest", "document", "removeEventListener", "addEventListener", "changeTheme", "theme", "setItem", "sendMessage", "token", "invalid", "warn", "stopTypingIndicator", "get", "tempMessage", "isPending", "fileType", "attachments", "url", "toString", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "map", "isError", "date", "toLocaleTimeString", "hour", "minute", "hour12", "lastActiveDate", "now", "diffHours", "Math", "abs", "toLocaleDateString", "formatMessageDate", "today", "options", "weekday", "toDateString", "yesterday", "setDate", "getDate", "day", "toUpperCase", "shouldShowDateHeader", "index", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "msgType", "AUDIO", "VIDEO", "SYSTEM", "attachment", "attachmentTypeStr", "isVoiceMessage", "VOICE_MESSAGE", "VOICE_MESSAGE_LOWER", "some", "att", "metadata", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "duration", "getMessageTypeClass", "isCurrentUser", "baseClass", "messageType", "IMAGE_LOWER", "FILE_LOWER", "onScroll", "container", "scrollTop", "showLoadingIndicator", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "hideLoadingIndicator", "messagesContainer", "messageElements", "querySelectorAll", "i", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "messageId", "getAttribute", "m", "querySelector", "getElementById", "indicator", "createElement", "className", "innerHTML", "prepend", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "oldMessages", "existingIds", "Set", "newMessages", "has", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "toggleVoiceRecording", "onVoiceRecordingComplete", "audioBlob", "sendVoiceMessage", "onVoiceRecordingCancelled", "openImageFullscreen", "imageUrl", "window", "open", "ngAfterViewChecked", "ngOnDestroy", "unsubscribe", "goBackToConversations", "navigate", "toggleEmojiPicker", "control", "currentValue", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "notification", "mark<PERSON><PERSON><PERSON>", "callSub", "incomingCall$", "call", "caller", "play", "initiateCall", "console", "acceptCall", "rejectCall", "endCall", "activeCall", "activeCall$", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "ToastService", "i7", "LoggerService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "startVideoCall", "MessageChatComponent_Template_button_click_17_listener", "startVoiceCall", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "toggleMainMenu", "MessageChatComponent_div_23_Template", "MessageChatComponent_Template_main_scroll_24_listener", "MessageChatComponent_Template_main_dragover_24_listener", "onDragOver", "MessageChatComponent_Template_main_dragleave_24_listener", "onDragLeave", "MessageChatComponent_Template_main_drop_24_listener", "onDrop", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_form_ngSubmit_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_button_click_35_listener", "toggleAttachmentMenu", "MessageChatComponent_Template_textarea_keydown_38_listener", "onInputKeyDown", "MessageChatComponent_Template_textarea_input_38_listener", "onInputChange", "MessageChatComponent_Template_textarea_focus_38_listener", "onInputFocus", "MessageChatComponent_i_40_Template", "MessageChatComponent_div_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_input_change_44_listener", "MessageChatComponent_div_46_Template", "MessageChatComponent_Template_app_call_interface_callEnded_47_listener", "MessageChatComponent_Template_app_call_interface_callAccepted_47_listener", "onCallAccepted", "MessageChatComponent_Template_app_call_interface_callRejected_47_listener", "onCallRejected", "isUserTyping", "searchMode", "isDragOver", "isLoading", "showAttachmentMenu", "isInputDisabled", "valid", "isSendingMessage", "getFileAcceptTypes", "isInCall", "callType"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnIni<PERSON>,\r\n  On<PERSON><PERSON><PERSON>,\r\n  <PERSON>Child,\r\n  ElementRef,\r\n  AfterViewChecked,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Subscription, combineLatest, Observable, of } from 'rxjs';\r\nimport { User } from '@app/models/user.model';\r\nimport { UserStatusService } from 'src/app/services/user-status.service';\r\nimport {\r\n  Message,\r\n  Conversation,\r\n  Attachment,\r\n  MessageType,\r\n  CallType,\r\n} from 'src/app/models/message.model';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\r\nimport { MessageService } from '@app/services/message.service';\r\nimport { LoggerService } from 'src/app/services/logger.service';\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: 'message-chat.component.html',\r\n  styleUrls: ['./message-chat.component.css', './message-chat-magic.css'],\r\n})\r\nexport class MessageChatComponent\r\n  implements OnInit, OnDestroy, AfterViewChecked\r\n{\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput', { static: false })\r\n  fileInput!: ElementRef<HTMLInputElement>;\r\n\r\n  messages: Message[] = [];\r\n  messageForm: FormGroup;\r\n  conversation: Conversation | null = null;\r\n  loading = true;\r\n  error: any;\r\n  currentUserId: string | null = null;\r\n  currentUsername: string = 'You';\r\n  otherParticipant: User | null = null;\r\n  selectedFile: File | null = null;\r\n  previewUrl: string | ArrayBuffer | null = null;\r\n  isUploading = false;\r\n  isTyping = false;\r\n  typingTimeout: any;\r\n  isRecordingVoice = false;\r\n  voiceRecordingDuration = 0;\r\n\r\n  private readonly MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\r\n  private readonly MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\r\n  private readonly MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\r\n  private currentPage = 1; // Page actuelle pour la pagination\r\n  isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\r\n  hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\r\n  private subscriptions: Subscription = new Subscription();\r\n\r\n  // Variables pour le sélecteur de thème\r\n  selectedTheme: string = 'theme-default'; // Thème par défaut\r\n  showThemeSelector: boolean = false; // Affichage du sélecteur de thème\r\n\r\n  // Variables pour le sélecteur d'émojis\r\n  showEmojiPicker: boolean = false;\r\n\r\n  // Variables pour les appels\r\n  incomingCall: any = null;\r\n  showCallModal: boolean = false;\r\n\r\n  commonEmojis: string[] = [\r\n    '😀',\r\n    '😃',\r\n    '😄',\r\n    '😁',\r\n    '😆',\r\n    '😅',\r\n    '😂',\r\n    '🤣',\r\n    '😊',\r\n    '😇',\r\n    '🙂',\r\n    '🙃',\r\n    '😉',\r\n    '😌',\r\n    '😍',\r\n    '🥰',\r\n    '😘',\r\n    '😗',\r\n    '😙',\r\n    '😚',\r\n    '😋',\r\n    '😛',\r\n    '😝',\r\n    '😜',\r\n    '🤪',\r\n    '🤨',\r\n    '🧐',\r\n    '🤓',\r\n    '😎',\r\n    '🤩',\r\n    '😏',\r\n    '😒',\r\n    '😞',\r\n    '😔',\r\n    '😟',\r\n    '😕',\r\n    '🙁',\r\n    '☹️',\r\n    '😣',\r\n    '😖',\r\n    '😫',\r\n    '😩',\r\n    '🥺',\r\n    '😢',\r\n    '😭',\r\n    '😤',\r\n    '😠',\r\n    '😡',\r\n    '🤬',\r\n    '🤯',\r\n    '😳',\r\n    '🥵',\r\n    '🥶',\r\n    '😱',\r\n    '😨',\r\n    '😰',\r\n    '😥',\r\n    '😓',\r\n    '🤗',\r\n    '🤔',\r\n    '👍',\r\n    '👎',\r\n    '👏',\r\n    '🙌',\r\n    '👐',\r\n    '🤲',\r\n    '🤝',\r\n    '🙏',\r\n    '✌️',\r\n    '🤞',\r\n    '❤️',\r\n    '🧡',\r\n    '💛',\r\n    '💚',\r\n    '💙',\r\n    '💜',\r\n    '🖤',\r\n    '💔',\r\n    '💯',\r\n    '💢',\r\n  ];\r\n\r\n  constructor(\r\n    private MessageService: MessageService,\r\n    public route: ActivatedRoute,\r\n    private authService: AuthuserService,\r\n    private fb: FormBuilder,\r\n    public statusService: UserStatusService,\r\n    public router: Router,\r\n    private toastService: ToastService,\r\n    private logger: LoggerService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.messageForm = this.fb.group({\r\n      content: ['', [Validators.maxLength(1000)]],\r\n    });\r\n  }\r\n  ngOnInit(): void {\r\n    this.currentUserId = this.authService.getCurrentUserId();\r\n\r\n    // Charger le thème sauvegardé\r\n    const savedTheme = localStorage.getItem('chat-theme');\r\n    if (savedTheme) {\r\n      this.selectedTheme = savedTheme;\r\n      this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);\r\n    }\r\n\r\n    // Récupérer les messages vocaux pour assurer leur persistance\r\n    this.loadVoiceMessages();\r\n\r\n    // S'abonner aux notifications en temps réel\r\n    this.subscribeToNotifications();\r\n\r\n    const routeSub = this.route.params\r\n      .pipe(\r\n        filter((params) => params['id']),\r\n        distinctUntilChanged(),\r\n        switchMap((params) => {\r\n          this.loading = true;\r\n          this.messages = [];\r\n          this.currentPage = 1; // Réinitialiser à la page 1\r\n          this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires\r\n\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`\r\n          );\r\n\r\n          // Charger la conversation avec pagination (page 1, limit 10)\r\n          return this.MessageService.getConversation(\r\n            params['id'],\r\n            this.MAX_MESSAGES_TO_LOAD,\r\n            this.currentPage // Utiliser la page au lieu de l'offset\r\n          );\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (conversation) => {\r\n          this.handleConversationLoaded(conversation);\r\n        },\r\n        error: (error) => {\r\n          this.handleError('Failed to load conversation', error);\r\n        },\r\n      });\r\n    this.subscriptions.add(routeSub);\r\n  }\r\n\r\n  /**\r\n   * Charge les messages vocaux pour assurer leur persistance\r\n   */\r\n  private loadVoiceMessages(): void {\r\n    this.logger.debug('MessageChat', 'Loading voice messages for persistence');\r\n\r\n    const sub = this.MessageService.getVoiceMessages().subscribe({\r\n      next: (voiceMessages) => {\r\n        this.logger.info(\r\n          'MessageChat',\r\n          `Retrieved ${voiceMessages.length} voice messages`\r\n        );\r\n\r\n        // Les messages vocaux sont maintenant chargés et disponibles dans le service\r\n        // Ils seront automatiquement associés aux conversations correspondantes\r\n        if (voiceMessages.length > 0) {\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            'Voice messages loaded successfully'\r\n          );\r\n\r\n          // Forcer le rafraîchissement de la vue après le chargement des messages vocaux\r\n          setTimeout(() => {\r\n            this.cdr.detectChanges();\r\n            this.logger.debug(\r\n              'MessageChat',\r\n              'View refreshed after loading voice messages'\r\n            );\r\n          }, 100);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageChat',\r\n          'Error loading voice messages:',\r\n          error\r\n        );\r\n        // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue\r\n      },\r\n    });\r\n\r\n    this.subscriptions.add(sub);\r\n  }\r\n\r\n  /**\r\n   * Gère les erreurs et les affiche à l'utilisateur\r\n   * @param message Message d'erreur à afficher\r\n   * @param error Objet d'erreur\r\n   */\r\n  private handleError(message: string, error: any): void {\r\n    this.logger.error('MessageChat', message, error);\r\n    this.loading = false;\r\n    this.error = error;\r\n    this.toastService.showError(message);\r\n  }\r\n\r\n  // logique FileService\r\n  getFileIcon(mimeType?: string): string {\r\n    if (!mimeType) return 'fa-file';\r\n    if (mimeType.startsWith('image/')) return 'fa-image';\r\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\r\n    if (mimeType.includes('word') || mimeType.includes('msword'))\r\n      return 'fa-file-word';\r\n    if (mimeType.includes('excel')) return 'fa-file-excel';\r\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\r\n    if (mimeType.includes('audio')) return 'fa-file-audio';\r\n    if (mimeType.includes('video')) return 'fa-file-video';\r\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\r\n      return 'fa-file-archive';\r\n    return 'fa-file';\r\n  }\r\n  getFileType(mimeType?: string): string {\r\n    if (!mimeType) return 'File';\r\n\r\n    const typeMap: Record<string, string> = {\r\n      'image/': 'Image',\r\n      'application/pdf': 'PDF',\r\n      'application/msword': 'Word Doc',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\r\n        'Word Doc',\r\n      'application/vnd.ms-excel': 'Excel',\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\r\n        'Excel',\r\n      'application/vnd.ms-powerpoint': 'PowerPoint',\r\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\r\n        'PowerPoint',\r\n      'audio/': 'Audio',\r\n      'video/': 'Video',\r\n      'application/zip': 'ZIP Archive',\r\n      'application/x-rar-compressed': 'RAR Archive',\r\n    };\r\n    for (const [key, value] of Object.entries(typeMap)) {\r\n      if (mimeType.includes(key)) return value;\r\n    }\r\n    return 'File';\r\n  }\r\n\r\n  private handleConversationLoaded(conversation: Conversation): void {\r\n    this.logger.info(\r\n      'MessageChat',\r\n      `Handling loaded conversation: ${conversation.id}`\r\n    );\r\n    this.logger.debug(\r\n      'MessageChat',\r\n      `Conversation has ${conversation?.messages?.length || 0} messages and ${\r\n        conversation?.participants?.length || 0\r\n      } participants`\r\n    );\r\n\r\n    // Log détaillé des messages pour le débogage\r\n    if (conversation?.messages && conversation.messages.length > 0) {\r\n      this.logger.debug(\r\n        'MessageChat',\r\n        `First message details: id=${\r\n          conversation.messages[0].id\r\n        }, content=${conversation.messages[0].content?.substring(\r\n          0,\r\n          20\r\n        )}, sender=${conversation.messages[0].sender?.username}`\r\n      );\r\n    }\r\n\r\n    this.conversation = conversation;\r\n\r\n    // Si la conversation n'a pas de messages, initialiser un tableau vide\r\n    if (!conversation?.messages || conversation.messages.length === 0) {\r\n      this.logger.debug('MessageChat', 'No messages found in conversation');\r\n\r\n      // Récupérer les participants\r\n      this.otherParticipant =\r\n        conversation?.participants?.find(\r\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\r\n        ) || null;\r\n\r\n      // Initialiser un tableau vide pour les messages\r\n      this.messages = [];\r\n\r\n      this.logger.debug('MessageChat', 'Initialized empty messages array');\r\n    } else {\r\n      // Récupérer les messages de la conversation\r\n      const conversationMessages = [...(conversation?.messages || [])];\r\n\r\n      // Trier les messages par date (du plus ancien au plus récent)\r\n      conversationMessages.sort((a, b) => {\r\n        const timeA =\r\n          a.timestamp instanceof Date\r\n            ? a.timestamp.getTime()\r\n            : new Date(a.timestamp as string).getTime();\r\n        const timeB =\r\n          b.timestamp instanceof Date\r\n            ? b.timestamp.getTime()\r\n            : new Date(b.timestamp as string).getTime();\r\n        return timeA - timeB;\r\n      });\r\n\r\n      // Log détaillé pour comprendre la structure des messages\r\n      if (conversationMessages.length > 0) {\r\n        const firstMessage = conversationMessages[0];\r\n        this.logger.debug(\r\n          'MessageChat',\r\n          `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`\r\n        );\r\n      }\r\n\r\n      // Utiliser directement tous les messages triés sans filtrage supplémentaire\r\n      this.messages = conversationMessages;\r\n\r\n      this.logger.debug(\r\n        'MessageChat',\r\n        `Using all ${this.messages.length} messages from conversation`\r\n      );\r\n\r\n      this.logger.debug(\r\n        'MessageChat',\r\n        `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`\r\n      );\r\n    }\r\n\r\n    this.otherParticipant =\r\n      conversation?.participants?.find(\r\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\r\n      ) || null;\r\n\r\n    this.logger.debug(\r\n      'MessageChat',\r\n      `Other participant identified: ${\r\n        this.otherParticipant?.username || 'Unknown'\r\n      }`\r\n    );\r\n\r\n    this.loading = false;\r\n    setTimeout(() => this.scrollToBottom(), 100);\r\n\r\n    this.logger.debug('MessageChat', `Marking unread messages as read`);\r\n    this.markMessagesAsRead();\r\n\r\n    if (this.conversation?.id) {\r\n      this.logger.debug(\r\n        'MessageChat',\r\n        `Setting up subscriptions for conversation: ${this.conversation.id}`\r\n      );\r\n      this.subscribeToConversationUpdates(this.conversation.id);\r\n      this.subscribeToNewMessages(this.conversation.id);\r\n      this.subscribeToTypingIndicators(this.conversation.id);\r\n    }\r\n\r\n    this.logger.info('MessageChat', `Conversation loaded successfully`);\r\n  }\r\n\r\n  private subscribeToConversationUpdates(conversationId: string): void {\r\n    const sub = this.MessageService.subscribeToConversationUpdates(\r\n      conversationId\r\n    ).subscribe({\r\n      next: (updatedConversation) => {\r\n        this.conversation = updatedConversation;\r\n        this.messages = updatedConversation.messages\r\n          ? [...updatedConversation.messages]\r\n          : [];\r\n        this.scrollToBottom();\r\n      },\r\n      error: (error) => {\r\n        this.toastService.showError('Connection to conversation updates lost');\r\n      },\r\n    });\r\n    this.subscriptions.add(sub);\r\n  }\r\n\r\n  private subscribeToNewMessages(conversationId: string): void {\r\n    const sub = this.MessageService.subscribeToNewMessages(\r\n      conversationId\r\n    ).subscribe({\r\n      next: (newMessage) => {\r\n        if (newMessage?.conversationId === this.conversation?.id) {\r\n          // Ajouter le nouveau message à la liste complète\r\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\r\n            const timeA =\r\n              a.timestamp instanceof Date\r\n                ? a.timestamp.getTime()\r\n                : new Date(a.timestamp as string).getTime();\r\n            const timeB =\r\n              b.timestamp instanceof Date\r\n                ? b.timestamp.getTime()\r\n                : new Date(b.timestamp as string).getTime();\r\n            return timeA - timeB; // Tri par ordre croissant pour l'affichage\r\n          });\r\n\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            `Added new message, now showing ${this.messages.length} messages`\r\n          );\r\n\r\n          setTimeout(() => this.scrollToBottom(), 100);\r\n\r\n          // Marquer le message comme lu s'il vient d'un autre utilisateur\r\n          if (\r\n            newMessage.sender?.id !== this.currentUserId &&\r\n            newMessage.sender?._id !== this.currentUserId\r\n          ) {\r\n            if (newMessage.id) {\r\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\r\n            }\r\n          }\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.toastService.showError('Connection to new messages lost');\r\n      },\r\n    });\r\n    this.subscriptions.add(sub);\r\n  }\r\n\r\n  private subscribeToTypingIndicators(conversationId: string): void {\r\n    const sub = this.MessageService.subscribeToTypingIndicator(\r\n      conversationId\r\n    ).subscribe({\r\n      next: (event) => {\r\n        if (event.userId !== this.currentUserId) {\r\n          this.isTyping = event.isTyping;\r\n          if (this.isTyping) {\r\n            clearTimeout(this.typingTimeout);\r\n            this.typingTimeout = setTimeout(() => {\r\n              this.isTyping = false;\r\n            }, 2000);\r\n          }\r\n        }\r\n      },\r\n    });\r\n    this.subscriptions.add(sub);\r\n  }\r\n\r\n  private markMessagesAsRead(): void {\r\n    const unreadMessages = this.messages.filter(\r\n      (msg) =>\r\n        !msg.isRead &&\r\n        (msg.receiver?.id === this.currentUserId ||\r\n          msg.receiver?._id === this.currentUserId)\r\n    );\r\n\r\n    unreadMessages.forEach((msg) => {\r\n      if (msg.id) {\r\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\r\n          error: (error) => {\r\n            this.logger.error(\r\n              'MessageChat',\r\n              'Error marking message as read:',\r\n              error\r\n            );\r\n          },\r\n        });\r\n        this.subscriptions.add(sub);\r\n      }\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (!file) return;\r\n\r\n    // Validate file size (e.g., 5MB max)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      this.toastService.showError('File size should be less than 5MB');\r\n      return;\r\n    }\r\n\r\n    // Validate file type\r\n    const validTypes = [\r\n      'image/jpeg',\r\n      'image/png',\r\n      'image/gif',\r\n      'application/pdf',\r\n      'application/msword',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n    ];\r\n    if (!validTypes.includes(file.type)) {\r\n      this.toastService.showError(\r\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.selectedFile = file;\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      this.previewUrl = reader.result;\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  removeAttachment(): void {\r\n    this.selectedFile = null;\r\n    this.previewUrl = null;\r\n    if (this.fileInput?.nativeElement) {\r\n      this.fileInput.nativeElement.value = '';\r\n    }\r\n  }\r\n\r\n  private typingTimer: any;\r\n  private isCurrentlyTyping = false;\r\n  private readonly TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\r\n  private readonly TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\r\n\r\n  /**\r\n   * Gère l'événement de frappe de l'utilisateur\r\n   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\r\n   */\r\n  onTyping(): void {\r\n    if (!this.conversation?.id || !this.currentUserId) {\r\n      return;\r\n    }\r\n\r\n    // Stocker l'ID de conversation pour éviter les erreurs TypeScript\r\n    const conversationId = this.conversation.id;\r\n\r\n    // Annuler le timer précédent\r\n    clearTimeout(this.typingTimer);\r\n\r\n    // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement\r\n    if (!this.isCurrentlyTyping) {\r\n      this.isCurrentlyTyping = true;\r\n      this.logger.debug('MessageChat', 'Starting typing indicator');\r\n\r\n      this.MessageService.startTyping(conversationId).subscribe({\r\n        next: () => {\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            'Typing indicator started successfully'\r\n          );\r\n        },\r\n        error: (error) => {\r\n          this.logger.error(\r\n            'MessageChat',\r\n            'Error starting typing indicator:',\r\n            error\r\n          );\r\n        },\r\n      });\r\n    }\r\n\r\n    // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité\r\n    this.typingTimer = setTimeout(() => {\r\n      if (this.isCurrentlyTyping) {\r\n        this.isCurrentlyTyping = false;\r\n        this.logger.debug(\r\n          'MessageChat',\r\n          'Stopping typing indicator due to inactivity'\r\n        );\r\n\r\n        this.MessageService.stopTyping(conversationId).subscribe({\r\n          next: () => {\r\n            this.logger.debug(\r\n              'MessageChat',\r\n              'Typing indicator stopped successfully'\r\n            );\r\n          },\r\n          error: (error) => {\r\n            this.logger.error(\r\n              'MessageChat',\r\n              'Error stopping typing indicator:',\r\n              error\r\n            );\r\n          },\r\n        });\r\n      }\r\n    }, this.TYPING_TIMEOUT);\r\n  }\r\n\r\n  /**\r\n   * Affiche ou masque le sélecteur de thème\r\n   */\r\n  toggleThemeSelector(): void {\r\n    this.showThemeSelector = !this.showThemeSelector;\r\n\r\n    // Fermer le sélecteur de thème lorsqu'on clique ailleurs\r\n    if (this.showThemeSelector) {\r\n      setTimeout(() => {\r\n        const clickHandler = (event: MouseEvent) => {\r\n          const target = event.target as HTMLElement;\r\n          if (!target.closest('.theme-selector')) {\r\n            this.showThemeSelector = false;\r\n            document.removeEventListener('click', clickHandler);\r\n          }\r\n        };\r\n        document.addEventListener('click', clickHandler);\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Change le thème de la conversation\r\n   * @param theme Nom du thème à appliquer\r\n   */\r\n  changeTheme(theme: string): void {\r\n    this.selectedTheme = theme;\r\n    this.showThemeSelector = false;\r\n\r\n    // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions\r\n    localStorage.setItem('chat-theme', theme);\r\n\r\n    this.logger.debug('MessageChat', `Theme changed to: ${theme}`);\r\n  }\r\n\r\n  sendMessage(): void {\r\n    this.logger.info('MessageChat', `Attempting to send message`);\r\n\r\n    // Vérifier l'authentification\r\n    const token = localStorage.getItem('token');\r\n    this.logger.debug(\r\n      'MessageChat',\r\n      `Authentication check: token=${!!token}, userId=${this.currentUserId}`\r\n    );\r\n\r\n    if (\r\n      (this.messageForm.invalid && !this.selectedFile) ||\r\n      !this.currentUserId ||\r\n      !this.otherParticipant?.id\r\n    ) {\r\n      this.logger.warn(\r\n        'MessageChat',\r\n        `Cannot send message: form invalid or missing user IDs`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Arrêter l'indicateur de frappe lorsqu'un message est envoyé\r\n    this.stopTypingIndicator();\r\n\r\n    const content = this.messageForm.get('content')?.value;\r\n\r\n    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\r\n    const tempMessage: Message = {\r\n      id: 'temp-' + new Date().getTime(),\r\n      content: content || '',\r\n      sender: {\r\n        id: this.currentUserId || '',\r\n        username: this.currentUsername,\r\n      },\r\n      receiver: {\r\n        id: this.otherParticipant.id,\r\n        username: this.otherParticipant.username || 'Recipient',\r\n      },\r\n      timestamp: new Date(),\r\n      isRead: false,\r\n      isPending: true, // Marquer comme en attente\r\n    };\r\n\r\n    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\r\n    if (this.selectedFile) {\r\n      // Déterminer le type de fichier\r\n      let fileType = 'file';\r\n      if (this.selectedFile.type.startsWith('image/')) {\r\n        fileType = 'image';\r\n\r\n        // Pour les images, ajouter un aperçu immédiat\r\n        if (this.previewUrl) {\r\n          tempMessage.attachments = [\r\n            {\r\n              id: 'temp-attachment',\r\n              url: this.previewUrl ? this.previewUrl.toString() : '',\r\n              type: MessageType.IMAGE,\r\n              name: this.selectedFile.name,\r\n              size: this.selectedFile.size,\r\n            },\r\n          ];\r\n        }\r\n      }\r\n\r\n      // Définir le type de message en fonction du type de fichier\r\n      if (fileType === 'image') {\r\n        tempMessage.type = MessageType.IMAGE;\r\n      } else if (fileType === 'file') {\r\n        tempMessage.type = MessageType.FILE;\r\n      }\r\n    }\r\n\r\n    // Ajouter immédiatement le message temporaire à la liste\r\n    this.messages = [...this.messages, tempMessage];\r\n\r\n    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\r\n    const fileToSend = this.selectedFile; // Sauvegarder une référence\r\n    this.messageForm.reset();\r\n    this.removeAttachment();\r\n\r\n    // Forcer le défilement vers le bas immédiatement\r\n    setTimeout(() => this.scrollToBottom(true), 50);\r\n\r\n    // Maintenant, envoyer le message au serveur\r\n    this.isUploading = true;\r\n\r\n    const sendSub = this.MessageService.sendMessage(\r\n      this.otherParticipant.id,\r\n      content,\r\n      fileToSend || undefined,\r\n      MessageType.TEXT\r\n    ).subscribe({\r\n      next: (message) => {\r\n        this.logger.info(\r\n          'MessageChat',\r\n          `Message sent successfully: ${message?.id || 'unknown'}`\r\n        );\r\n\r\n        // Remplacer le message temporaire par le message réel\r\n        this.messages = this.messages.map((msg) =>\r\n          msg.id === tempMessage.id ? message : msg\r\n        );\r\n\r\n        this.isUploading = false;\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('MessageChat', `Error sending message:`, error);\r\n\r\n        // Marquer le message temporaire comme échoué\r\n        this.messages = this.messages.map((msg) => {\r\n          if (msg.id === tempMessage.id) {\r\n            return {\r\n              ...msg,\r\n              isPending: false,\r\n              isError: true,\r\n            };\r\n          }\r\n          return msg;\r\n        });\r\n\r\n        this.isUploading = false;\r\n        this.toastService.showError('Failed to send message');\r\n      },\r\n    });\r\n\r\n    this.subscriptions.add(sendSub);\r\n  }\r\n\r\n  formatMessageTime(timestamp: string | Date | undefined): string {\r\n    if (!timestamp) {\r\n      return 'Unknown time';\r\n    }\r\n    try {\r\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\r\n      // Format heure:minute sans les secondes, comme dans l'image de référence\r\n      return date.toLocaleTimeString([], {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('MessageChat', 'Error formatting message time:', error);\r\n      return 'Invalid time';\r\n    }\r\n  }\r\n\r\n  formatLastActive(lastActive: string | Date | undefined): string {\r\n    if (!lastActive) return 'Offline';\r\n    const lastActiveDate =\r\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\r\n    const now = new Date();\r\n    const diffHours =\r\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\r\n\r\n    if (diffHours < 24) {\r\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      })}`;\r\n    }\r\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\r\n  }\r\n\r\n  formatMessageDate(timestamp: string | Date | undefined): string {\r\n    if (!timestamp) {\r\n      return 'Unknown date';\r\n    }\r\n\r\n    try {\r\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\r\n      const today = new Date();\r\n\r\n      // Format pour l'affichage comme dans l'image de référence\r\n      const options: Intl.DateTimeFormatOptions = {\r\n        weekday: 'short',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      };\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return date.toLocaleTimeString([], {\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n        });\r\n      }\r\n\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n      if (date.toDateString() === yesterday.toDateString()) {\r\n        return `LUN., ${date.toLocaleTimeString([], {\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n        })}`;\r\n      }\r\n\r\n      // Format pour les autres jours (comme dans l'image)\r\n      const day = date\r\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\r\n        .toUpperCase();\r\n      return `${day}., ${date.toLocaleTimeString([], {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      })}`;\r\n    } catch (error) {\r\n      this.logger.error('MessageChat', 'Error formatting message date:', error);\r\n      return 'Invalid date';\r\n    }\r\n  }\r\n\r\n  shouldShowDateHeader(index: number): boolean {\r\n    if (index === 0) return true;\r\n\r\n    try {\r\n      const currentMsg = this.messages[index];\r\n      const prevMsg = this.messages[index - 1];\r\n\r\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) {\r\n        return true;\r\n      }\r\n\r\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\r\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\r\n\r\n      return currentDate !== prevDate;\r\n    } catch (error) {\r\n      this.logger.error('MessageChat', 'Error checking date header:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\r\n    if (!timestamp) {\r\n      return 'unknown-date';\r\n    }\r\n\r\n    try {\r\n      return (\r\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\r\n      ).toDateString();\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageChat',\r\n        'Error getting date from timestamp:',\r\n        error\r\n      );\r\n      return 'invalid-date';\r\n    }\r\n  }\r\n  getMessageType(message: Message | null | undefined): MessageType {\r\n    if (!message) {\r\n      return MessageType.TEXT;\r\n    }\r\n\r\n    try {\r\n      // Vérifier d'abord le type de message explicite\r\n      if (message.type) {\r\n        // Convertir les types en minuscules en leurs équivalents en majuscules\r\n        const msgType = message.type.toString();\r\n        if (msgType === 'text' || msgType === 'TEXT') {\r\n          return MessageType.TEXT;\r\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\r\n          return MessageType.IMAGE;\r\n        } else if (msgType === 'file' || msgType === 'FILE') {\r\n          return MessageType.FILE;\r\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\r\n          return MessageType.AUDIO;\r\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\r\n          return MessageType.VIDEO;\r\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\r\n          return MessageType.SYSTEM;\r\n        }\r\n      }\r\n\r\n      // Ensuite, vérifier les pièces jointes\r\n      if (message.attachments?.length) {\r\n        const attachment = message.attachments[0];\r\n        if (attachment && attachment.type) {\r\n          const attachmentTypeStr = attachment.type.toString();\r\n\r\n          // Gérer les différentes formes de types d'attachements\r\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\r\n            return MessageType.IMAGE;\r\n          } else if (\r\n            attachmentTypeStr === 'file' ||\r\n            attachmentTypeStr === 'FILE'\r\n          ) {\r\n            return MessageType.FILE;\r\n          } else if (\r\n            attachmentTypeStr === 'audio' ||\r\n            attachmentTypeStr === 'AUDIO'\r\n          ) {\r\n            return MessageType.AUDIO;\r\n          } else if (\r\n            attachmentTypeStr === 'video' ||\r\n            attachmentTypeStr === 'VIDEO'\r\n          ) {\r\n            return MessageType.VIDEO;\r\n          }\r\n        }\r\n\r\n        // Type par défaut pour les pièces jointes\r\n        return MessageType.FILE;\r\n      }\r\n\r\n      // Type par défaut\r\n      return MessageType.TEXT;\r\n    } catch (error) {\r\n      this.logger.error('MessageChat', 'Error getting message type:', error);\r\n      return MessageType.TEXT;\r\n    }\r\n  }\r\n\r\n  // Méthode auxiliaire pour vérifier si un message contient une image\r\n  hasImage(message: Message | null | undefined): boolean {\r\n    if (!message || !message.attachments || message.attachments.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    const attachment = message.attachments[0];\r\n    if (!attachment || !attachment.type) {\r\n      return false;\r\n    }\r\n\r\n    const type = attachment.type.toString();\r\n    return type === 'IMAGE' || type === 'image';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si le message est un message vocal\r\n   */\r\n  isVoiceMessage(message: Message | null | undefined): boolean {\r\n    if (!message) {\r\n      return false;\r\n    }\r\n\r\n    // Vérifier le type du message\r\n    if (\r\n      message.type === MessageType.VOICE_MESSAGE ||\r\n      message.type === MessageType.VOICE_MESSAGE_LOWER\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les pièces jointes\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      return message.attachments.some((att) => {\r\n        const type = att.type?.toString();\r\n        return (\r\n          type === 'VOICE_MESSAGE' ||\r\n          type === 'voice_message' ||\r\n          (message.metadata?.isVoiceMessage &&\r\n            (type === 'AUDIO' || type === 'audio'))\r\n        );\r\n      });\r\n    }\r\n\r\n    // Vérifier les métadonnées\r\n    return !!message.metadata?.isVoiceMessage;\r\n  }\r\n\r\n  /**\r\n   * Récupère l'URL du message vocal\r\n   */\r\n  getVoiceMessageUrl(message: Message | null | undefined): string {\r\n    if (!message || !message.attachments || message.attachments.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    // Chercher une pièce jointe de type message vocal ou audio\r\n    const voiceAttachment = message.attachments.find((att) => {\r\n      const type = att.type?.toString();\r\n      return (\r\n        type === 'VOICE_MESSAGE' ||\r\n        type === 'voice_message' ||\r\n        type === 'AUDIO' ||\r\n        type === 'audio'\r\n      );\r\n    });\r\n\r\n    return voiceAttachment?.url || '';\r\n  }\r\n\r\n  /**\r\n   * Récupère la durée du message vocal\r\n   */\r\n  getVoiceMessageDuration(message: Message | null | undefined): number {\r\n    if (!message) {\r\n      return 0;\r\n    }\r\n\r\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\r\n    if (message.metadata?.duration) {\r\n      return message.metadata.duration;\r\n    }\r\n\r\n    // Sinon, essayer de récupérer depuis les pièces jointes\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      const voiceAttachment = message.attachments.find((att) => {\r\n        const type = att.type?.toString();\r\n        return (\r\n          type === 'VOICE_MESSAGE' ||\r\n          type === 'voice_message' ||\r\n          type === 'AUDIO' ||\r\n          type === 'audio'\r\n        );\r\n      });\r\n\r\n      if (voiceAttachment && voiceAttachment.duration) {\r\n        return voiceAttachment.duration;\r\n      }\r\n    }\r\n\r\n    return 0;\r\n  }\r\n\r\n  // Méthode pour obtenir l'URL de l'image en toute sécurité\r\n  getImageUrl(message: Message | null | undefined): string {\r\n    if (!message || !message.attachments || message.attachments.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    const attachment = message.attachments[0];\r\n    return attachment?.url || '';\r\n  }\r\n\r\n  getMessageTypeClass(message: Message | null | undefined): string {\r\n    if (!message) {\r\n      return 'bg-gray-100 rounded-lg px-4 py-2';\r\n    }\r\n\r\n    try {\r\n      const isCurrentUser =\r\n        message.sender?.id === this.currentUserId ||\r\n        message.sender?._id === this.currentUserId ||\r\n        message.senderId === this.currentUserId;\r\n\r\n      // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)\r\n      // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)\r\n      // Couleurs et forme adaptées exactement à l'image de référence mobile\r\n      const baseClass = isCurrentUser\r\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\r\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\r\n\r\n      const messageType = this.getMessageType(message);\r\n\r\n      // Vérifier si le message contient une image\r\n      if (message.attachments && message.attachments.length > 0) {\r\n        const attachment = message.attachments[0];\r\n        if (attachment && attachment.type) {\r\n          const attachmentTypeStr = attachment.type.toString();\r\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\r\n            // Pour les images, on utilise un style sans bordure\r\n            return `p-1 max-w-xs`;\r\n          } else if (\r\n            attachmentTypeStr === 'FILE' ||\r\n            attachmentTypeStr === 'file'\r\n          ) {\r\n            return `${baseClass} p-3`;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Vérifier le type de message\r\n      if (\r\n        messageType === MessageType.IMAGE ||\r\n        messageType === MessageType.IMAGE_LOWER\r\n      ) {\r\n        // Pour les images, on utilise un style sans bordure\r\n        return `p-1 max-w-xs`;\r\n      } else if (\r\n        messageType === MessageType.FILE ||\r\n        messageType === MessageType.FILE_LOWER\r\n      ) {\r\n        return `${baseClass} p-3`;\r\n      }\r\n\r\n      // Type par défaut (texte)\r\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageChat',\r\n        'Error getting message type class:',\r\n        error\r\n      );\r\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\r\n    }\r\n  }\r\n\r\n  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\r\n\r\n  // Méthode pour détecter le défilement vers le haut et charger plus de messages\r\n  onScroll(event: any): void {\r\n    const container = event.target;\r\n    const scrollTop = container.scrollTop;\r\n\r\n    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\r\n    if (\r\n      scrollTop < 50 &&\r\n      !this.isLoadingMore &&\r\n      this.conversation?.id &&\r\n      this.hasMoreMessages\r\n    ) {\r\n      // Afficher un indicateur de chargement en haut de la liste\r\n      this.showLoadingIndicator();\r\n\r\n      // Sauvegarder la hauteur actuelle et la position des messages\r\n      const oldScrollHeight = container.scrollHeight;\r\n      const firstVisibleMessage = this.getFirstVisibleMessage();\r\n\r\n      // Marquer comme chargement en cours\r\n      this.isLoadingMore = true;\r\n\r\n      // Charger plus de messages avec un délai réduit\r\n      this.loadMoreMessages();\r\n\r\n      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\r\n      // en utilisant le premier message visible comme ancre\r\n      requestAnimationFrame(() => {\r\n        const preserveScrollPosition = () => {\r\n          if (firstVisibleMessage) {\r\n            const messageElement = this.findMessageElement(\r\n              firstVisibleMessage.id\r\n            );\r\n            if (messageElement) {\r\n              // Faire défiler jusqu'à l'élément qui était visible avant\r\n              messageElement.scrollIntoView({ block: 'center' });\r\n            } else {\r\n              // Fallback: utiliser la différence de hauteur\r\n              const newScrollHeight = container.scrollHeight;\r\n              const scrollDiff = newScrollHeight - oldScrollHeight;\r\n              container.scrollTop = scrollTop + scrollDiff;\r\n            }\r\n          }\r\n\r\n          // Masquer l'indicateur de chargement\r\n          this.hideLoadingIndicator();\r\n        };\r\n\r\n        // Attendre que le DOM soit mis à jour\r\n        setTimeout(preserveScrollPosition, 100);\r\n      });\r\n    }\r\n  }\r\n\r\n  // Méthode pour trouver le premier message visible dans la vue\r\n  private getFirstVisibleMessage(): Message | null {\r\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\r\n      return null;\r\n\r\n    const container = this.messagesContainer.nativeElement;\r\n    const messageElements = container.querySelectorAll('.message-item');\r\n\r\n    for (let i = 0; i < messageElements.length; i++) {\r\n      const element = messageElements[i];\r\n      const rect = element.getBoundingClientRect();\r\n\r\n      // Si l'élément est visible dans la vue\r\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\r\n        const messageId = element.getAttribute('data-message-id');\r\n        return this.messages.find((m) => m.id === messageId) || null;\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  // Méthode pour trouver un élément de message par ID\r\n  private findMessageElement(\r\n    messageId: string | undefined\r\n  ): HTMLElement | null {\r\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\r\n    return this.messagesContainer.nativeElement.querySelector(\r\n      `[data-message-id=\"${messageId}\"]`\r\n    );\r\n  }\r\n\r\n  // Afficher un indicateur de chargement en haut de la liste\r\n  private showLoadingIndicator(): void {\r\n    // Créer l'indicateur s'il n'existe pas déjà\r\n    if (!document.getElementById('message-loading-indicator')) {\r\n      const indicator = document.createElement('div');\r\n      indicator.id = 'message-loading-indicator';\r\n      indicator.className = 'text-center py-2 text-gray-500 text-sm';\r\n      indicator.innerHTML =\r\n        '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\r\n\r\n      if (this.messagesContainer?.nativeElement) {\r\n        this.messagesContainer.nativeElement.prepend(indicator);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Masquer l'indicateur de chargement\r\n  private hideLoadingIndicator(): void {\r\n    const indicator = document.getElementById('message-loading-indicator');\r\n    if (indicator && indicator.parentNode) {\r\n      indicator.parentNode.removeChild(indicator);\r\n    }\r\n  }\r\n\r\n  // Méthode pour charger plus de messages (style Facebook Messenger)\r\n  loadMoreMessages(): void {\r\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\r\n      return;\r\n\r\n    // Marquer comme chargement en cours\r\n    this.isLoadingMore = true;\r\n\r\n    // Augmenter la page pour charger les messages plus anciens\r\n    this.currentPage++;\r\n\r\n    // Charger plus de messages depuis le serveur avec pagination\r\n    this.MessageService.getConversation(\r\n      this.conversation.id,\r\n      this.MAX_MESSAGES_TO_LOAD,\r\n      this.currentPage\r\n    ).subscribe({\r\n      next: (conversation) => {\r\n        if (\r\n          conversation &&\r\n          conversation.messages &&\r\n          conversation.messages.length > 0\r\n        ) {\r\n          // Sauvegarder les messages actuels\r\n          const oldMessages = [...this.messages];\r\n\r\n          // Créer un Set des IDs existants pour une recherche de doublons plus rapide\r\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\r\n\r\n          // Filtrer et trier les nouveaux messages plus efficacement\r\n          const newMessages = conversation.messages\r\n            .filter((msg) => !existingIds.has(msg.id))\r\n            .sort((a, b) => {\r\n              const timeA = new Date(a.timestamp as string).getTime();\r\n              const timeB = new Date(b.timestamp as string).getTime();\r\n              return timeA - timeB;\r\n            });\r\n\r\n          if (newMessages.length > 0) {\r\n            // Ajouter les nouveaux messages au début de la liste\r\n            this.messages = [...newMessages, ...oldMessages];\r\n\r\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\r\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\r\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\r\n            }\r\n\r\n            // Vérifier s'il y a plus de messages à charger\r\n            this.hasMoreMessages =\r\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\r\n          } else {\r\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\r\n            this.hasMoreMessages = false;\r\n          }\r\n        } else {\r\n          this.hasMoreMessages = false;\r\n        }\r\n\r\n        // Désactiver le flag de chargement après un court délai\r\n        // pour permettre au DOM de se mettre à jour\r\n        setTimeout(() => {\r\n          this.isLoadingMore = false;\r\n        }, 200);\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('MessageChat', 'Error loading more messages:', error);\r\n        this.isLoadingMore = false;\r\n        this.hideLoadingIndicator();\r\n        this.toastService.showError('Failed to load more messages');\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode utilitaire pour comparer les timestamps\r\n  private isSameTimestamp(\r\n    timestamp1: string | Date | undefined,\r\n    timestamp2: string | Date | undefined\r\n  ): boolean {\r\n    if (!timestamp1 || !timestamp2) return false;\r\n\r\n    try {\r\n      const time1 =\r\n        timestamp1 instanceof Date\r\n          ? timestamp1.getTime()\r\n          : new Date(timestamp1 as string).getTime();\r\n      const time2 =\r\n        timestamp2 instanceof Date\r\n          ? timestamp2.getTime()\r\n          : new Date(timestamp2 as string).getTime();\r\n      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  scrollToBottom(force: boolean = false): void {\r\n    try {\r\n      if (!this.messagesContainer?.nativeElement) return;\r\n\r\n      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\r\n      requestAnimationFrame(() => {\r\n        const container = this.messagesContainer.nativeElement;\r\n        const isScrolledToBottom =\r\n          container.scrollHeight - container.clientHeight <=\r\n          container.scrollTop + 150;\r\n\r\n        // Faire défiler vers le bas si:\r\n        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\r\n        // - ou si l'utilisateur est déjà proche du bas\r\n        if (force || isScrolledToBottom) {\r\n          // Utiliser une animation fluide pour le défilement (comme dans Messenger)\r\n          container.scrollTo({\r\n            top: container.scrollHeight,\r\n            behavior: 'smooth',\r\n          });\r\n        }\r\n      });\r\n    } catch (err) {\r\n      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  // Méthode pour ouvrir l'image en plein écran (style Messenger)\r\n  /**\r\n   * Active/désactive l'enregistrement vocal\r\n   */\r\n  toggleVoiceRecording(): void {\r\n    this.isRecordingVoice = !this.isRecordingVoice;\r\n\r\n    if (!this.isRecordingVoice) {\r\n      // Si on désactive l'enregistrement, réinitialiser la durée\r\n      this.voiceRecordingDuration = 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère la fin de l'enregistrement vocal\r\n   * @param audioBlob Blob audio enregistré\r\n   */\r\n  onVoiceRecordingComplete(audioBlob: Blob): void {\r\n    this.logger.debug(\r\n      'MessageChat',\r\n      'Voice recording complete, size:',\r\n      audioBlob.size\r\n    );\r\n\r\n    if (!this.conversation?.id && !this.otherParticipant?.id) {\r\n      this.toastService.showError('No conversation or recipient selected');\r\n      this.isRecordingVoice = false;\r\n      return;\r\n    }\r\n\r\n    // Récupérer l'ID du destinataire\r\n    const receiverId = this.otherParticipant?.id || '';\r\n\r\n    // Envoyer le message vocal\r\n    this.MessageService.sendVoiceMessage(\r\n      receiverId,\r\n      audioBlob,\r\n      this.conversation?.id,\r\n      this.voiceRecordingDuration\r\n    ).subscribe({\r\n      next: (message) => {\r\n        this.logger.debug('MessageChat', 'Voice message sent:', message);\r\n        this.isRecordingVoice = false;\r\n        this.voiceRecordingDuration = 0;\r\n        this.scrollToBottom(true);\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('MessageChat', 'Error sending voice message:', error);\r\n        this.toastService.showError('Failed to send voice message');\r\n        this.isRecordingVoice = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gère l'annulation de l'enregistrement vocal\r\n   */\r\n  onVoiceRecordingCancelled(): void {\r\n    this.logger.debug('MessageChat', 'Voice recording cancelled');\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingDuration = 0;\r\n  }\r\n\r\n  /**\r\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\r\n   * @param imageUrl URL de l'image à afficher\r\n   */\r\n  openImageFullscreen(imageUrl: string): void {\r\n    // Ouvrir l'image dans un nouvel onglet\r\n    window.open(imageUrl, '_blank');\r\n    this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);\r\n  }\r\n\r\n  /**\r\n   * Détecte les changements après chaque vérification de la vue\r\n   * Cela permet de s'assurer que les messages vocaux sont correctement affichés\r\n   * et que le défilement est maintenu\r\n   */\r\n  ngAfterViewChecked(): void {\r\n    // Faire défiler vers le bas si nécessaire\r\n    this.scrollToBottom();\r\n\r\n    // Forcer la détection des changements pour les messages vocaux\r\n    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\r\n    if (this.messages.some((msg) => msg.type === MessageType.VOICE_MESSAGE)) {\r\n      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\r\n      setTimeout(() => {\r\n        this.cdr.detectChanges();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Arrête l'indicateur de frappe\r\n   */\r\n  private stopTypingIndicator(): void {\r\n    if (this.isCurrentlyTyping && this.conversation?.id) {\r\n      this.isCurrentlyTyping = false;\r\n      clearTimeout(this.typingTimer);\r\n\r\n      this.logger.debug('MessageChat', 'Stopping typing indicator');\r\n\r\n      // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript\r\n      const conversationId = this.conversation?.id;\r\n      if (conversationId) {\r\n        this.MessageService.stopTyping(conversationId).subscribe({\r\n          next: () => {\r\n            this.logger.debug(\r\n              'MessageChat',\r\n              'Typing indicator stopped successfully'\r\n            );\r\n          },\r\n          error: (error) => {\r\n            this.logger.error(\r\n              'MessageChat',\r\n              'Error stopping typing indicator:',\r\n              error\r\n            );\r\n          },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation\r\n    this.stopTypingIndicator();\r\n\r\n    this.subscriptions.unsubscribe();\r\n    clearTimeout(this.typingTimeout);\r\n  }\r\n\r\n  /**\r\n   * Navigue vers la liste des conversations\r\n   */\r\n  goBackToConversations(): void {\r\n    this.router.navigate(['/messages/conversations']);\r\n  }\r\n\r\n  /**\r\n   * Bascule l'affichage du sélecteur d'émojis\r\n   */\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n    if (this.showEmojiPicker) {\r\n      this.showThemeSelector = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Insère un emoji dans le champ de message\r\n   * @param emoji Emoji à insérer\r\n   */\r\n  insertEmoji(emoji: string): void {\r\n    const control = this.messageForm.get('content');\r\n    if (control) {\r\n      const currentValue = control.value || '';\r\n      control.setValue(currentValue + emoji);\r\n      control.markAsDirty();\r\n      // Garder le focus sur le champ de saisie\r\n      setTimeout(() => {\r\n        const inputElement = document.querySelector(\r\n          '.whatsapp-input-field'\r\n        ) as HTMLInputElement;\r\n        if (inputElement) {\r\n          inputElement.focus();\r\n        }\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * S'abonne aux notifications en temps réel\r\n   */\r\n  private subscribeToNotifications(): void {\r\n    // S'abonner aux nouvelles notifications\r\n    const notificationSub =\r\n      this.MessageService.subscribeToNewNotifications().subscribe({\r\n        next: (notification) => {\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            `Nouvelle notification reçue: ${notification.type}`\r\n          );\r\n\r\n          // Si c'est une notification de message et que nous sommes dans la conversation concernée\r\n          if (\r\n            notification.type === 'NEW_MESSAGE' &&\r\n            notification.conversationId === this.conversation?.id\r\n          ) {\r\n            // Marquer automatiquement comme lue\r\n            if (notification.id) {\r\n              this.MessageService.markAsRead([notification.id]).subscribe();\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.logger.error(\r\n            'MessageChat',\r\n            'Erreur lors de la réception des notifications:',\r\n            error\r\n          );\r\n        },\r\n      });\r\n    this.subscriptions.add(notificationSub);\r\n\r\n    // S'abonner aux appels entrants\r\n    const callSub = this.MessageService.incomingCall$.subscribe({\r\n      next: (call) => {\r\n        if (call) {\r\n          this.logger.debug(\r\n            'MessageChat',\r\n            `Appel entrant de: ${call.caller.username}`\r\n          );\r\n          this.incomingCall = call;\r\n          this.showCallModal = true;\r\n\r\n          // Jouer la sonnerie\r\n          this.MessageService.play('ringtone');\r\n        } else {\r\n          this.showCallModal = false;\r\n          this.incomingCall = null;\r\n        }\r\n      },\r\n    });\r\n    this.subscriptions.add(callSub);\r\n  }\r\n\r\n  /**\r\n   * Initie un appel audio ou vidéo avec l'autre participant\r\n   * @param type Type d'appel (AUDIO ou VIDEO)\r\n   */\r\n  initiateCall(type: 'AUDIO' | 'VIDEO'): void {\r\n    if (!this.otherParticipant || !this.otherParticipant.id) {\r\n      console.error(\"Impossible d'initier un appel: participant invalide\");\r\n      return;\r\n    }\r\n\r\n    this.logger.info(\r\n      'MessageChat',\r\n      `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`\r\n    );\r\n\r\n    // Utiliser le service d'appel pour initier l'appel\r\n    this.MessageService.initiateCall(\r\n      this.otherParticipant.id,\r\n      type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\r\n      this.conversation?.id\r\n    ).subscribe({\r\n      next: (call) => {\r\n        this.logger.info('MessageChat', 'Appel initié avec succès:', call);\r\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageChat',\r\n          \"Erreur lors de l'initiation de l'appel:\",\r\n          error\r\n        );\r\n        this.toastService.showError(\r\n          \"Impossible d'initier l'appel. Veuillez réessayer.\"\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Accepte un appel entrant\r\n   */\r\n  acceptCall(): void {\r\n    if (!this.incomingCall) {\r\n      this.logger.error('MessageChat', 'Aucun appel entrant à accepter');\r\n      return;\r\n    }\r\n\r\n    this.logger.info(\r\n      'MessageChat',\r\n      `Acceptation de l'appel de ${this.incomingCall.caller.username}`\r\n    );\r\n\r\n    this.MessageService.acceptCall(this.incomingCall.id).subscribe({\r\n      next: (call) => {\r\n        this.logger.info('MessageChat', 'Appel accepté avec succès:', call);\r\n        this.showCallModal = false;\r\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageChat',\r\n          \"Erreur lors de l'acceptation de l'appel:\",\r\n          error\r\n        );\r\n        this.toastService.showError(\r\n          \"Impossible d'accepter l'appel. Veuillez réessayer.\"\r\n        );\r\n        this.showCallModal = false;\r\n        this.incomingCall = null;\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Rejette un appel entrant\r\n   */\r\n  rejectCall(): void {\r\n    if (!this.incomingCall) {\r\n      this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');\r\n      return;\r\n    }\r\n\r\n    this.logger.info(\r\n      'MessageChat',\r\n      `Rejet de l'appel de ${this.incomingCall.caller.username}`\r\n    );\r\n\r\n    this.MessageService.rejectCall(this.incomingCall.id).subscribe({\r\n      next: (call) => {\r\n        this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);\r\n        this.showCallModal = false;\r\n        this.incomingCall = null;\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageChat',\r\n          \"Erreur lors du rejet de l'appel:\",\r\n          error\r\n        );\r\n        this.showCallModal = false;\r\n        this.incomingCall = null;\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Termine un appel en cours\r\n   */\r\n  endCall(): void {\r\n    // Utiliser une variable pour stocker la dernière valeur de l'observable\r\n    let activeCall: any = null;\r\n\r\n    // S'abonner à l'observable pour obtenir la valeur actuelle\r\n    const sub = this.MessageService.activeCall$.subscribe((call) => {\r\n      activeCall = call;\r\n\r\n      if (!activeCall) {\r\n        this.logger.error('MessageChat', 'Aucun appel actif à terminer');\r\n        return;\r\n      }\r\n\r\n      this.logger.info('MessageChat', `Fin de l'appel`);\r\n\r\n      this.MessageService.endCall(activeCall.id).subscribe({\r\n        next: (call) => {\r\n          this.logger.info('MessageChat', 'Appel terminé avec succès:', call);\r\n        },\r\n        error: (error) => {\r\n          this.logger.error(\r\n            'MessageChat',\r\n            \"Erreur lors de la fin de l'appel:\",\r\n            error\r\n          );\r\n        },\r\n      });\r\n    });\r\n\r\n    // Se désabonner immédiatement après avoir obtenu la valeur\r\n    sub.unsubscribe();\r\n  }\r\n}\r\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\r\n<div\r\n  style=\"\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100vh;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    color: #1f2937;\r\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\r\n  \"\r\n>\r\n  <!-- ===== ANIMATIONS CSS ===== -->\r\n  <style>\r\n    @keyframes pulse {\r\n      0%,\r\n      100% {\r\n        opacity: 1;\r\n      }\r\n      50% {\r\n        opacity: 0.5;\r\n      }\r\n    }\r\n    @keyframes bounce {\r\n      0%,\r\n      20%,\r\n      53%,\r\n      80%,\r\n      100% {\r\n        transform: translateY(0);\r\n      }\r\n      40%,\r\n      43% {\r\n        transform: translateY(-8px);\r\n      }\r\n      70% {\r\n        transform: translateY(-4px);\r\n      }\r\n    }\r\n    @keyframes spin {\r\n      from {\r\n        transform: rotate(0deg);\r\n      }\r\n      to {\r\n        transform: rotate(360deg);\r\n      }\r\n    }\r\n    @keyframes ping {\r\n      75%,\r\n      100% {\r\n        transform: scale(2);\r\n        opacity: 0;\r\n      }\r\n    }\r\n  </style>\r\n\r\n  <!-- ===== HEADER SECTION ===== -->\r\n  <header\r\n    style=\"\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 16px;\r\n      background: #ffffff;\r\n      border-bottom: 1px solid #e5e7eb;\r\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n      z-index: 10;\r\n    \"\r\n  >\r\n    <!-- Bouton retour -->\r\n    <button\r\n      (click)=\"goBackToConversations()\"\r\n      style=\"\r\n        padding: 10px;\r\n        margin-right: 12px;\r\n        border-radius: 50%;\r\n        border: none;\r\n        background: transparent;\r\n        cursor: pointer;\r\n        transition: all 0.2s ease;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        min-width: 40px;\r\n        min-height: 40px;\r\n      \"\r\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\r\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\r\n      title=\"Retour aux conversations\"\r\n    >\r\n      <i\r\n        class=\"fas fa-arrow-left\"\r\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\r\n      ></i>\r\n    </button>\r\n\r\n    <!-- Info utilisateur -->\r\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\r\n      <!-- Avatar avec statut -->\r\n      <div style=\"position: relative; margin-right: 12px\">\r\n        <img\r\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\r\n          [alt]=\"otherParticipant?.username\"\r\n          style=\"\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 50%;\r\n            object-fit: cover;\r\n            border: 2px solid #10b981;\r\n            cursor: pointer;\r\n            transition: transform 0.2s ease;\r\n          \"\r\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\r\n          onmouseover=\"this.style.transform='scale(1.05)'\"\r\n          onmouseout=\"this.style.transform='scale(1)'\"\r\n          title=\"Voir le profil\"\r\n        />\r\n        <!-- Indicateur en ligne -->\r\n        <div\r\n          *ngIf=\"otherParticipant?.isOnline\"\r\n          style=\"\r\n            position: absolute;\r\n            bottom: 0;\r\n            right: 0;\r\n            width: 12px;\r\n            height: 12px;\r\n            background: #10b981;\r\n            border: 2px solid #ffffff;\r\n            border-radius: 50%;\r\n            animation: pulse 2s infinite;\r\n          \"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- Nom et statut -->\r\n      <div style=\"flex: 1; min-width: 0\">\r\n        <h3\r\n          style=\"\r\n            font-weight: 600;\r\n            color: #111827;\r\n            margin: 0;\r\n            font-size: 16px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          \"\r\n        >\r\n          {{ otherParticipant?.username || \"Utilisateur\" }}\r\n        </h3>\r\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\r\n          <!-- Indicateur de frappe -->\r\n          <div\r\n            *ngIf=\"isUserTyping\"\r\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\r\n          >\r\n            <span>En train d'écrire</span>\r\n            <div style=\"display: flex; gap: 2px\">\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite;\r\n                \"\r\n              ></div>\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite 0.1s;\r\n                \"\r\n              ></div>\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite 0.2s;\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n          <!-- Statut en ligne -->\r\n          <span *ngIf=\"!isUserTyping\">\r\n            {{\r\n              otherParticipant?.isOnline\r\n                ? \"En ligne\"\r\n                : formatLastActive(otherParticipant?.lastActive)\r\n            }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Actions -->\r\n    <div style=\"display: flex; align-items: center; gap: 8px\">\r\n      <!-- Appel vidéo -->\r\n      <button\r\n        (click)=\"startVideoCall()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        title=\"Appel vidéo\"\r\n        onmouseover=\"this.style.background='#f3f4f6'\"\r\n        onmouseout=\"this.style.background='transparent'\"\r\n      >\r\n        <i class=\"fas fa-video\"></i>\r\n      </button>\r\n\r\n      <!-- Appel vocal -->\r\n      <button\r\n        (click)=\"startVoiceCall()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        title=\"Appel vocal\"\r\n        onmouseover=\"this.style.background='#f3f4f6'\"\r\n        onmouseout=\"this.style.background='transparent'\"\r\n      >\r\n        <i class=\"fas fa-phone\"></i>\r\n      </button>\r\n\r\n      <!-- Recherche -->\r\n      <button\r\n        (click)=\"toggleSearch()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\r\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\r\n        title=\"Rechercher\"\r\n      >\r\n        <i class=\"fas fa-search\"></i>\r\n      </button>\r\n\r\n      <!-- Menu principal -->\r\n      <button\r\n        (click)=\"toggleMainMenu()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          position: relative;\r\n        \"\r\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\r\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\r\n        title=\"Menu\"\r\n      >\r\n        <i class=\"fas fa-ellipsis-v\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Menu dropdown -->\r\n    <div\r\n      *ngIf=\"showMainMenu\"\r\n      style=\"\r\n        position: absolute;\r\n        top: 64px;\r\n        right: 16px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        min-width: 192px;\r\n      \"\r\n    >\r\n      <div style=\"padding: 8px\">\r\n        <button\r\n          (click)=\"toggleSearch(); showMainMenu = false\"\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\r\n          <span style=\"color: #374151\">Rechercher</span>\r\n        </button>\r\n        <button\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\r\n          <span style=\"color: #374151\">Voir le profil</span>\r\n        </button>\r\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\r\n        <button\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\r\n          <span style=\"color: #374151\">Paramètres</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\r\n  <main\r\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\r\n    #messagesContainer\r\n    (scroll)=\"onScroll($event)\"\r\n    (dragover)=\"onDragOver($event)\"\r\n    (dragleave)=\"onDragLeave($event)\"\r\n    (drop)=\"onDrop($event)\"\r\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\r\n  >\r\n    <!-- Drag & Drop Overlay -->\r\n    <div\r\n      *ngIf=\"isDragOver\"\r\n      style=\"\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: rgba(34, 197, 94, 0.2);\r\n        border: 2px dashed #10b981;\r\n        border-radius: 8px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 50;\r\n        backdrop-filter: blur(2px);\r\n        animation: pulse 2s infinite;\r\n      \"\r\n    >\r\n      <div\r\n        style=\"\r\n          text-align: center;\r\n          background: #ffffff;\r\n          padding: 24px;\r\n          border-radius: 12px;\r\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #10b981;\r\n        \"\r\n      >\r\n        <i\r\n          class=\"fas fa-cloud-upload-alt\"\r\n          style=\"\r\n            font-size: 48px;\r\n            color: #10b981;\r\n            margin-bottom: 12px;\r\n            animation: bounce 1s infinite;\r\n          \"\r\n        ></i>\r\n        <p\r\n          style=\"\r\n            font-size: 20px;\r\n            font-weight: bold;\r\n            color: #047857;\r\n            margin-bottom: 8px;\r\n          \"\r\n        >\r\n          Déposez vos fichiers ici\r\n        </p>\r\n        <p style=\"font-size: 14px; color: #10b981\">\r\n          Images, vidéos, documents...\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div\r\n      *ngIf=\"isLoading\"\r\n      style=\"\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 32px 0;\r\n      \"\r\n    >\r\n      <div\r\n        style=\"\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 2px solid #e5e7eb;\r\n          border-bottom-color: #10b981;\r\n          border-radius: 50%;\r\n          animation: spin 1s linear infinite;\r\n          margin-bottom: 16px;\r\n        \"\r\n      ></div>\r\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\r\n    </div>\r\n\r\n    <!-- Empty State -->\r\n    <div\r\n      *ngIf=\"!isLoading && messages.length === 0\"\r\n      style=\"\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 64px 0;\r\n      \"\r\n    >\r\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\r\n        <i class=\"fas fa-comments\"></i>\r\n      </div>\r\n      <h3\r\n        style=\"\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n        \"\r\n      >\r\n        Aucun message\r\n      </h3>\r\n      <p style=\"color: #6b7280; text-align: center\">\r\n        Commencez votre conversation avec {{ otherParticipant?.username }}\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Messages List -->\r\n    <div\r\n      *ngIf=\"!isLoading && messages.length > 0\"\r\n      style=\"display: flex; flex-direction: column; gap: 8px\"\r\n    >\r\n      <ng-container\r\n        *ngFor=\"\r\n          let message of messages;\r\n          let i = index;\r\n          trackBy: trackByMessageId\r\n        \"\r\n      >\r\n        <!-- Date Separator -->\r\n        <div\r\n          *ngIf=\"shouldShowDateSeparator(i)\"\r\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\r\n        >\r\n          <div\r\n            style=\"\r\n              background: #ffffff;\r\n              padding: 4px 12px;\r\n              border-radius: 20px;\r\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n            \"\r\n          >\r\n            <span style=\"font-size: 12px; color: #6b7280\">\r\n              {{ formatDateSeparator(message.timestamp) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Message Container -->\r\n        <div\r\n          style=\"display: flex\"\r\n          [style.justify-content]=\"\r\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\r\n          \"\r\n          [id]=\"'message-' + message.id\"\r\n          (click)=\"onMessageClick(message, $event)\"\r\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\r\n        >\r\n          <!-- Avatar for others -->\r\n          <div\r\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\r\n            style=\"margin-right: 8px; flex-shrink: 0\"\r\n          >\r\n            <img\r\n              [src]=\"\r\n                message.sender?.image || 'assets/images/default-avatar.png'\r\n              \"\r\n              [alt]=\"message.sender?.username\"\r\n              style=\"\r\n                width: 32px;\r\n                height: 32px;\r\n                border-radius: 50%;\r\n                object-fit: cover;\r\n                cursor: pointer;\r\n                transition: transform 0.2s;\r\n              \"\r\n              (click)=\"openUserProfile(message.sender?.id!)\"\r\n              onmouseover=\"this.style.transform='scale(1.05)'\"\r\n              onmouseout=\"this.style.transform='scale(1)'\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Message Bubble -->\r\n          <div\r\n            [style.background-color]=\"\r\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\r\n            \"\r\n            [style.color]=\"\r\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\r\n            \"\r\n            style=\"\r\n              max-width: 320px;\r\n              padding: 12px 16px;\r\n              border-radius: 18px;\r\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n              position: relative;\r\n              word-wrap: break-word;\r\n              overflow-wrap: break-word;\r\n              border: none;\r\n            \"\r\n          >\r\n            <!-- Sender Name (for groups) -->\r\n            <div\r\n              *ngIf=\"\r\n                isGroupConversation() &&\r\n                message.sender?.id !== currentUserId &&\r\n                shouldShowSenderName(i)\r\n              \"\r\n              style=\"\r\n                font-size: 12px;\r\n                font-weight: 600;\r\n                margin-bottom: 4px;\r\n                opacity: 0.75;\r\n              \"\r\n              [style.color]=\"getUserColor(message.sender?.id!)\"\r\n            >\r\n              {{ message.sender?.username }}\r\n            </div>\r\n\r\n            <!-- Text Content -->\r\n            <div\r\n              *ngIf=\"getMessageType(message) === 'text'\"\r\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\r\n            >\r\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\r\n            </div>\r\n\r\n            <!-- Image Content -->\r\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\r\n              <img\r\n                [src]=\"getImageUrl(message)\"\r\n                [alt]=\"message.content || 'Image'\"\r\n                (click)=\"openImageViewer(message)\"\r\n                (load)=\"onImageLoad($event, message)\"\r\n                (error)=\"onImageError($event, message)\"\r\n                style=\"\r\n                  max-width: 280px;\r\n                  height: auto;\r\n                  border-radius: 12px;\r\n                  cursor: pointer;\r\n                  transition: transform 0.2s;\r\n                \"\r\n                onmouseover=\"this.style.transform='scale(1.02)'\"\r\n                onmouseout=\"this.style.transform='scale(1)'\"\r\n              />\r\n              <!-- Image Caption -->\r\n              <div\r\n                *ngIf=\"message.content\"\r\n                [style.color]=\"\r\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\r\n                \"\r\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\r\n                [innerHTML]=\"formatMessageContent(message.content)\"\r\n              ></div>\r\n            </div>\r\n\r\n            <!-- Message Metadata -->\r\n            <div\r\n              style=\"\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: flex-end;\r\n                gap: 4px;\r\n                margin-top: 4px;\r\n                font-size: 12px;\r\n                opacity: 0.75;\r\n              \"\r\n            >\r\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\r\n              <div\r\n                *ngIf=\"message.sender?.id === currentUserId\"\r\n                style=\"display: flex; align-items: center\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-clock\"\r\n                  *ngIf=\"message.status === 'SENDING'\"\r\n                  title=\"Envoi en cours\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check\"\r\n                  *ngIf=\"message.status === 'SENT'\"\r\n                  title=\"Envoyé\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check-double\"\r\n                  *ngIf=\"message.status === 'DELIVERED'\"\r\n                  title=\"Livré\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check-double\"\r\n                  style=\"color: #3b82f6\"\r\n                  *ngIf=\"message.status === 'READ'\"\r\n                  title=\"Lu\"\r\n                ></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <!-- Typing Indicator -->\r\n      <div\r\n        *ngIf=\"otherUserIsTyping\"\r\n        style=\"display: flex; align-items: start; gap: 8px\"\r\n      >\r\n        <img\r\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\r\n          [alt]=\"otherParticipant?.username\"\r\n          style=\"\r\n            width: 32px;\r\n            height: 32px;\r\n            border-radius: 50%;\r\n            object-fit: cover;\r\n          \"\r\n        />\r\n        <div\r\n          style=\"\r\n            background: #ffffff;\r\n            padding: 12px 16px;\r\n            border-radius: 18px;\r\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n          \"\r\n        >\r\n          <div style=\"display: flex; gap: 4px\">\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite;\r\n              \"\r\n            ></div>\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite 0.1s;\r\n              \"\r\n            ></div>\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite 0.2s;\r\n              \"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </main>\r\n\r\n  <!-- ===== FOOTER INPUT SECTION ===== -->\r\n  <footer\r\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\r\n  >\r\n    <form\r\n      [formGroup]=\"messageForm\"\r\n      (ngSubmit)=\"sendMessage()\"\r\n      style=\"display: flex; align-items: end; gap: 12px\"\r\n    >\r\n      <!-- Left Actions -->\r\n      <div style=\"display: flex; gap: 8px\">\r\n        <!-- Emoji Button -->\r\n        <button\r\n          type=\"button\"\r\n          (click)=\"toggleEmojiPicker()\"\r\n          style=\"\r\n            padding: 8px;\r\n            border-radius: 50%;\r\n            border: none;\r\n            background: transparent;\r\n            color: #6b7280;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n          \"\r\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\r\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\r\n          title=\"Émojis\"\r\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\r\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\r\n        >\r\n          <i class=\"fas fa-smile\"></i>\r\n        </button>\r\n\r\n        <!-- Attachment Button -->\r\n        <button\r\n          type=\"button\"\r\n          (click)=\"toggleAttachmentMenu()\"\r\n          style=\"\r\n            padding: 8px;\r\n            border-radius: 50%;\r\n            border: none;\r\n            background: transparent;\r\n            color: #6b7280;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n          \"\r\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\r\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\r\n          title=\"Pièces jointes\"\r\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\r\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\r\n        >\r\n          <i class=\"fas fa-paperclip\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Message Input -->\r\n      <div style=\"flex: 1; position: relative\">\r\n        <textarea\r\n          formControlName=\"content\"\r\n          placeholder=\"Tapez votre message...\"\r\n          (keydown)=\"onInputKeyDown($event)\"\r\n          (input)=\"onInputChange($event)\"\r\n          (focus)=\"onInputFocus()\"\r\n          style=\"\r\n            width: 100%;\r\n            min-height: 44px;\r\n            max-height: 120px;\r\n            padding: 12px 16px;\r\n            border: 1px solid #e5e7eb;\r\n            border-radius: 22px;\r\n            resize: none;\r\n            outline: none;\r\n            font-family: inherit;\r\n            font-size: 14px;\r\n            line-height: 1.4;\r\n            background: #ffffff;\r\n            color: #111827;\r\n            transition: all 0.2s;\r\n          \"\r\n          [disabled]=\"isInputDisabled()\"\r\n        ></textarea>\r\n      </div>\r\n\r\n      <!-- Send Button -->\r\n      <button\r\n        type=\"submit\"\r\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\r\n        style=\"\r\n          padding: 12px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: #3b82f6;\r\n          color: #ffffff;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-width: 44px;\r\n          min-height: 44px;\r\n        \"\r\n        [style.background]=\"\r\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\r\n        \"\r\n        [style.cursor]=\"\r\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\r\n        \"\r\n        title=\"Envoyer\"\r\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\r\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\r\n      >\r\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\r\n        <div\r\n          *ngIf=\"isSendingMessage\"\r\n          style=\"\r\n            width: 16px;\r\n            height: 16px;\r\n            border: 2px solid #ffffff;\r\n            border-top-color: transparent;\r\n            border-radius: 50%;\r\n            animation: spin 1s linear infinite;\r\n          \"\r\n        ></div>\r\n      </button>\r\n    </form>\r\n\r\n    <!-- Emoji Picker -->\r\n    <div\r\n      *ngIf=\"showEmojiPicker\"\r\n      style=\"\r\n        position: absolute;\r\n        bottom: 80px;\r\n        left: 16px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        width: 320px;\r\n        max-height: 300px;\r\n        overflow-y: auto;\r\n      \"\r\n    >\r\n      <div style=\"padding: 16px\">\r\n        <h4\r\n          style=\"\r\n            margin: 0 0 12px 0;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            color: #374151;\r\n          \"\r\n        >\r\n          Émojis\r\n        </h4>\r\n        <div\r\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\r\n        >\r\n          <button\r\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\r\n            (click)=\"insertEmoji(emoji)\"\r\n            style=\"\r\n              padding: 8px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 8px;\r\n              cursor: pointer;\r\n              font-size: 20px;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n            [title]=\"emoji.name\"\r\n          >\r\n            {{ emoji.emoji }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Attachment Menu -->\r\n    <div\r\n      *ngIf=\"showAttachmentMenu\"\r\n      style=\"\r\n        position: absolute;\r\n        bottom: 80px;\r\n        left: 60px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        min-width: 200px;\r\n      \"\r\n    >\r\n      <div style=\"padding: 16px\">\r\n        <h4\r\n          style=\"\r\n            margin: 0 0 12px 0;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            color: #374151;\r\n          \"\r\n        >\r\n          Pièces jointes\r\n        </h4>\r\n        <div\r\n          style=\"\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 12px;\r\n          \"\r\n        >\r\n          <!-- Images -->\r\n          <button\r\n            (click)=\"triggerFileInput('image')\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #dbeafe;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-image\"\r\n                style=\"color: #3b82f6; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Images</span\r\n            >\r\n          </button>\r\n\r\n          <!-- Documents -->\r\n          <button\r\n            (click)=\"triggerFileInput('document')\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #fef3c7;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-file-alt\"\r\n                style=\"color: #f59e0b; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Documents</span\r\n            >\r\n          </button>\r\n\r\n          <!-- Camera -->\r\n          <button\r\n            (click)=\"openCamera()\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #dcfce7;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-camera\"\r\n                style=\"color: #10b981; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Caméra</span\r\n            >\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Hidden File Input -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      style=\"display: none\"\r\n      (change)=\"onFileSelected($event)\"\r\n      [accept]=\"getFileAcceptTypes()\"\r\n      multiple\r\n    />\r\n  </footer>\r\n\r\n  <!-- Overlay to close menus -->\r\n  <div\r\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\r\n    style=\"\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: rgba(0, 0, 0, 0.25);\r\n      z-index: 40;\r\n    \"\r\n    (click)=\"closeAllMenus()\"\r\n  ></div>\r\n</div>\r\n\r\n<!-- Call Interface Component -->\r\n<app-call-interface\r\n  [isVisible]=\"isInCall\"\r\n  [activeCall]=\"activeCall\"\r\n  [callType]=\"callType\"\r\n  [otherParticipant]=\"otherParticipant\"\r\n  (callEnded)=\"endCall()\"\r\n  (callAccepted)=\"onCallAccepted($event)\"\r\n  (callRejected)=\"onCallRejected()\"\r\n></app-call-interface>\r\n"], "mappings": "AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAuC,MAAM;AAGlE,SAIEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;IC8FhEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAoBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAGRJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IAsFNX,EAAA,CAAAE,cAAA,cAaC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,EAAA,CAAAC,SAAA,YAAoD;IACpDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhDJ,EAAA,CAAAE,cAAA,iBAgBC;IACCF,EAAA,CAAAC,SAAA,YAAkD;IAClDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,SAAA,cAAmD;IACnDD,EAAA,CAAAE,cAAA,kBAgBC;IACCF,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBpDJ,EAAA,CAAAE,cAAA,cAkBC;IAWGF,EAAA,CAAAC,SAAA,YAQK;IACLD,EAAA,CAAAE,cAAA,YAOC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAA2C;IACzCF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAKRJ,EAAA,CAAAE,cAAA,cASC;IACCF,EAAA,CAAAC,SAAA,cAUO;IACPD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIhEJ,EAAA,CAAAE,cAAA,cASC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,EAAA,CAAAE,cAAA,cAGC;IAUKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,EAAA,CAAAE,cAAA,cAGC;IAcGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IAbhDjC,EAAA,CAAAI,YAAA,EAgBE;;;;IAfAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,EAAA,CAAAE,cAAA,cAaC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,cAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,cAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,kBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;IA0BtB1C,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAKK;;;;;IAxBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAI,8DAAA,iBAIK;IACL7D,EAAA,CAAAyD,UAAA,IAAAK,8DAAA,iBAIK;IACL9D,EAAA,CAAAyD,UAAA,IAAAM,8DAAA,iBAIK;IACL/D,EAAA,CAAAyD,UAAA,IAAAO,8DAAA,iBAKK;IACPhE,EAAA,CAAAI,YAAA,EAAM;;;;IAnBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,eAAkC;IAKlCjE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,YAA+B;IAK/BjE,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,iBAAoC;IAMpCjE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,YAA+B;;;;;;IAzK5CjE,EAAA,CAAAkE,uBAAA,GAMC;IAEClE,EAAA,CAAAyD,UAAA,IAAAU,yDAAA,kBAgBM;IAGNnE,EAAA,CAAAE,cAAA,cAQC;IAFCF,EAAA,CAAAY,UAAA,mBAAAwD,yEAAAjB,MAAA;MAAA,MAAAkB,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAwD,IAAA;MAAA,MAAA7C,WAAA,GAAA4C,WAAA,CAAAxC,SAAA;MAAA,MAAA0C,OAAA,GAAAvE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAA/C,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAAsB,+EAAAtB,MAAA;MAAA,MAAAkB,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAwD,IAAA;MAAA,MAAA7C,WAAA,GAAA4C,WAAA,CAAAxC,SAAA;MAAA,MAAA6C,OAAA,GAAA1E,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAuD,OAAA,CAAAC,oBAAA,CAAAlD,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAmB,yDAAA,kBAqBM;IAGN5E,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAoB,yDAAA,kBAeM;IAGN7E,EAAA,CAAAyD,UAAA,IAAAqB,yDAAA,kBAKM;IAGN9E,EAAA,CAAAyD,UAAA,IAAAsB,yDAAA,kBA0BM;IAGN/E,EAAA,CAAAE,cAAA,cAUC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAAuB,0DAAA,kBAyBM;IACRhF,EAAA,CAAAI,YAAA,EAAM;IAGZJ,EAAA,CAAAiF,qBAAA,EAAe;;;;;;IAvKVjF,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCpF,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,qBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,6BAEC;IACD7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,IAAAqC,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlEpF,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAIF;IAJEL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAI,mBAAA,OAAA7D,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,IAAAqC,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAIF;IAcEpF,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAM,cAAA,CAAA/D,WAAA,aAAwC;IAOrCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAO,QAAA,CAAAhE,WAAA,EAAuB;IAwCrBzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA0F,iBAAA,CAAAR,OAAA,CAAAS,iBAAA,CAAAlE,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,CAA0C;;;;;IA+BrD7C,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eASE;IACFD,EAAA,CAAAE,cAAA,eAOC;IAEGF,EAAA,CAAAC,SAAA,eAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;IA7CNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAA0D,OAAA,CAAApF,gBAAA,kBAAAoF,OAAA,CAAApF,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAwD,OAAA,CAAApF,gBAAA,kBAAAoF,OAAA,CAAApF,gBAAA,CAAAc,QAAA;;;;;IA5L3EtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAoC,mDAAA,6BAgLe;IAGf7F,EAAA,CAAAyD,UAAA,IAAAqC,0CAAA,kBAoDM;IACR9F,EAAA,CAAAI,YAAA,EAAM;;;;IAtOuBJ,EAAA,CAAAK,SAAA,GACb;IADaL,EAAA,CAAAkC,UAAA,YAAA6D,MAAA,CAAAC,QAAA,CACb,iBAAAD,MAAA,CAAAE,gBAAA;IAiLXjG,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA6D,MAAA,CAAAG,iBAAA,CAAuB;;;;;IAuKxBlG,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,eAUO;;;;;;IAmCLD,EAAA,CAAAE,cAAA,kBAeC;IAbCF,EAAA,CAAAY,UAAA,mBAAAuF,sEAAA;MAAA,MAAA9B,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAsF,IAAA;MAAA,MAAAC,SAAA,GAAAhC,WAAA,CAAAxC,SAAA;MAAA,MAAAyE,OAAA,GAAAtG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAmF,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5BrG,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAmE,SAAA,CAAAG,IAAA,CAAoB;IAEpBxG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+F,SAAA,CAAAI,KAAA,MACF;;;;;IA/CNzG,EAAA,CAAAE,cAAA,eAeC;IAUKF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAyD,UAAA,IAAAiD,6CAAA,sBAiBS;IACX1G,EAAA,CAAAI,YAAA,EAAM;;;;IAjBgBJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAAyE,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxE7G,EAAA,CAAAE,cAAA,eAaC;IAUKF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAMC;IAGGF,EAAA,CAAAY,UAAA,mBAAAkG,6DAAA;MAAA9G,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAC,OAAA,GAAAhH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6F,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCjH,EAAA,CAAAE,cAAA,eAUC;IACCF,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAAsG,8DAAA;MAAAlH,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAI,OAAA,GAAAnH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAgG,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCjH,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAAwG,8DAAA;MAAApH,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAM,OAAA,GAAArH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkG,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtBtH,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAkBXJ,EAAA,CAAAE,cAAA,eAYC;IADCF,EAAA,CAAAY,UAAA,mBAAA2G,0DAAA;MAAAvH,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsG,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B1H,EAAA,CAAAI,YAAA,EAAM;;;;ADphCT,OAAM,MAAOuH,oBAAoB;EA6H/BC,YACUC,cAA8B,EAC/BC,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAC1BC,MAAqB,EACrBC,GAAsB;IARtB,KAAAR,cAAc,GAAdA,cAAc;IACf,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA/Hb,KAAArC,QAAQ,GAAc,EAAE;IAExB,KAAAsC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAA1F,aAAa,GAAkB,IAAI;IACnC,KAAA2F,eAAe,GAAW,KAAK;IAC/B,KAAAhI,gBAAgB,GAAgB,IAAI;IACpC,KAAAiI,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IAC3B,KAAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC;IAC3B,KAAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;IACnC,KAAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IACzB,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IAChB,KAAAC,aAAa,GAAiB,IAAI3J,YAAY,EAAE;IAExD;IACA,KAAA4J,aAAa,GAAW,eAAe,CAAC,CAAC;IACzC,KAAAC,iBAAiB,GAAY,KAAK,CAAC,CAAC;IAEpC;IACA,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,aAAa,GAAY,KAAK;IAE9B,KAAAC,YAAY,GAAa,CACvB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;IAwaO,KAAAC,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG,CAAC,CAAC;IACpB,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IA7ZtC,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC/B,EAAE,CAACgC,KAAK,CAAC;MAC/BtH,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAACwK,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACrH,aAAa,GAAG,IAAI,CAACkF,WAAW,CAACoC,gBAAgB,EAAE;IAExD;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACd,aAAa,GAAGc,UAAU;MAC/B,IAAI,CAAChC,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,uBAAuBH,UAAU,EAAE,CAAC;;IAGvE;IACA,IAAI,CAACI,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,wBAAwB,EAAE;IAE/B,MAAMC,QAAQ,GAAG,IAAI,CAAC5C,KAAK,CAAC6C,MAAM,CAC/BC,IAAI,CACH7K,MAAM,CAAE4K,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChC7K,oBAAoB,EAAE,EACtBD,SAAS,CAAE8K,MAAM,IAAI;MACnB,IAAI,CAACpC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACvC,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACkD,WAAW,GAAG,CAAC,CAAC,CAAC;MACtB,IAAI,CAACE,eAAe,GAAG,IAAI,CAAC,CAAC;MAE7B,IAAI,CAAChB,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,8CAA8C,IAAI,CAACrB,WAAW,WAAW,IAAI,CAACF,oBAAoB,EAAE,CACrG;MAED;MACA,OAAO,IAAI,CAACnB,cAAc,CAACgD,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAAC3B,oBAAoB,EACzB,IAAI,CAACE,WAAW,CAAC;OAClB;IACH,CAAC,CAAC,CACH,CACA4B,SAAS,CAAC;MACTC,IAAI,EAAGzC,YAAY,IAAI;QACrB,IAAI,CAAC0C,wBAAwB,CAAC1C,YAAY,CAAC;MAC7C,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,WAAW,CAAC,6BAA6B,EAAED,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAC5B,aAAa,CAAC8B,GAAG,CAACT,QAAQ,CAAC;EAClC;EAEA;;;EAGQF,iBAAiBA,CAAA;IACvB,IAAI,CAACpC,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,wCAAwC,CAAC;IAE1E,MAAMa,GAAG,GAAG,IAAI,CAACvD,cAAc,CAACwD,gBAAgB,EAAE,CAACP,SAAS,CAAC;MAC3DC,IAAI,EAAGO,aAAa,IAAI;QACtB,IAAI,CAAClD,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,aAAaD,aAAa,CAACE,MAAM,iBAAiB,CACnD;QAED;QACA;QACA,IAAIF,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACpD,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,oCAAoC,CACrC;UAED;UACAkB,UAAU,CAAC,MAAK;YACd,IAAI,CAACpD,GAAG,CAACqD,aAAa,EAAE;YACxB,IAAI,CAACtD,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,6CAA6C,CAC9C;UACH,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,+BAA+B,EAC/BA,KAAK,CACN;QACD;MACF;KACD,CAAC;;IAEF,IAAI,CAAC5B,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEA;;;;;EAKQF,WAAWA,CAACS,OAAe,EAAEV,KAAU;IAC7C,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAEU,OAAO,EAAEV,KAAK,CAAC;IAChD,IAAI,CAAC1C,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC0C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CAACD,OAAO,CAAC;EACtC;EAEA;EACAE,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EACAC,WAAWA,CAACH,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMI,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IACD,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;MAClD,IAAIJ,QAAQ,CAACE,QAAQ,CAACG,GAAG,CAAC,EAAE,OAAOC,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEQpB,wBAAwBA,CAAC1C,YAA0B;IACzD,IAAI,CAACF,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,iCAAiCjD,YAAY,CAACrG,EAAE,EAAE,CACnD;IACD,IAAI,CAACmG,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,oBAAoBjC,YAAY,EAAEtC,QAAQ,EAAEwF,MAAM,IAAI,CAAC,iBACrDlD,YAAY,EAAEiE,YAAY,EAAEf,MAAM,IAAI,CACxC,eAAe,CAChB;IAED;IACA,IAAIlD,YAAY,EAAEtC,QAAQ,IAAIsC,YAAY,CAACtC,QAAQ,CAACwF,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACpD,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,6BACEjC,YAAY,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC/D,EAC3B,aAAaqG,YAAY,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAACtD,OAAO,EAAE8J,SAAS,CACtD,CAAC,EACD,EAAE,CACH,YAAYlE,YAAY,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAChE,MAAM,EAAEV,QAAQ,EAAE,CACzD;;IAGH,IAAI,CAACgH,YAAY,GAAGA,YAAY;IAEhC;IACA,IAAI,CAACA,YAAY,EAAEtC,QAAQ,IAAIsC,YAAY,CAACtC,QAAQ,CAACwF,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAACpD,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,mCAAmC,CAAC;MAErE;MACA,IAAI,CAAC/J,gBAAgB,GACnB8H,YAAY,EAAEiE,YAAY,EAAEE,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACzK,EAAE,KAAK,IAAI,CAACY,aAAa,IAAI6J,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC9J,aAAa,CACnE,IAAI,IAAI;MAEX;MACA,IAAI,CAACmD,QAAQ,GAAG,EAAE;MAElB,IAAI,CAACoC,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,kCAAkC,CAAC;KACrE,MAAM;MACL;MACA,MAAMqC,oBAAoB,GAAG,CAAC,IAAItE,YAAY,EAAEtC,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhE;MACA4G,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAACpL,SAAS,YAAYuL,IAAI,GACvBH,CAAC,CAACpL,SAAS,CAACwL,OAAO,EAAE,GACrB,IAAID,IAAI,CAACH,CAAC,CAACpL,SAAmB,CAAC,CAACwL,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTJ,CAAC,CAACrL,SAAS,YAAYuL,IAAI,GACvBF,CAAC,CAACrL,SAAS,CAACwL,OAAO,EAAE,GACrB,IAAID,IAAI,CAACF,CAAC,CAACrL,SAAmB,CAAC,CAACwL,OAAO,EAAE;QAC/C,OAAOF,KAAK,GAAGG,KAAK;MACtB,CAAC,CAAC;MAEF;MACA,IAAIP,oBAAoB,CAACpB,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM4B,YAAY,GAAGR,oBAAoB,CAAC,CAAC,CAAC;QAC5C,IAAI,CAACxE,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,gCAAgC6C,YAAY,CAACpL,MAAM,EAAEC,EAAE,gBAAgBmL,YAAY,CAACpL,MAAM,EAAE2K,GAAG,cAAcS,YAAY,CAACC,QAAQ,iBAAiBD,YAAY,CAACE,QAAQ,EAAErL,EAAE,kBAAkBmL,YAAY,CAACE,QAAQ,EAAEX,GAAG,gBAAgBS,YAAY,CAACG,UAAU,EAAE,CAClQ;;MAGH;MACA,IAAI,CAACvH,QAAQ,GAAG4G,oBAAoB;MAEpC,IAAI,CAACxE,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,aAAa,IAAI,CAACvE,QAAQ,CAACwF,MAAM,6BAA6B,CAC/D;MAED,IAAI,CAACpD,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,SAASqC,oBAAoB,CAACpB,MAAM,6CAA6C,IAAI,CAACxF,QAAQ,CAACwF,MAAM,EAAE,CACxG;;IAGH,IAAI,CAAChL,gBAAgB,GACnB8H,YAAY,EAAEiE,YAAY,EAAEE,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACzK,EAAE,KAAK,IAAI,CAACY,aAAa,IAAI6J,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC9J,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAACuF,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,iCACE,IAAI,CAAC/J,gBAAgB,EAAEc,QAAQ,IAAI,SACrC,EAAE,CACH;IAED,IAAI,CAACiH,OAAO,GAAG,KAAK;IACpBkD,UAAU,CAAC,MAAM,IAAI,CAAC+B,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAACpF,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,iCAAiC,CAAC;IACnE,IAAI,CAACkD,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAACnF,YAAY,EAAErG,EAAE,EAAE;MACzB,IAAI,CAACmG,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,8CAA8C,IAAI,CAACjC,YAAY,CAACrG,EAAE,EAAE,CACrE;MACD,IAAI,CAACyL,8BAA8B,CAAC,IAAI,CAACpF,YAAY,CAACrG,EAAE,CAAC;MACzD,IAAI,CAAC0L,sBAAsB,CAAC,IAAI,CAACrF,YAAY,CAACrG,EAAE,CAAC;MACjD,IAAI,CAAC2L,2BAA2B,CAAC,IAAI,CAACtF,YAAY,CAACrG,EAAE,CAAC;;IAGxD,IAAI,CAACmG,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,kCAAkC,CAAC;EACrE;EAEQmC,8BAA8BA,CAACG,cAAsB;IAC3D,MAAMzC,GAAG,GAAG,IAAI,CAACvD,cAAc,CAAC6F,8BAA8B,CAC5DG,cAAc,CACf,CAAC/C,SAAS,CAAC;MACVC,IAAI,EAAG+C,mBAAmB,IAAI;QAC5B,IAAI,CAACxF,YAAY,GAAGwF,mBAAmB;QACvC,IAAI,CAAC9H,QAAQ,GAAG8H,mBAAmB,CAAC9H,QAAQ,GACxC,CAAC,GAAG8H,mBAAmB,CAAC9H,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACwH,cAAc,EAAE;MACvB,CAAC;MACDvC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQuC,sBAAsBA,CAACE,cAAsB;IACnD,MAAMzC,GAAG,GAAG,IAAI,CAACvD,cAAc,CAAC8F,sBAAsB,CACpDE,cAAc,CACf,CAAC/C,SAAS,CAAC;MACVC,IAAI,EAAGgD,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAEF,cAAc,KAAK,IAAI,CAACvF,YAAY,EAAErG,EAAE,EAAE;UACxD;UACA,IAAI,CAAC+D,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE+H,UAAU,CAAC,CAAClB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAACpL,SAAS,YAAYuL,IAAI,GACvBH,CAAC,CAACpL,SAAS,CAACwL,OAAO,EAAE,GACrB,IAAID,IAAI,CAACH,CAAC,CAACpL,SAAmB,CAAC,CAACwL,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTJ,CAAC,CAACrL,SAAS,YAAYuL,IAAI,GACvBF,CAAC,CAACrL,SAAS,CAACwL,OAAO,EAAE,GACrB,IAAID,IAAI,CAACF,CAAC,CAACrL,SAAmB,CAAC,CAACwL,OAAO,EAAE;YAC/C,OAAOF,KAAK,GAAGG,KAAK,CAAC,CAAC;UACxB,CAAC,CAAC;;UAEF,IAAI,CAAC/E,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,kCAAkC,IAAI,CAACvE,QAAQ,CAACwF,MAAM,WAAW,CAClE;UAEDC,UAAU,CAAC,MAAM,IAAI,CAAC+B,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C;UACA,IACEO,UAAU,CAAC/L,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACY,aAAa,IAC5CkL,UAAU,CAAC/L,MAAM,EAAE2K,GAAG,KAAK,IAAI,CAAC9J,aAAa,EAC7C;YACA,IAAIkL,UAAU,CAAC9L,EAAE,EAAE;cACjB,IAAI,CAAC4F,cAAc,CAACmG,iBAAiB,CAACD,UAAU,CAAC9L,EAAE,CAAC,CAAC6I,SAAS,EAAE;;;;MAIxE,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQwC,2BAA2BA,CAACC,cAAsB;IACxD,MAAMzC,GAAG,GAAG,IAAI,CAACvD,cAAc,CAACoG,0BAA0B,CACxDJ,cAAc,CACf,CAAC/C,SAAS,CAAC;MACVC,IAAI,EAAGmD,KAAK,IAAI;QACd,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,CAACtL,aAAa,EAAE;UACvC,IAAI,CAAC+F,QAAQ,GAAGsF,KAAK,CAACtF,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjBwF,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAG5C,UAAU,CAAC,MAAK;cACnC,IAAI,CAAC7C,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQqC,kBAAkBA,CAAA;IACxB,MAAMa,cAAc,GAAG,IAAI,CAACtI,QAAQ,CAACjG,MAAM,CACxCwO,GAAG,IACF,CAACA,GAAG,CAACC,MAAM,KACVD,GAAG,CAACjB,QAAQ,EAAErL,EAAE,KAAK,IAAI,CAACY,aAAa,IACtC0L,GAAG,CAACjB,QAAQ,EAAEX,GAAG,KAAK,IAAI,CAAC9J,aAAa,CAAC,CAC9C;IAEDyL,cAAc,CAACG,OAAO,CAAEF,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAACtM,EAAE,EAAE;QACV,MAAMmJ,GAAG,GAAG,IAAI,CAACvD,cAAc,CAACmG,iBAAiB,CAACO,GAAG,CAACtM,EAAE,CAAC,CAAC6I,SAAS,CAAC;UAClEG,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,gCAAgC,EAChCA,KAAK,CACN;UACH;SACD,CAAC;QACF,IAAI,CAAC5B,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEAsD,cAAcA,CAACR,KAAU;IACvB,MAAMS,IAAI,GAAGT,KAAK,CAACU,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX;IACA,IAAIA,IAAI,CAACG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAAC3G,YAAY,CAACyD,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF;IACA,MAAMmD,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAAC/C,QAAQ,CAAC2C,IAAI,CAACK,IAAI,CAAC,EAAE;MACnC,IAAI,CAAC7G,YAAY,CAACyD,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAACnD,YAAY,GAAGkG,IAAI;IACxB,MAAMM,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAACzG,UAAU,GAAGuG,MAAM,CAACG,MAAM;IACjC,CAAC;IACDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;EAC5B;EAEAW,gBAAgBA,CAAA;IACd,IAAI,CAAC7G,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAAC6G,SAAS,EAAEC,aAAa,EAAE;MACjC,IAAI,CAACD,SAAS,CAACC,aAAa,CAACpD,KAAK,GAAG,EAAE;;EAE3C;EAOA;;;;EAIAqD,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACnH,YAAY,EAAErG,EAAE,IAAI,CAAC,IAAI,CAACY,aAAa,EAAE;MACjD;;IAGF;IACA,MAAMgL,cAAc,GAAG,IAAI,CAACvF,YAAY,CAACrG,EAAE;IAE3C;IACAmM,YAAY,CAAC,IAAI,CAACsB,WAAW,CAAC;IAE9B;IACA,IAAI,CAAC,IAAI,CAAC9F,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACxB,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;MAE7D,IAAI,CAAC1C,cAAc,CAAC8H,WAAW,CAAC9B,cAAc,CAAC,CAAC/C,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC3C,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;QACH,CAAC;QACDU,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;QACH;OACD,CAAC;;IAGJ;IACA,IAAI,CAACyE,WAAW,GAAGjE,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAAC7B,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACxB,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,6CAA6C,CAC9C;QAED,IAAI,CAAC1C,cAAc,CAAC+H,UAAU,CAAC/B,cAAc,CAAC,CAAC/C,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAAC3C,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;UACH,CAAC;UACDU,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;UACH;SACD,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACnB,cAAc,CAAC;EACzB;EAEA;;;EAGA+F,mBAAmBA,CAAA;IACjB,IAAI,CAACtG,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MAC1BkC,UAAU,CAAC,MAAK;QACd,MAAMqE,YAAY,GAAI5B,KAAiB,IAAI;UACzC,MAAMU,MAAM,GAAGV,KAAK,CAACU,MAAqB;UAC1C,IAAI,CAACA,MAAM,CAACmB,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACtC,IAAI,CAACxG,iBAAiB,GAAG,KAAK;YAC9ByG,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEH,YAAY,CAAC;;QAEvD,CAAC;QACDE,QAAQ,CAACE,gBAAgB,CAAC,OAAO,EAAEJ,YAAY,CAAC;MAClD,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;;EAIAK,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC9G,aAAa,GAAG8G,KAAK;IAC1B,IAAI,CAAC7G,iBAAiB,GAAG,KAAK;IAE9B;IACAc,YAAY,CAACgG,OAAO,CAAC,YAAY,EAAED,KAAK,CAAC;IAEzC,IAAI,CAAChI,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,qBAAqB6F,KAAK,EAAE,CAAC;EAChE;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAClI,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,4BAA4B,CAAC;IAE7D;IACA,MAAMgF,KAAK,GAAGlG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAClC,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,+BAA+B,CAAC,CAACgG,KAAK,YAAY,IAAI,CAAC1N,aAAa,EAAE,CACvE;IAED,IACG,IAAI,CAACkH,WAAW,CAACyG,OAAO,IAAI,CAAC,IAAI,CAAC/H,YAAY,IAC/C,CAAC,IAAI,CAAC5F,aAAa,IACnB,CAAC,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,EAC1B;MACA,IAAI,CAACmG,MAAM,CAACqI,IAAI,CACd,aAAa,EACb,uDAAuD,CACxD;MACD;;IAGF;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B,MAAMhO,OAAO,GAAG,IAAI,CAACqH,WAAW,CAAC4G,GAAG,CAAC,SAAS,CAAC,EAAEvE,KAAK;IAEtD;IACA,MAAMwE,WAAW,GAAY;MAC3B3O,EAAE,EAAE,OAAO,GAAG,IAAIgL,IAAI,EAAE,CAACC,OAAO,EAAE;MAClCxK,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBV,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACY,aAAa,IAAI,EAAE;QAC5BvB,QAAQ,EAAE,IAAI,CAACkH;OAChB;MACD8E,QAAQ,EAAE;QACRrL,EAAE,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,EAAE;QAC5BX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,CAACc,QAAQ,IAAI;OAC7C;MACDI,SAAS,EAAE,IAAIuL,IAAI,EAAE;MACrBuB,MAAM,EAAE,KAAK;MACbqC,SAAS,EAAE,IAAI,CAAE;KAClB;IAED;IACA,IAAI,IAAI,CAACpI,YAAY,EAAE;MACrB;MACA,IAAIqI,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAACrI,YAAY,CAACuG,IAAI,CAACjD,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/C+E,QAAQ,GAAG,OAAO;QAElB;QACA,IAAI,IAAI,CAACpI,UAAU,EAAE;UACnBkI,WAAW,CAACG,WAAW,GAAG,CACxB;YACE9O,EAAE,EAAE,iBAAiB;YACrB+O,GAAG,EAAE,IAAI,CAACtI,UAAU,GAAG,IAAI,CAACA,UAAU,CAACuI,QAAQ,EAAE,GAAG,EAAE;YACtDjC,IAAI,EAAErP,WAAW,CAACuR,KAAK;YACvB1K,IAAI,EAAE,IAAI,CAACiC,YAAY,CAACjC,IAAI;YAC5BsI,IAAI,EAAE,IAAI,CAACrG,YAAY,CAACqG;WACzB,CACF;;;MAIL;MACA,IAAIgC,QAAQ,KAAK,OAAO,EAAE;QACxBF,WAAW,CAAC5B,IAAI,GAAGrP,WAAW,CAACuR,KAAK;OACrC,MAAM,IAAIJ,QAAQ,KAAK,MAAM,EAAE;QAC9BF,WAAW,CAAC5B,IAAI,GAAGrP,WAAW,CAACwR,IAAI;;;IAIvC;IACA,IAAI,CAACnL,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE4K,WAAW,CAAC;IAE/C;IACA,MAAMQ,UAAU,GAAG,IAAI,CAAC3I,YAAY,CAAC,CAAC;IACtC,IAAI,CAACsB,WAAW,CAACsH,KAAK,EAAE;IACxB,IAAI,CAAC/B,gBAAgB,EAAE;IAEvB;IACA7D,UAAU,CAAC,MAAM,IAAI,CAAC+B,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C;IACA,IAAI,CAAC7E,WAAW,GAAG,IAAI;IAEvB,MAAM2I,OAAO,GAAG,IAAI,CAACzJ,cAAc,CAACyI,WAAW,CAC7C,IAAI,CAAC9P,gBAAgB,CAACyB,EAAE,EACxBS,OAAO,EACP0O,UAAU,IAAIG,SAAS,EACvB5R,WAAW,CAAC6R,IAAI,CACjB,CAAC1G,SAAS,CAAC;MACVC,IAAI,EAAGY,OAAO,IAAI;QAChB,IAAI,CAACvD,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,8BAA8BI,OAAO,EAAE1J,EAAE,IAAI,SAAS,EAAE,CACzD;QAED;QACA,IAAI,CAAC+D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyL,GAAG,CAAElD,GAAG,IACpCA,GAAG,CAACtM,EAAE,KAAK2O,WAAW,CAAC3O,EAAE,GAAG0J,OAAO,GAAG4C,GAAG,CAC1C;QAED,IAAI,CAAC5F,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;QAEjE;QACA,IAAI,CAACjF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyL,GAAG,CAAElD,GAAG,IAAI;UACxC,IAAIA,GAAG,CAACtM,EAAE,KAAK2O,WAAW,CAAC3O,EAAE,EAAE;YAC7B,OAAO;cACL,GAAGsM,GAAG;cACNsC,SAAS,EAAE,KAAK;cAChBa,OAAO,EAAE;aACV;;UAEH,OAAOnD,GAAG;QACZ,CAAC,CAAC;QAEF,IAAI,CAAC5F,WAAW,GAAG,KAAK;QACxB,IAAI,CAACR,YAAY,CAACyD,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAACmG,OAAO,CAAC;EACjC;EAEA3L,iBAAiBA,CAACjE,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAEvB,IAAI;MACF,MAAMiQ,IAAI,GAAGjQ,SAAS,YAAYuL,IAAI,GAAGvL,SAAS,GAAG,IAAIuL,IAAI,CAACvL,SAAS,CAAC;MACxE;MACA,OAAOiQ,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAO9G,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACzE,OAAO,cAAc;;EAEzB;EAEAvK,gBAAgBA,CAACC,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMqR,cAAc,GAClBrR,UAAU,YAAYsM,IAAI,GAAGtM,UAAU,GAAG,IAAIsM,IAAI,CAACtM,UAAU,CAAC;IAChE,MAAMsR,GAAG,GAAG,IAAIhF,IAAI,EAAE;IACtB,MAAMiF,SAAS,GACbC,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC/E,OAAO,EAAE,GAAG8E,cAAc,CAAC9E,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIgF,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUF,cAAc,CAACJ,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUE,cAAc,CAACK,kBAAkB,EAAE,EAAE;EACxD;EAEAC,iBAAiBA,CAAC5Q,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAGvB,IAAI;MACF,MAAMiQ,IAAI,GAAGjQ,SAAS,YAAYuL,IAAI,GAAGvL,SAAS,GAAG,IAAIuL,IAAI,CAACvL,SAAS,CAAC;MACxE,MAAM6Q,KAAK,GAAG,IAAItF,IAAI,EAAE;MAExB;MACA,MAAMuF,OAAO,GAA+B;QAC1CC,OAAO,EAAE,OAAO;QAChBZ,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT;MAED,IAAIH,IAAI,CAACe,YAAY,EAAE,KAAKH,KAAK,CAACG,YAAY,EAAE,EAAE;QAChD,OAAOf,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMa,SAAS,GAAG,IAAI1F,IAAI,CAACsF,KAAK,CAAC;MACjCI,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAIlB,IAAI,CAACe,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASf,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN;MACA,MAAMgB,GAAG,GAAGnB,IAAI,CACbU,kBAAkB,CAAC,OAAO,EAAE;QAAEI,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDM,WAAW,EAAE;MAChB,OAAO,GAAGD,GAAG,MAAMnB,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAO7G,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACzE,OAAO,cAAc;;EAEzB;EAEA+H,oBAAoBA,CAACC,KAAa;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI,CAAClN,QAAQ,CAACiN,KAAK,CAAC;MACvC,MAAME,OAAO,GAAG,IAAI,CAACnN,QAAQ,CAACiN,KAAK,GAAG,CAAC,CAAC;MAExC,IAAI,CAACC,UAAU,EAAExR,SAAS,IAAI,CAACyR,OAAO,EAAEzR,SAAS,EAAE;QACjD,OAAO,IAAI;;MAGb,MAAM0R,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAACxR,SAAS,CAAC;MACnE,MAAM4R,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAACzR,SAAS,CAAC;MAE7D,OAAO0R,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAOrI,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,6BAA6B,EAAEA,KAAK,CAAC;MACtE,OAAO,KAAK;;EAEhB;EAEQoI,oBAAoBA,CAAC3R,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAGvB,IAAI;MACF,OAAO,CACLA,SAAS,YAAYuL,IAAI,GAAGvL,SAAS,GAAG,IAAIuL,IAAI,CAACvL,SAAS,CAAC,EAC3DgR,YAAY,EAAE;KACjB,CAAC,OAAOzH,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;MACD,OAAO,cAAc;;EAEzB;EACAzF,cAAcA,CAACmG,OAAmC;IAChD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOhM,WAAW,CAAC6R,IAAI;;IAGzB,IAAI;MACF;MACA,IAAI7F,OAAO,CAACqD,IAAI,EAAE;QAChB;QACA,MAAMuE,OAAO,GAAG5H,OAAO,CAACqD,IAAI,CAACiC,QAAQ,EAAE;QACvC,IAAIsC,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAO5T,WAAW,CAAC6R,IAAI;SACxB,MAAM,IAAI+B,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5T,WAAW,CAACuR,KAAK;SACzB,MAAM,IAAIqC,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAO5T,WAAW,CAACwR,IAAI;SACxB,MAAM,IAAIoC,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5T,WAAW,CAAC6T,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5T,WAAW,CAAC8T,KAAK;SACzB,MAAM,IAAIF,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAO5T,WAAW,CAAC+T,MAAM;;;MAI7B;MACA,IAAI/H,OAAO,CAACoF,WAAW,EAAEvF,MAAM,EAAE;QAC/B,MAAMmI,UAAU,GAAGhI,OAAO,CAACoF,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI4C,UAAU,IAAIA,UAAU,CAAC3E,IAAI,EAAE;UACjC,MAAM4E,iBAAiB,GAAGD,UAAU,CAAC3E,IAAI,CAACiC,QAAQ,EAAE;UAEpD;UACA,IAAI2C,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAOjU,WAAW,CAACuR,KAAK;WACzB,MAAM,IACL0C,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAOjU,WAAW,CAACwR,IAAI;WACxB,MAAM,IACLyC,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOjU,WAAW,CAAC6T,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOjU,WAAW,CAAC8T,KAAK;;;QAI5B;QACA,OAAO9T,WAAW,CAACwR,IAAI;;MAGzB;MACA,OAAOxR,WAAW,CAAC6R,IAAI;KACxB,CAAC,OAAOvG,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,6BAA6B,EAAEA,KAAK,CAAC;MACtE,OAAOtL,WAAW,CAAC6R,IAAI;;EAE3B;EAEA;EACA/L,QAAQA,CAACkG,OAAmC;IAC1C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMmI,UAAU,GAAGhI,OAAO,CAACoF,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC4C,UAAU,IAAI,CAACA,UAAU,CAAC3E,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAG2E,UAAU,CAAC3E,IAAI,CAACiC,QAAQ,EAAE;IACvC,OAAOjC,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGA6E,cAAcA,CAAClI,OAAmC;IAChD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,KAAK;;IAGd;IACA,IACEA,OAAO,CAACqD,IAAI,KAAKrP,WAAW,CAACmU,aAAa,IAC1CnI,OAAO,CAACqD,IAAI,KAAKrP,WAAW,CAACoU,mBAAmB,EAChD;MACA,OAAO,IAAI;;IAGb;IACA,IAAIpI,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOG,OAAO,CAACoF,WAAW,CAACiD,IAAI,CAAEC,GAAG,IAAI;QACtC,MAAMjF,IAAI,GAAGiF,GAAG,CAACjF,IAAI,EAAEiC,QAAQ,EAAE;QACjC,OACEjC,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBrD,OAAO,CAACuI,QAAQ,EAAEL,cAAc,KAC9B7E,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACrD,OAAO,CAACuI,QAAQ,EAAEL,cAAc;EAC3C;EAEA;;;EAGAM,kBAAkBA,CAACxI,OAAmC;IACpD,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX;IACA,MAAM4I,eAAe,GAAGzI,OAAO,CAACoF,WAAW,CAACtE,IAAI,CAAEwH,GAAG,IAAI;MACvD,MAAMjF,IAAI,GAAGiF,GAAG,CAACjF,IAAI,EAAEiC,QAAQ,EAAE;MACjC,OACEjC,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAOoF,eAAe,EAAEpD,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAqD,uBAAuBA,CAAC1I,OAAmC;IACzD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,CAAC;;IAGV;IACA,IAAIA,OAAO,CAACuI,QAAQ,EAAEI,QAAQ,EAAE;MAC9B,OAAO3I,OAAO,CAACuI,QAAQ,CAACI,QAAQ;;IAGlC;IACA,IAAI3I,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM4I,eAAe,GAAGzI,OAAO,CAACoF,WAAW,CAACtE,IAAI,CAAEwH,GAAG,IAAI;QACvD,MAAMjF,IAAI,GAAGiF,GAAG,CAACjF,IAAI,EAAEiC,QAAQ,EAAE;QACjC,OACEjC,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAIoF,eAAe,IAAIA,eAAe,CAACE,QAAQ,EAAE;QAC/C,OAAOF,eAAe,CAACE,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;EACA1Q,WAAWA,CAAC+H,OAAmC;IAC7C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMmI,UAAU,GAAGhI,OAAO,CAACoF,WAAW,CAAC,CAAC,CAAC;IACzC,OAAO4C,UAAU,EAAE3C,GAAG,IAAI,EAAE;EAC9B;EAEAuD,mBAAmBA,CAAC5I,OAAmC;IACrD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAM6I,aAAa,GACjB7I,OAAO,CAAC3J,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACY,aAAa,IACzC8I,OAAO,CAAC3J,MAAM,EAAE2K,GAAG,KAAK,IAAI,CAAC9J,aAAa,IAC1C8I,OAAO,CAAC0B,QAAQ,KAAK,IAAI,CAACxK,aAAa;MAEzC;MACA;MACA;MACA,MAAM4R,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAME,WAAW,GAAG,IAAI,CAAClP,cAAc,CAACmG,OAAO,CAAC;MAEhD;MACA,IAAIA,OAAO,CAACoF,WAAW,IAAIpF,OAAO,CAACoF,WAAW,CAACvF,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMmI,UAAU,GAAGhI,OAAO,CAACoF,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI4C,UAAU,IAAIA,UAAU,CAAC3E,IAAI,EAAE;UACjC,MAAM4E,iBAAiB,GAAGD,UAAU,CAAC3E,IAAI,CAACiC,QAAQ,EAAE;UACpD,IAAI2C,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE;YACA,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGa,SAAS,MAAM;;;;MAK/B;MACA,IACEC,WAAW,KAAK/U,WAAW,CAACuR,KAAK,IACjCwD,WAAW,KAAK/U,WAAW,CAACgV,WAAW,EACvC;QACA;QACA,OAAO,cAAc;OACtB,MAAM,IACLD,WAAW,KAAK/U,WAAW,CAACwR,IAAI,IAChCuD,WAAW,KAAK/U,WAAW,CAACiV,UAAU,EACtC;QACA,OAAO,GAAGH,SAAS,MAAM;;MAG3B;MACA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAOxJ,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCA,KAAK,CACN;MACD,OAAO,gEAAgE;;EAE3E;EAEA;EAEA;EACA4J,QAAQA,CAAC3G,KAAU;IACjB,MAAM4G,SAAS,GAAG5G,KAAK,CAACU,MAAM;IAC9B,MAAMmG,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC;IACA,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAAC5L,aAAa,IACnB,IAAI,CAACb,YAAY,EAAErG,EAAE,IACrB,IAAI,CAACmH,eAAe,EACpB;MACA;MACA,IAAI,CAAC4L,oBAAoB,EAAE;MAE3B;MACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD;MACA,IAAI,CAACjM,aAAa,GAAG,IAAI;MAEzB;MACA,IAAI,CAACkM,gBAAgB,EAAE;MAEvB;MACA;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAAClT,EAAE,CACvB;YACD,IAAIuT,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGd,SAAS,CAACI,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDH,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGc,UAAU;;;UAIhD;UACA,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArK,UAAU,CAAC8J,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACW,iBAAiB,EAAEvG,aAAa,IAAI,CAAC,IAAI,CAACxJ,QAAQ,CAACwF,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMsJ,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAACvG,aAAa;IACtD,MAAMwG,eAAe,GAAGlB,SAAS,CAACmB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAACxK,MAAM,EAAE0K,CAAC,EAAE,EAAE;MAC/C,MAAMC,OAAO,GAAGH,eAAe,CAACE,CAAC,CAAC;MAClC,MAAME,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIzB,SAAS,CAAC0B,YAAY,EAAE;QAC1D,MAAMC,SAAS,GAAGN,OAAO,CAACO,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAAC1Q,QAAQ,CAACyG,IAAI,CAAEkK,CAAC,IAAKA,CAAC,CAAC1U,EAAE,KAAKwU,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQhB,kBAAkBA,CACxBgB,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAACV,iBAAiB,EAAEvG,aAAa,IAAI,CAACiH,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAACV,iBAAiB,CAACvG,aAAa,CAACoH,aAAa,CACvD,qBAAqBH,SAAS,IAAI,CACnC;EACH;EAEA;EACQzB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAAChF,QAAQ,CAAC6G,cAAc,CAAC,2BAA2B,CAAC,EAAE;MACzD,MAAMC,SAAS,GAAG9G,QAAQ,CAAC+G,aAAa,CAAC,KAAK,CAAC;MAC/CD,SAAS,CAAC7U,EAAE,GAAG,2BAA2B;MAC1C6U,SAAS,CAACE,SAAS,GAAG,wCAAwC;MAC9DF,SAAS,CAACG,SAAS,GACjB,uEAAuE;MAEzE,IAAI,IAAI,CAAClB,iBAAiB,EAAEvG,aAAa,EAAE;QACzC,IAAI,CAACuG,iBAAiB,CAACvG,aAAa,CAAC0H,OAAO,CAACJ,SAAS,CAAC;;;EAG7D;EAEA;EACQhB,oBAAoBA,CAAA;IAC1B,MAAMgB,SAAS,GAAG9G,QAAQ,CAAC6G,cAAc,CAAC,2BAA2B,CAAC;IACtE,IAAIC,SAAS,IAAIA,SAAS,CAACK,UAAU,EAAE;MACrCL,SAAS,CAACK,UAAU,CAACC,WAAW,CAACN,SAAS,CAAC;;EAE/C;EAEA;EACAzB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAClM,aAAa,IAAI,CAAC,IAAI,CAACb,YAAY,EAAErG,EAAE,IAAI,CAAC,IAAI,CAACmH,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACrB,cAAc,CAACgD,eAAe,CACjC,IAAI,CAACvC,YAAY,CAACrG,EAAE,EACpB,IAAI,CAAC+G,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAAC4B,SAAS,CAAC;MACVC,IAAI,EAAGzC,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACtC,QAAQ,IACrBsC,YAAY,CAACtC,QAAQ,CAACwF,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM6L,WAAW,GAAG,CAAC,GAAG,IAAI,CAACrR,QAAQ,CAAC;UAEtC;UACA,MAAMsR,WAAW,GAAG,IAAIC,GAAG,CAACF,WAAW,CAAC5F,GAAG,CAAElD,GAAG,IAAKA,GAAG,CAACtM,EAAE,CAAC,CAAC;UAE7D;UACA,MAAMuV,WAAW,GAAGlP,YAAY,CAACtC,QAAQ,CACtCjG,MAAM,CAAEwO,GAAG,IAAK,CAAC+I,WAAW,CAACG,GAAG,CAAClJ,GAAG,CAACtM,EAAE,CAAC,CAAC,CACzC4K,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACpL,SAAmB,CAAC,CAACwL,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACrL,SAAmB,CAAC,CAACwL,OAAO,EAAE;YACvD,OAAOF,KAAK,GAAGG,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAIqK,WAAW,CAAChM,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACxF,QAAQ,GAAG,CAAC,GAAGwR,WAAW,EAAE,GAAGH,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACrR,QAAQ,CAACwF,MAAM,GAAG,IAAI,CAACvC,kBAAkB,EAAE;cAClD,IAAI,CAACjD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC0R,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzO,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClBoO,WAAW,CAAChM,MAAM,IAAI,IAAI,CAACxC,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACAqC,UAAU,CAAC,MAAK;UACd,IAAI,CAACtC,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACvE,IAAI,CAAC9B,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC2M,oBAAoB,EAAE;QAC3B,IAAI,CAAC3N,YAAY,CAACyD,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQ+L,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAY3K,IAAI,GACtB2K,UAAU,CAAC1K,OAAO,EAAE,GACpB,IAAID,IAAI,CAAC2K,UAAoB,CAAC,CAAC1K,OAAO,EAAE;MAC9C,MAAM6K,KAAK,GACTF,UAAU,YAAY5K,IAAI,GACtB4K,UAAU,CAAC3K,OAAO,EAAE,GACpB,IAAID,IAAI,CAAC4K,UAAoB,CAAC,CAAC3K,OAAO,EAAE;MAC9C,OAAOiF,IAAI,CAACC,GAAG,CAAC0F,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;KACxC,CAAC,OAAO9M,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEAuC,cAAcA,CAACwK,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAACjC,iBAAiB,EAAEvG,aAAa,EAAE;MAE5C;MACA8F,qBAAqB,CAAC,MAAK;QACzB,MAAMR,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAACvG,aAAa;QACtD,MAAMyI,kBAAkB,GACtBnD,SAAS,CAACI,YAAY,GAAGJ,SAAS,CAAC0B,YAAY,IAC/C1B,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B;QACA;QACA;QACA,IAAIiD,KAAK,IAAIC,kBAAkB,EAAE;UAC/B;UACAnD,SAAS,CAACoD,QAAQ,CAAC;YACjB5B,GAAG,EAAExB,SAAS,CAACI,YAAY;YAC3BiD,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAI,CAAChQ,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAEmN,GAAG,CAAC;;EAEvE;EAEA;EACA;;;EAGAC,oBAAoBA,CAAA;IAClB,IAAI,CAACxP,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B;MACA,IAAI,CAACC,sBAAsB,GAAG,CAAC;;EAEnC;EAEA;;;;EAIAwP,wBAAwBA,CAACC,SAAe;IACtC,IAAI,CAACnQ,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,iCAAiC,EACjCgO,SAAS,CAACzJ,IAAI,CACf;IAED,IAAI,CAAC,IAAI,CAACxG,YAAY,EAAErG,EAAE,IAAI,CAAC,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,EAAE;MACxD,IAAI,CAACkG,YAAY,CAACyD,SAAS,CAAC,uCAAuC,CAAC;MACpE,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC7B;;IAGF;IACA,MAAM0E,UAAU,GAAG,IAAI,CAAC/M,gBAAgB,EAAEyB,EAAE,IAAI,EAAE;IAElD;IACA,IAAI,CAAC4F,cAAc,CAAC2Q,gBAAgB,CAClCjL,UAAU,EACVgL,SAAS,EACT,IAAI,CAACjQ,YAAY,EAAErG,EAAE,EACrB,IAAI,CAAC6G,sBAAsB,CAC5B,CAACgC,SAAS,CAAC;MACVC,IAAI,EAAGY,OAAO,IAAI;QAChB,IAAI,CAACvD,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,qBAAqB,EAAEoB,OAAO,CAAC;QAChE,IAAI,CAAC9C,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;QAC/B,IAAI,CAAC0E,cAAc,CAAC,IAAI,CAAC;MAC3B,CAAC;MACDvC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACvE,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CAAC,8BAA8B,CAAC;QAC3D,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEA;;;EAGA4P,yBAAyBA,CAAA;IACvB,IAAI,CAACrQ,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;IAC7D,IAAI,CAAC1B,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEA;;;;EAIA4P,mBAAmBA,CAACC,QAAgB;IAClC;IACAC,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;IAC/B,IAAI,CAACvQ,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,4BAA4BoO,QAAQ,EAAE,CAAC;EAC1E;EAEA;;;;;EAKAG,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACtL,cAAc,EAAE;IAErB;IACA;IACA,IAAI,IAAI,CAACxH,QAAQ,CAACgO,IAAI,CAAEzF,GAAG,IAAKA,GAAG,CAACS,IAAI,KAAKrP,WAAW,CAACmU,aAAa,CAAC,EAAE;MACvE;MACArI,UAAU,CAAC,MAAK;QACd,IAAI,CAACpD,GAAG,CAACqD,aAAa,EAAE;MAC1B,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQgF,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC9G,iBAAiB,IAAI,IAAI,CAACtB,YAAY,EAAErG,EAAE,EAAE;MACnD,IAAI,CAAC2H,iBAAiB,GAAG,KAAK;MAC9BwE,YAAY,CAAC,IAAI,CAACsB,WAAW,CAAC;MAE9B,IAAI,CAACtH,MAAM,CAACmC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;MAE7D;MACA,MAAMsD,cAAc,GAAG,IAAI,CAACvF,YAAY,EAAErG,EAAE;MAC5C,IAAI4L,cAAc,EAAE;QAClB,IAAI,CAAChG,cAAc,CAAC+H,UAAU,CAAC/B,cAAc,CAAC,CAAC/C,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAAC3C,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;UACH,CAAC;UACDU,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;UACH;SACD,CAAC;;;EAGR;EAEA8N,WAAWA,CAAA;IACT;IACA,IAAI,CAACrI,mBAAmB,EAAE;IAE1B,IAAI,CAACrH,aAAa,CAAC2P,WAAW,EAAE;IAChC5K,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;EAClC;EAEA;;;EAGA4K,qBAAqBA,CAAA;IACnB,IAAI,CAAC/Q,MAAM,CAACgR,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,IAAI,CAAC3P,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB,IAAI,CAACD,iBAAiB,GAAG,KAAK;;EAElC;EAEA;;;;EAIAhD,WAAWA,CAACE,KAAa;IACvB,MAAM2S,OAAO,GAAG,IAAI,CAACrP,WAAW,CAAC4G,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIyI,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAAChN,KAAK,IAAI,EAAE;MACxCgN,OAAO,CAACE,QAAQ,CAACD,YAAY,GAAG5S,KAAK,CAAC;MACtC2S,OAAO,CAACG,WAAW,EAAE;MACrB;MACA9N,UAAU,CAAC,MAAK;QACd,MAAM+N,YAAY,GAAGxJ,QAAQ,CAAC4G,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAI4C,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQhP,wBAAwBA,CAAA;IAC9B;IACA,MAAMiP,eAAe,GACnB,IAAI,CAAC7R,cAAc,CAAC8R,2BAA2B,EAAE,CAAC7O,SAAS,CAAC;MAC1DC,IAAI,EAAG6O,YAAY,IAAI;QACrB,IAAI,CAACxR,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,gCAAgCqP,YAAY,CAAC5K,IAAI,EAAE,CACpD;QAED;QACA,IACE4K,YAAY,CAAC5K,IAAI,KAAK,aAAa,IACnC4K,YAAY,CAAC/L,cAAc,KAAK,IAAI,CAACvF,YAAY,EAAErG,EAAE,EACrD;UACA;UACA,IAAI2X,YAAY,CAAC3X,EAAE,EAAE;YACnB,IAAI,CAAC4F,cAAc,CAACgS,UAAU,CAAC,CAACD,YAAY,CAAC3X,EAAE,CAAC,CAAC,CAAC6I,SAAS,EAAE;;;MAGnE,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,gDAAgD,EAChDA,KAAK,CACN;MACH;KACD,CAAC;IACJ,IAAI,CAAC5B,aAAa,CAAC8B,GAAG,CAACuO,eAAe,CAAC;IAEvC;IACA,MAAMI,OAAO,GAAG,IAAI,CAACjS,cAAc,CAACkS,aAAa,CAACjP,SAAS,CAAC;MAC1DC,IAAI,EAAGiP,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC5R,MAAM,CAACmC,KAAK,CACf,aAAa,EACb,qBAAqByP,IAAI,CAACC,MAAM,CAAC3Y,QAAQ,EAAE,CAC5C;UACD,IAAI,CAACmI,YAAY,GAAGuQ,IAAI;UACxB,IAAI,CAACtQ,aAAa,GAAG,IAAI;UAEzB;UACA,IAAI,CAAC7B,cAAc,CAACqS,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAACxQ,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACD,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACJ,aAAa,CAAC8B,GAAG,CAAC2O,OAAO,CAAC;EACjC;EAEA;;;;EAIAK,YAAYA,CAACnL,IAAuB;IAClC,IAAI,CAAC,IAAI,CAACxO,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAACyB,EAAE,EAAE;MACvDmY,OAAO,CAACnP,KAAK,CAAC,qDAAqD,CAAC;MACpE;;IAGF,IAAI,CAAC7C,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,yBAAyByD,IAAI,SAAS,IAAI,CAACxO,gBAAgB,CAACc,QAAQ,EAAE,CACvE;IAED;IACA,IAAI,CAACuG,cAAc,CAACsS,YAAY,CAC9B,IAAI,CAAC3Z,gBAAgB,CAACyB,EAAE,EACxB+M,IAAI,KAAK,OAAO,GAAGpP,QAAQ,CAAC4T,KAAK,GAAG5T,QAAQ,CAAC6T,KAAK,EAClD,IAAI,CAACnL,YAAY,EAAErG,EAAE,CACtB,CAAC6I,SAAS,CAAC;MACVC,IAAI,EAAGiP,IAAI,IAAI;QACb,IAAI,CAAC5R,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,2BAA2B,EAAEyO,IAAI,CAAC;QAClE;MACF,CAAC;;MACD/O,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;QACD,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CACzB,mDAAmD,CACpD;MACH;KACD,CAAC;EACJ;EAEA;;;EAGAyO,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC5Q,YAAY,EAAE;MACtB,IAAI,CAACrB,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,gCAAgC,CAAC;MAClE;;IAGF,IAAI,CAAC7C,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,6BAA6B,IAAI,CAAC9B,YAAY,CAACwQ,MAAM,CAAC3Y,QAAQ,EAAE,CACjE;IAED,IAAI,CAACuG,cAAc,CAACwS,UAAU,CAAC,IAAI,CAAC5Q,YAAY,CAACxH,EAAE,CAAC,CAAC6I,SAAS,CAAC;MAC7DC,IAAI,EAAGiP,IAAI,IAAI;QACb,IAAI,CAAC5R,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEyO,IAAI,CAAC;QACnE,IAAI,CAACtQ,aAAa,GAAG,KAAK;QAC1B;MACF,CAAC;;MACDuB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,0CAA0C,EAC1CA,KAAK,CACN;QACD,IAAI,CAAC9C,YAAY,CAACyD,SAAS,CACzB,oDAAoD,CACrD;QACD,IAAI,CAAClC,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACD,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGA6Q,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC7Q,YAAY,EAAE;MACtB,IAAI,CAACrB,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;MACjE;;IAGF,IAAI,CAAC7C,MAAM,CAACmD,IAAI,CACd,aAAa,EACb,uBAAuB,IAAI,CAAC9B,YAAY,CAACwQ,MAAM,CAAC3Y,QAAQ,EAAE,CAC3D;IAED,IAAI,CAACuG,cAAc,CAACyS,UAAU,CAAC,IAAI,CAAC7Q,YAAY,CAACxH,EAAE,CAAC,CAAC6I,SAAS,CAAC;MAC7DC,IAAI,EAAGiP,IAAI,IAAI;QACb,IAAI,CAAC5R,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,2BAA2B,EAAEyO,IAAI,CAAC;QAClE,IAAI,CAACtQ,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACD,YAAY,GAAG,IAAI;MAC1B,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;QACD,IAAI,CAACvB,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACD,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGA8Q,OAAOA,CAAA;IACL;IACA,IAAIC,UAAU,GAAQ,IAAI;IAE1B;IACA,MAAMpP,GAAG,GAAG,IAAI,CAACvD,cAAc,CAAC4S,WAAW,CAAC3P,SAAS,CAAEkP,IAAI,IAAI;MAC7DQ,UAAU,GAAGR,IAAI;MAEjB,IAAI,CAACQ,UAAU,EAAE;QACf,IAAI,CAACpS,MAAM,CAAC6C,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;QAChE;;MAGF,IAAI,CAAC7C,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC;MAEjD,IAAI,CAAC1D,cAAc,CAAC0S,OAAO,CAACC,UAAU,CAACvY,EAAE,CAAC,CAAC6I,SAAS,CAAC;QACnDC,IAAI,EAAGiP,IAAI,IAAI;UACb,IAAI,CAAC5R,MAAM,CAACmD,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEyO,IAAI,CAAC;QACrE,CAAC;QACD/O,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCA,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,CAAC;IAEF;IACAG,GAAG,CAAC4N,WAAW,EAAE;EACnB;;;uBA7sDWrR,oBAAoB,EAAA3H,EAAA,CAAA0a,iBAAA,CAAAC,EAAA,CAAA9S,cAAA,GAAA7H,EAAA,CAAA0a,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAA7a,EAAA,CAAA0a,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA/a,EAAA,CAAA0a,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAjb,EAAA,CAAA0a,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAAnb,EAAA,CAAA0a,iBAAA,CAAAE,EAAA,CAAAQ,MAAA,GAAApb,EAAA,CAAA0a,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAtb,EAAA,CAAA0a,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAxb,EAAA,CAAA0a,iBAAA,CAAA1a,EAAA,CAAAyb,iBAAA;IAAA;EAAA;;;YAApB9T,oBAAoB;MAAA+T,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC9BjC7b,EAAA,CAAAE,cAAA,aASC;UA4DKF,EAAA,CAAAY,UAAA,mBAAAmb,sDAAA;YAAA,OAASD,GAAA,CAAA7C,qBAAA,EAAuB;UAAA,EAAC;UAmBjCjZ,EAAA,CAAAC,SAAA,WAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAejEF,EAAA,CAAAY,UAAA,mBAAAob,mDAAA;YAAA,OAASF,GAAA,CAAA/Z,eAAA,CAAA+Z,GAAA,CAAAtb,gBAAA,kBAAAsb,GAAA,CAAAtb,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAgBE;UAEFJ,EAAA,CAAAyD,UAAA,IAAAwY,mCAAA,iBAaO;UACTjc,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,aAAmC;UAY/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA8D;UAE5DF,EAAA,CAAAyD,UAAA,KAAAyY,oCAAA,kBAkCM;UAENlc,EAAA,CAAAyD,UAAA,KAAA0Y,qCAAA,mBAMO;UACTnc,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAGtDF,EAAA,CAAAY,UAAA,mBAAAwb,uDAAA;YAAA,OAASN,GAAA,CAAAO,cAAA,EAAgB;UAAA,EAAC;UAc1Brc,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAA0b,uDAAA;YAAA,OAASR,GAAA,CAAAS,cAAA,EAAgB;UAAA,EAAC;UAc1Bvc,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAA4b,uDAAA;YAAA,OAASV,GAAA,CAAA5a,YAAA,EAAc;UAAA,EAAC;UAcxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAeC;UAdCF,EAAA,CAAAY,UAAA,mBAAA6b,uDAAA;YAAA,OAASX,GAAA,CAAAY,cAAA,EAAgB;UAAA,EAAC;UAe1B1c,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAkZ,oCAAA,mBA8EM;UACR3c,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAgc,sDAAAzZ,MAAA;YAAA,OAAU2Y,GAAA,CAAAjH,QAAA,CAAA1R,MAAA,CAAgB;UAAA,EAAC,sBAAA0Z,wDAAA1Z,MAAA;YAAA,OACf2Y,GAAA,CAAAgB,UAAA,CAAA3Z,MAAA,CAAkB;UAAA,EADH,uBAAA4Z,yDAAA5Z,MAAA;YAAA,OAEd2Y,GAAA,CAAAkB,WAAA,CAAA7Z,MAAA,CAAmB;UAAA,EAFL,kBAAA8Z,oDAAA9Z,MAAA;YAAA,OAGnB2Y,GAAA,CAAAoB,MAAA,CAAA/Z,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAA0Z,oCAAA,kBAoDM;UAGNnd,EAAA,CAAAyD,UAAA,KAAA2Z,oCAAA,kBAsBM;UAGNpd,EAAA,CAAAyD,UAAA,KAAA4Z,oCAAA,kBA0BM;UAGNrd,EAAA,CAAAyD,UAAA,KAAA6Z,oCAAA,kBA4OM;UACRtd,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAA2c,wDAAA;YAAA,OAAYzB,GAAA,CAAAxL,WAAA,EAAa;UAAA,EAAC;UAI1BtQ,EAAA,CAAAE,cAAA,eAAqC;UAIjCF,EAAA,CAAAY,UAAA,mBAAA4c,uDAAA;YAAA,OAAS1B,GAAA,CAAA3C,iBAAA,EAAmB;UAAA,EAAC;UAgB7BnZ,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAiBC;UAfCF,EAAA,CAAAY,UAAA,mBAAA6c,uDAAA;YAAA,OAAS3B,GAAA,CAAA4B,oBAAA,EAAsB;UAAA,EAAC;UAgBhC1d,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAyC;UAIrCF,EAAA,CAAAY,UAAA,qBAAA+c,2DAAAxa,MAAA;YAAA,OAAW2Y,GAAA,CAAA8B,cAAA,CAAAza,MAAA,CAAsB;UAAA,EAAC,mBAAA0a,yDAAA1a,MAAA;YAAA,OACzB2Y,GAAA,CAAAgC,aAAA,CAAA3a,MAAA,CAAqB;UAAA,EADI,mBAAA4a,yDAAA;YAAA,OAEzBjC,GAAA,CAAAkC,YAAA,EAAc;UAAA,EAFW;UAoBnChe,EAAA,CAAAI,YAAA,EAAW;UAIdJ,EAAA,CAAAE,cAAA,kBA0BC;UACCF,EAAA,CAAAyD,UAAA,KAAAwa,kCAAA,gBAA4D;UAC5Dje,EAAA,CAAAyD,UAAA,KAAAya,oCAAA,kBAUO;UACTle,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAA0a,oCAAA,kBAkDM;UAGNne,EAAA,CAAAyD,UAAA,KAAA2a,oCAAA,mBAsJM;UAGNpe,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAAyd,uDAAAlb,MAAA;YAAA,OAAU2Y,GAAA,CAAApN,cAAA,CAAAvL,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAIJJ,EAAA,CAAAyD,UAAA,KAAA6a,oCAAA,kBAYO;UACTte,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,8BAQC;UAHCF,EAAA,CAAAY,UAAA,uBAAA2d,uEAAA;YAAA,OAAazC,GAAA,CAAAvB,OAAA,EAAS;UAAA,EAAC,0BAAAiE,0EAAArb,MAAA;YAAA,OACP2Y,GAAA,CAAA2C,cAAA,CAAAtb,MAAA,CAAsB;UAAA,EADf,0BAAAub,0EAAA;YAAA,OAEP5C,GAAA,CAAA6C,cAAA,EAAgB;UAAA,EAFT;UAGxB3e,EAAA,CAAAI,YAAA,EAAqB;;;UA39BZJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAtb,gBAAA,kBAAAsb,GAAA,CAAAtb,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA0Z,GAAA,CAAAtb,gBAAA,kBAAAsb,GAAA,CAAAtb,gBAAA,CAAAc,QAAA;UAkBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAtb,gBAAA,kBAAAsb,GAAA,CAAAtb,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAwb,GAAA,CAAAtb,gBAAA,kBAAAsb,GAAA,CAAAtb,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAA8C,YAAA,CAAkB;UAmCd5e,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAA4Z,GAAA,CAAA8C,YAAA,CAAmB;UA+D5B5e,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAAyZ,GAAA,CAAA+C,UAAA,6BAA2D,UAAA/C,GAAA,CAAA+C,UAAA;UAoB3D7e,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAAyZ,GAAA,CAAA1a,YAAA,6BAA6D,UAAA0a,GAAA,CAAA1a,YAAA;UAU9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAA1a,YAAA,CAAkB;UAwFrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAAyZ,GAAA,CAAAgD,UAAA,4CAA0E;UAIvE9e,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAgD,UAAA,CAAgB;UAuDhB9e,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAiD,SAAA,CAAe;UAyBf/e,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAA4Z,GAAA,CAAAiD,SAAA,IAAAjD,GAAA,CAAA9V,QAAA,CAAAwF,MAAA,OAAyC;UA6BzCxL,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAA4Z,GAAA,CAAAiD,SAAA,IAAAjD,GAAA,CAAA9V,QAAA,CAAAwF,MAAA,KAAuC;UAmPxCxL,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAA4Z,GAAA,CAAA/R,WAAA,CAAyB;UAmBrB/J,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAAqC,WAAA,eAAAyZ,GAAA,CAAAtS,eAAA,6BAAgE,UAAAsS,GAAA,CAAAtS,eAAA;UAsBhExJ,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAqC,WAAA,eAAAyZ,GAAA,CAAAkD,kBAAA,6BAAmE,UAAAlD,GAAA,CAAAkD,kBAAA;UAkCnEhf,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAkC,UAAA,aAAA4Z,GAAA,CAAAmD,eAAA,GAA8B;UAsBhCjf,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAAqC,WAAA,gBAAAyZ,GAAA,CAAA/R,WAAA,CAAAmV,KAAA,IAAApD,GAAA,CAAAqD,gBAAA,yBAEC,YAAArD,GAAA,CAAA/R,WAAA,CAAAmV,KAAA,IAAApD,GAAA,CAAAqD,gBAAA;UAjBDnf,EAAA,CAAAkC,UAAA,cAAA4Z,GAAA,CAAA/R,WAAA,CAAAmV,KAAA,IAAApD,GAAA,CAAAqD,gBAAA,CAAmD;UAyBpBnf,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAA4Z,GAAA,CAAAqD,gBAAA,CAAuB;UAEnDnf,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAqD,gBAAA,CAAsB;UAe1Bnf,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAtS,eAAA,CAAqB;UAqDrBxJ,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAkD,kBAAA,CAAwB;UA6JzBhf,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAA4Z,GAAA,CAAAsD,kBAAA,GAA+B;UAOhCpf,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAkC,UAAA,SAAA4Z,GAAA,CAAAtS,eAAA,IAAAsS,GAAA,CAAAkD,kBAAA,IAAAlD,GAAA,CAAA1a,YAAA,CAA2D;UAgB9DpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,cAAA4Z,GAAA,CAAAuD,QAAA,CAAsB,eAAAvD,GAAA,CAAAtB,UAAA,cAAAsB,GAAA,CAAAwD,QAAA,sBAAAxD,GAAA,CAAAtb,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}