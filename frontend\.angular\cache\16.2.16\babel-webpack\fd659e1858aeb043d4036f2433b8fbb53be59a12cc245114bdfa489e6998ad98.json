{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ProfileCompletionComponent } from './profile-completion.component';\nconst routes = [{\n  path: '',\n  component: ProfileCompletionComponent\n}];\nexport let ProfileCompletionModule = class ProfileCompletionModule {};\nProfileCompletionModule = __decorate([NgModule({\n  declarations: [ProfileCompletionComponent],\n  imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes)]\n})], ProfileCompletionModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "RouterModule", "ProfileCompletionComponent", "routes", "path", "component", "ProfileCompletionModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule, Routes } from '@angular/router';\n\nimport { ProfileCompletionComponent } from './profile-completion.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileCompletionComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileCompletionComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfileCompletionModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,0BAA0B,QAAQ,gCAAgC;AAE3E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYM,WAAMI,uBAAuB,GAA7B,MAAMA,uBAAuB,GAAI;AAA3BA,uBAAuB,GAAAC,UAAA,EAVnCT,QAAQ,CAAC;EACRU,YAAY,EAAE,CACZN,0BAA0B,CAC3B;EACDO,OAAO,EAAE,CACPV,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC;CAEhC,CAAC,C,EACWG,uBAAuB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}