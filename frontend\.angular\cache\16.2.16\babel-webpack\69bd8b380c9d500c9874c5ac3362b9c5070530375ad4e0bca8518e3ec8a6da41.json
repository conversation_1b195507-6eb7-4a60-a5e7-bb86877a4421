{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Output } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let CreateUserModalComponent = class CreateUserModalComponent {\n  constructor(fb, authService, toastService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.closeModal = new EventEmitter();\n    this.userCreated = new EventEmitter();\n    this.loading = false;\n    this.roles = ['student', 'teacher', 'admin'];\n    this.createUserForm = this.fb.group({\n      fullName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      role: ['student', Validators.required]\n    });\n  }\n  onSubmit() {\n    if (this.createUserForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.toastService.showError('Authentication token not found');\n      return;\n    }\n    this.authService.createUser(this.createUserForm.value, token).subscribe({\n      next: response => {\n        this.toastService.showSuccess('User created successfully');\n        this.userCreated.emit(response.user);\n        this.closeModal.emit(true);\n        this.loading = false;\n      },\n      error: error => {\n        this.toastService.showError(error.error?.message || 'Failed to create user');\n        this.loading = false;\n      }\n    });\n  }\n  close() {\n    this.closeModal.emit(true);\n  }\n};\n__decorate([Output()], CreateUserModalComponent.prototype, \"closeModal\", void 0);\n__decorate([Output()], CreateUserModalComponent.prototype, \"userCreated\", void 0);\nCreateUserModalComponent = __decorate([Component({\n  selector: 'app-create-user-modal',\n  templateUrl: './create-user-modal.component.html',\n  styleUrls: ['./create-user-modal.component.css']\n})], CreateUserModalComponent);", "map": {"version": 3, "names": ["Component", "EventEmitter", "Output", "Validators", "CreateUserModalComponent", "constructor", "fb", "authService", "toastService", "closeModal", "userCreated", "loading", "roles", "createUserForm", "group", "fullName", "required", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "role", "onSubmit", "invalid", "token", "localStorage", "getItem", "showError", "createUser", "value", "subscribe", "next", "response", "showSuccess", "emit", "user", "error", "message", "close", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\create-user-modal\\create-user-modal.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Output } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\n\r\n@Component({\r\n  selector: 'app-create-user-modal',\r\n  templateUrl: './create-user-modal.component.html',\r\n  styleUrls: ['./create-user-modal.component.css']\r\n})\r\nexport class CreateUserModalComponent {\r\n  @Output() closeModal = new EventEmitter<boolean>();\r\n  @Output() userCreated = new EventEmitter<any>();\r\n\r\n  createUserForm: FormGroup;\r\n  loading = false;\r\n  roles = ['student', 'teacher', 'admin'];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private toastService: ToastService\r\n  ) {\r\n    this.createUserForm = this.fb.group({\r\n      fullName: ['', [Validators.required]],\r\n      email: ['', [Valida<PERSON>.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      role: ['student', Validators.required]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.createUserForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const token = localStorage.getItem('token');\r\n\r\n    if (!token) {\r\n      this.toastService.showError('Authentication token not found');\r\n      return;\r\n    }\r\n\r\n    this.authService.createUser(this.createUserForm.value, token).subscribe({\r\n      next: (response: any) => {\r\n        this.toastService.showSuccess('User created successfully');\r\n        this.userCreated.emit(response.user);\r\n        this.closeModal.emit(true);\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.toastService.showError(error.error?.message || 'Failed to create user');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  close() {\r\n    this.closeModal.emit(true);\r\n  }\r\n} "], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAC/D,SAAiCC,UAAU,QAAQ,gBAAgB;AAS5D,WAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAQnCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,YAA0B;IAF1B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IAVZ,KAAAC,UAAU,GAAG,IAAIR,YAAY,EAAW;IACxC,KAAAS,WAAW,GAAG,IAAIT,YAAY,EAAO;IAG/C,KAAAU,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAOrC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAClCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACa,QAAQ,CAAC,CAAC;MACrCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACc,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACf,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,IAAI,EAAE,CAAC,SAAS,EAAEjB,UAAU,CAACa,QAAQ;KACtC,CAAC;EACJ;EAEAK,QAAQA,CAAA;IACN,IAAI,IAAI,CAACR,cAAc,CAACS,OAAO,EAAE;MAC/B;;IAGF,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,MAAMY,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACF,KAAK,EAAE;MACV,IAAI,CAACf,YAAY,CAACkB,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAACnB,WAAW,CAACoB,UAAU,CAAC,IAAI,CAACd,cAAc,CAACe,KAAK,EAAEL,KAAK,CAAC,CAACM,SAAS,CAAC;MACtEC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACvB,YAAY,CAACwB,WAAW,CAAC,2BAA2B,CAAC;QAC1D,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC;QACpC,IAAI,CAACzB,UAAU,CAACwB,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACtB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3B,YAAY,CAACkB,SAAS,CAACS,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,uBAAuB,CAAC;QAC5E,IAAI,CAACzB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA0B,KAAKA,CAAA;IACH,IAAI,CAAC5B,UAAU,CAACwB,IAAI,CAAC,IAAI,CAAC;EAC5B;CACD;AAlDWK,UAAA,EAATpC,MAAM,EAAE,C,2DAA0C;AACzCoC,UAAA,EAATpC,MAAM,EAAE,C,4DAAuC;AAFrCE,wBAAwB,GAAAkC,UAAA,EALpCtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACWrC,wBAAwB,CAmDpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}