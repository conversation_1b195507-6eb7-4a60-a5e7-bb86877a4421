{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@auth0/angular-jwt\";\nexport class RoleService {\n  constructor(jwtHelper) {\n    this.jwtHelper = jwtHelper;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.permissionsSubject = new BehaviorSubject(null);\n    this.permissions$ = this.permissionsSubject.asObservable();\n    this.loadCurrentUser();\n  }\n  /**\n   * Charge l'utilisateur actuel depuis le token\n   */\n  loadCurrentUser() {\n    const token = localStorage.getItem('token');\n    if (token && !this.jwtHelper.isTokenExpired(token)) {\n      const decodedToken = this.jwtHelper.decodeToken(token);\n      const user = {\n        _id: decodedToken.id,\n        id: decodedToken.id,\n        username: decodedToken.username,\n        email: decodedToken.email,\n        role: decodedToken.role,\n        image: decodedToken.image,\n        isActive: true\n      };\n      this.currentUserSubject.next(user);\n      this.updatePermissions(user.role);\n    }\n  }\n  /**\n   * Met à jour les permissions basées sur le rôle\n   */\n  updatePermissions(role) {\n    const permissions = {\n      canCreatePlanning: this.canCreatePlanning(role),\n      canEditPlanning: this.canEditPlanning(role),\n      canDeletePlanning: this.canDeletePlanning(role),\n      canCreateReunion: this.canCreateReunion(role),\n      canEditReunion: this.canEditReunion(role),\n      canDeleteReunion: this.canDeleteReunion(role),\n      canViewAllUsers: this.canViewAllUsers(role),\n      canManageUsers: this.canManageUsers(role),\n      canAccessAdminPanel: this.canAccessAdminPanel(role),\n      canForceDelete: this.canForceDelete(role),\n      canViewDetailedReports: this.canViewDetailedReports(role)\n    };\n    this.permissionsSubject.next(permissions);\n  }\n  /**\n   * Obtient l'utilisateur actuel\n   */\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Obtient le rôle de l'utilisateur actuel\n   */\n  getCurrentUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est admin\n   */\n  isAdmin() {\n    return this.getCurrentUserRole() === 'admin';\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est tuteur\n   */\n  isTutor() {\n    return this.getCurrentUserRole() === 'tutor';\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est étudiant\n   */\n  isStudent() {\n    return this.getCurrentUserRole() === 'student';\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est alumni\n   */\n  isAlumni() {\n    return this.getCurrentUserRole() === 'alumni';\n  }\n  /**\n   * Vérifie si l'utilisateur peut créer des plannings\n   */\n  canCreatePlanning(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut éditer des plannings\n   */\n  canEditPlanning(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut supprimer des plannings\n   */\n  canDeletePlanning(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut créer des réunions\n   */\n  canCreateReunion(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut éditer des réunions\n   */\n  canEditReunion(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut supprimer des réunions\n   */\n  canDeleteReunion(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut voir tous les utilisateurs\n   */\n  canViewAllUsers(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur peut gérer les utilisateurs\n   */\n  canManageUsers(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return userRole === 'admin';\n  }\n  /**\n   * Vérifie si l'utilisateur peut accéder au panel admin\n   */\n  canAccessAdminPanel(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return userRole === 'admin';\n  }\n  /**\n   * Vérifie si l'utilisateur peut forcer la suppression\n   */\n  canForceDelete(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return userRole === 'admin';\n  }\n  /**\n   * Vérifie si l'utilisateur peut voir les rapports détaillés\n   */\n  canViewDetailedReports(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return ['admin', 'tutor'].includes(userRole || '');\n  }\n  /**\n   * Vérifie si l'utilisateur est propriétaire d'une ressource\n   */\n  isOwner(resourceCreatorId) {\n    const currentUser = this.getCurrentUser();\n    return currentUser ? currentUser._id === resourceCreatorId : false;\n  }\n  /**\n   * Vérifie si l'utilisateur peut modifier une ressource (propriétaire ou admin)\n   */\n  canModifyResource(resourceCreatorId) {\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\n  }\n  /**\n   * Vérifie si l'utilisateur peut supprimer une ressource (propriétaire ou admin)\n   */\n  canDeleteResource(resourceCreatorId) {\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\n  }\n  /**\n   * Met à jour l'utilisateur actuel (appelé après login)\n   */\n  updateCurrentUser(user) {\n    this.currentUserSubject.next(user);\n    this.updatePermissions(user.role);\n  }\n  /**\n   * Nettoie les données utilisateur (appelé après logout)\n   */\n  clearUserData() {\n    this.currentUserSubject.next(null);\n    this.permissionsSubject.next(null);\n  }\n  /**\n   * Obtient un message d'erreur personnalisé basé sur le rôle\n   */\n  getAccessDeniedMessage(action) {\n    const role = this.getCurrentUserRole();\n    const roleNames = {\n      'admin': 'Administrateur',\n      'tutor': 'Tuteur',\n      'alumni': 'Alumni',\n      'student': 'Étudiant'\n    };\n    return `Accès refusé. Votre rôle (${roleNames[role] || role}) ne vous permet pas de ${action}.`;\n  }\n  static {\n    this.ɵfac = function RoleService_Factory(t) {\n      return new (t || RoleService)(i0.ɵɵinject(i1.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleService,\n      factory: RoleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "RoleService", "constructor", "jwtHelper", "currentUserSubject", "currentUser$", "asObservable", "permissionsSubject", "permissions$", "loadCurrentUser", "token", "localStorage", "getItem", "isTokenExpired", "decodedToken", "decodeToken", "user", "_id", "id", "username", "email", "role", "image", "isActive", "next", "updatePermissions", "permissions", "canCreatePlanning", "canEditPlanning", "canDeletePlanning", "canCreateReunion", "canEditReunion", "canDeleteReunion", "canViewAllUsers", "canManageUsers", "canAccessAdminPanel", "canForceDelete", "canViewDetailedReports", "getCurrentUser", "value", "getCurrentUserRole", "isAdmin", "isTutor", "isStudent", "<PERSON><PERSON><PERSON><PERSON>", "userRole", "includes", "isOwner", "resourceCreatorId", "currentUser", "canModifyResource", "canDeleteResource", "updateCurrentUser", "clearUserData", "getAccessDeniedMessage", "action", "roleNames", "i0", "ɵɵinject", "i1", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\role.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { User } from '@app/models/user.model';\r\n\r\nexport interface UserPermissions {\r\n  canCreatePlanning: boolean;\r\n  canEditPlanning: boolean;\r\n  canDeletePlanning: boolean;\r\n  canCreateReunion: boolean;\r\n  canEditReunion: boolean;\r\n  canDeleteReunion: boolean;\r\n  canViewAllUsers: boolean;\r\n  canManageUsers: boolean;\r\n  canAccessAdminPanel: boolean;\r\n  canForceDelete: boolean;\r\n  canViewDetailedReports: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RoleService {\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private permissionsSubject = new BehaviorSubject<UserPermissions | null>(null);\r\n  public permissions$ = this.permissionsSubject.asObservable();\r\n\r\n  constructor(private jwtHelper: JwtHelperService) {\r\n    this.loadCurrentUser();\r\n  }\r\n\r\n  /**\r\n   * Charge l'utilisateur actuel depuis le token\r\n   */\r\n  private loadCurrentUser(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (token && !this.jwtHelper.isTokenExpired(token)) {\r\n      const decodedToken = this.jwtHelper.decodeToken(token);\r\n      const user: User = {\r\n        _id: decodedToken.id,\r\n        id: decodedToken.id,\r\n        username: decodedToken.username,\r\n        email: decodedToken.email,\r\n        role: decodedToken.role,\r\n        image: decodedToken.image,\r\n        isActive: true\r\n      };\r\n      this.currentUserSubject.next(user);\r\n      this.updatePermissions(user.role);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Met à jour les permissions basées sur le rôle\r\n   */\r\n  private updatePermissions(role: string): void {\r\n    const permissions: UserPermissions = {\r\n      canCreatePlanning: this.canCreatePlanning(role),\r\n      canEditPlanning: this.canEditPlanning(role),\r\n      canDeletePlanning: this.canDeletePlanning(role),\r\n      canCreateReunion: this.canCreateReunion(role),\r\n      canEditReunion: this.canEditReunion(role),\r\n      canDeleteReunion: this.canDeleteReunion(role),\r\n      canViewAllUsers: this.canViewAllUsers(role),\r\n      canManageUsers: this.canManageUsers(role),\r\n      canAccessAdminPanel: this.canAccessAdminPanel(role),\r\n      canForceDelete: this.canForceDelete(role),\r\n      canViewDetailedReports: this.canViewDetailedReports(role)\r\n    };\r\n    this.permissionsSubject.next(permissions);\r\n  }\r\n\r\n  /**\r\n   * Obtient l'utilisateur actuel\r\n   */\r\n  getCurrentUser(): User | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  /**\r\n   * Obtient le rôle de l'utilisateur actuel\r\n   */\r\n  getCurrentUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est admin\r\n   */\r\n  isAdmin(): boolean {\r\n    return this.getCurrentUserRole() === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est tuteur\r\n   */\r\n  isTutor(): boolean {\r\n    return this.getCurrentUserRole() === 'tutor';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est étudiant\r\n   */\r\n  isStudent(): boolean {\r\n    return this.getCurrentUserRole() === 'student';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est alumni\r\n   */\r\n  isAlumni(): boolean {\r\n    return this.getCurrentUserRole() === 'alumni';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut créer des plannings\r\n   */\r\n  canCreatePlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut éditer des plannings\r\n   */\r\n  canEditPlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer des plannings\r\n   */\r\n  canDeletePlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut créer des réunions\r\n   */\r\n  canCreateReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut éditer des réunions\r\n   */\r\n  canEditReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer des réunions\r\n   */\r\n  canDeleteReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut voir tous les utilisateurs\r\n   */\r\n  canViewAllUsers(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut gérer les utilisateurs\r\n   */\r\n  canManageUsers(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut accéder au panel admin\r\n   */\r\n  canAccessAdminPanel(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut forcer la suppression\r\n   */\r\n  canForceDelete(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut voir les rapports détaillés\r\n   */\r\n  canViewDetailedReports(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur est propriétaire d'une ressource\r\n   */\r\n  isOwner(resourceCreatorId: string): boolean {\r\n    const currentUser = this.getCurrentUser();\r\n    return currentUser ? currentUser._id === resourceCreatorId : false;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut modifier une ressource (propriétaire ou admin)\r\n   */\r\n  canModifyResource(resourceCreatorId: string): boolean {\r\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer une ressource (propriétaire ou admin)\r\n   */\r\n  canDeleteResource(resourceCreatorId: string): boolean {\r\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\r\n  }\r\n\r\n  /**\r\n   * Met à jour l'utilisateur actuel (appelé après login)\r\n   */\r\n  updateCurrentUser(user: User): void {\r\n    this.currentUserSubject.next(user);\r\n    this.updatePermissions(user.role);\r\n  }\r\n\r\n  /**\r\n   * Nettoie les données utilisateur (appelé après logout)\r\n   */\r\n  clearUserData(): void {\r\n    this.currentUserSubject.next(null);\r\n    this.permissionsSubject.next(null);\r\n  }\r\n\r\n  /**\r\n   * Obtient un message d'erreur personnalisé basé sur le rôle\r\n   */\r\n  getAccessDeniedMessage(action: string): string {\r\n    const role = this.getCurrentUserRole();\r\n    const roleNames = {\r\n      'admin': 'Administrateur',\r\n      'tutor': 'Tuteur',\r\n      'alumni': 'Alumni',\r\n      'student': 'Étudiant'\r\n    };\r\n\r\n    return `Accès refusé. Votre rôle (${roleNames[role as keyof typeof roleNames] || role}) ne vous permet pas de ${action}.`;\r\n  }\r\n}"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;;;AAoBlD,OAAM,MAAOC,WAAW;EAOtBC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;IANrB,KAAAC,kBAAkB,GAAG,IAAIJ,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAK,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAEpD,KAAAC,kBAAkB,GAAG,IAAIP,eAAe,CAAyB,IAAI,CAAC;IACvE,KAAAQ,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACD,YAAY,EAAE;IAG1D,IAAI,CAACG,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,IAAI,CAAC,IAAI,CAACP,SAAS,CAACU,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAMI,YAAY,GAAG,IAAI,CAACX,SAAS,CAACY,WAAW,CAACL,KAAK,CAAC;MACtD,MAAMM,IAAI,GAAS;QACjBC,GAAG,EAAEH,YAAY,CAACI,EAAE;QACpBA,EAAE,EAAEJ,YAAY,CAACI,EAAE;QACnBC,QAAQ,EAAEL,YAAY,CAACK,QAAQ;QAC/BC,KAAK,EAAEN,YAAY,CAACM,KAAK;QACzBC,IAAI,EAAEP,YAAY,CAACO,IAAI;QACvBC,KAAK,EAAER,YAAY,CAACQ,KAAK;QACzBC,QAAQ,EAAE;OACX;MACD,IAAI,CAACnB,kBAAkB,CAACoB,IAAI,CAACR,IAAI,CAAC;MAClC,IAAI,CAACS,iBAAiB,CAACT,IAAI,CAACK,IAAI,CAAC;;EAErC;EAEA;;;EAGQI,iBAAiBA,CAACJ,IAAY;IACpC,MAAMK,WAAW,GAAoB;MACnCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACN,IAAI,CAAC;MAC/CO,eAAe,EAAE,IAAI,CAACA,eAAe,CAACP,IAAI,CAAC;MAC3CQ,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACR,IAAI,CAAC;MAC/CS,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACT,IAAI,CAAC;MAC7CU,cAAc,EAAE,IAAI,CAACA,cAAc,CAACV,IAAI,CAAC;MACzCW,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACX,IAAI,CAAC;MAC7CY,eAAe,EAAE,IAAI,CAACA,eAAe,CAACZ,IAAI,CAAC;MAC3Ca,cAAc,EAAE,IAAI,CAACA,cAAc,CAACb,IAAI,CAAC;MACzCc,mBAAmB,EAAE,IAAI,CAACA,mBAAmB,CAACd,IAAI,CAAC;MACnDe,cAAc,EAAE,IAAI,CAACA,cAAc,CAACf,IAAI,CAAC;MACzCgB,sBAAsB,EAAE,IAAI,CAACA,sBAAsB,CAAChB,IAAI;KACzD;IACD,IAAI,CAACd,kBAAkB,CAACiB,IAAI,CAACE,WAAW,CAAC;EAC3C;EAEA;;;EAGAY,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClC,kBAAkB,CAACmC,KAAK;EACtC;EAEA;;;EAGAC,kBAAkBA,CAAA;IAChB,MAAMxB,IAAI,GAAG,IAAI,CAACsB,cAAc,EAAE;IAClC,OAAOtB,IAAI,GAAGA,IAAI,CAACK,IAAI,GAAG,IAAI;EAChC;EAEA;;;EAGAoB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACD,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;;;EAGAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACF,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;;;EAGAG,SAASA,CAAA;IACP,OAAO,IAAI,CAACH,kBAAkB,EAAE,KAAK,SAAS;EAChD;EAEA;;;EAGAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACJ,kBAAkB,EAAE,KAAK,QAAQ;EAC/C;EAEA;;;EAGAb,iBAAiBA,CAACN,IAAa;IAC7B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAjB,eAAeA,CAACP,IAAa;IAC3B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAhB,iBAAiBA,CAACR,IAAa;IAC7B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAf,gBAAgBA,CAACT,IAAa;IAC5B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EACzE;EAEA;;;EAGAd,cAAcA,CAACV,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EACzE;EAEA;;;EAGAb,gBAAgBA,CAACX,IAAa;IAC5B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAZ,eAAeA,CAACZ,IAAa;IAC3B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAX,cAAcA,CAACb,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAV,mBAAmBA,CAACd,IAAa;IAC/B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAT,cAAcA,CAACf,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAR,sBAAsBA,CAAChB,IAAa;IAClC,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACD,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAE,OAAOA,CAACC,iBAAyB;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAACX,cAAc,EAAE;IACzC,OAAOW,WAAW,GAAGA,WAAW,CAAChC,GAAG,KAAK+B,iBAAiB,GAAG,KAAK;EACpE;EAEA;;;EAGAE,iBAAiBA,CAACF,iBAAyB;IACzC,OAAO,IAAI,CAACP,OAAO,EAAE,IAAI,IAAI,CAACM,OAAO,CAACC,iBAAiB,CAAC;EAC1D;EAEA;;;EAGAG,iBAAiBA,CAACH,iBAAyB;IACzC,OAAO,IAAI,CAACP,OAAO,EAAE,IAAI,IAAI,CAACM,OAAO,CAACC,iBAAiB,CAAC;EAC1D;EAEA;;;EAGAI,iBAAiBA,CAACpC,IAAU;IAC1B,IAAI,CAACZ,kBAAkB,CAACoB,IAAI,CAACR,IAAI,CAAC;IAClC,IAAI,CAACS,iBAAiB,CAACT,IAAI,CAACK,IAAI,CAAC;EACnC;EAEA;;;EAGAgC,aAAaA,CAAA;IACX,IAAI,CAACjD,kBAAkB,CAACoB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACjB,kBAAkB,CAACiB,IAAI,CAAC,IAAI,CAAC;EACpC;EAEA;;;EAGA8B,sBAAsBA,CAACC,MAAc;IACnC,MAAMlC,IAAI,GAAG,IAAI,CAACmB,kBAAkB,EAAE;IACtC,MAAMgB,SAAS,GAAG;MAChB,OAAO,EAAE,gBAAgB;MACzB,OAAO,EAAE,QAAQ;MACjB,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE;KACZ;IAED,OAAO,6BAA6BA,SAAS,CAACnC,IAA8B,CAAC,IAAIA,IAAI,2BAA2BkC,MAAM,GAAG;EAC3H;;;uBA1OWtD,WAAW,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAX3D,WAAW;MAAA4D,OAAA,EAAX5D,WAAW,CAAA6D,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}