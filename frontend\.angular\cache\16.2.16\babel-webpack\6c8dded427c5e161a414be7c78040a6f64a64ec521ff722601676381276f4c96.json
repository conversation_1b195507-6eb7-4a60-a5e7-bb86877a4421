{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\nimport * as i0 from \"@angular/core\";\nexport class ReunionsModule {\n  static {\n    this.ɵfac = function ReunionsModule_Factory(t) {\n      return new (t || ReunionsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReunionsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReunionsModule, {\n    declarations: [ReunionListComponent, ReunionDetailComponent, ReunionFormComponent, ReunionEditComponent],\n    imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReunionsRoutingModule", "ReunionListComponent", "ReunionDetailComponent", "ReunionFormComponent", "RouterModule", "FormsModule", "ReactiveFormsModule", "PipesModule", "ReunionEditComponent", "ReunionsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunions.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ReunionsRoutingModule } from './reunions-routing.module';\r\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\r\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\r\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { PipesModule } from '../../../pipes/pipes.module';\r\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ReunionListComponent,\r\n    ReunionDetailComponent,\r\n    ReunionFormComponent,\r\n    ReunionEditComponent,\r\n  \r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReunionsRoutingModule,\r\n    RouterModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    PipesModule,\r\n  ],\r\n})\r\nexport class ReunionsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,oBAAoB,QAAQ,uCAAuC;;AAmB5E,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBARvBV,YAAY,EACZC,qBAAqB,EACrBI,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW;IAAA;EAAA;;;2EAGFE,cAAc;IAAAC,YAAA,GAfvBT,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBK,oBAAoB;IAAAG,OAAA,GAIpBZ,YAAY,EACZC,qBAAqB,EACrBI,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}