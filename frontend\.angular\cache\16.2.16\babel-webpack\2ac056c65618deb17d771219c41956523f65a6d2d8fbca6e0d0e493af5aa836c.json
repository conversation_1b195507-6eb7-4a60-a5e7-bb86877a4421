{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.AUTH_API = 'http://localhost:3000/api/auth';\n    this.ADMIN_API = 'http://localhost:3000/api/admin';\n  }\n  // Auth endpoints\n  signup(data) {\n    return this.http.post(`${this.AUTH_API}/signup`, data);\n  }\n  verifyEmail(data) {\n    return this.http.post(`${this.AUTH_API}/verify-email`, data);\n  }\n  login(data) {\n    return this.http.post(`${this.AUTH_API}/login`, data);\n  }\n  forgotPassword(email) {\n    return this.http.post(`${this.AUTH_API}/forgot-password`, {\n      email\n    });\n  }\n  resetPassword(data) {\n    return this.http.post(`${this.AUTH_API}/reset-password`, data);\n  }\n  getProfile(token) {\n    return this.http.get(`${this.AUTH_API}/profile`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  updateProfile(formData, token) {\n    return this.http.put(`${this.AUTH_API}/update-profile`, formData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  removeProfileImage(token) {\n    return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  changePassword(data, token) {\n    return this.http.put(`${this.AUTH_API}/change-password`, data, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  // Admin endpoints\n  getAllUsers(token) {\n    return this.http.get(`${this.ADMIN_API}/users`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  createUser(userData, token) {\n    return this.http.post(`${this.ADMIN_API}/users`, userData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  createUserWithGeneratedPassword(userData, token) {\n    return this.http.post(`${this.ADMIN_API}/users/create-with-password`, userData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  updateUserRole(userId, role, token) {\n    return this.http.put(`${this.ADMIN_API}/users/${userId}/role`, {\n      role\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  deleteUser(userId, token) {\n    return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  toggleUserActivation(userId, isActive, token) {\n    return this.http.put(`${this.ADMIN_API}/users/${userId}/activation`, {\n      isActive\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  getUserById(userId, token) {\n    return this.http.get(`${this.ADMIN_API}/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n  }\n  getUserRole() {\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    return user?.role || '';\n  }\n  isAdmin() {\n    return this.getUserRole() === 'admin';\n  }\n  // Complete user profile\n  completeProfile(profileData) {\n    return this.http.put(`${this.API_URL}/user/complete-profile`, profileData, {\n      headers: this.getAuthHeaders()\n    });\n  }\n  // Check if user needs to complete profile\n  shouldCompleteProfile() {\n    const user = this.getCurrentUser();\n    return user && (user.isFirstLogin || user.profileCompletionPercentage < 100);\n  }\n  // Get current user profile completion percentage\n  getProfileCompletionPercentage() {\n    const user = this.getCurrentUser();\n    return user?.profileCompletionPercentage || 0;\n  }\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }\n  resendCode(email) {\n    return this.http.post(`${this.AUTH_API}/resend-code`, {\n      email\n    });\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthService", "constructor", "http", "AUTH_API", "ADMIN_API", "signup", "data", "post", "verifyEmail", "login", "forgotPassword", "email", "resetPassword", "getProfile", "token", "get", "headers", "Authorization", "updateProfile", "formData", "put", "removeProfileImage", "delete", "changePassword", "getAllUsers", "createUser", "userData", "createUserWithGeneratedPassword", "updateUserRole", "userId", "role", "deleteUser", "toggleUserActivation", "isActive", "getUserById", "getUserRole", "user", "JSON", "parse", "localStorage", "getItem", "isAdmin", "completeProfile", "profileData", "API_URL", "getAuthHeaders", "shouldCompleteProfile", "getCurrentUser", "is<PERSON>irstL<PERSON>in", "profileCompletionPercentage", "getProfileCompletionPercentage", "logout", "removeItem", "resendCode", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AuthService {\r\n  private AUTH_API = 'http://localhost:3000/api/auth';\r\n  private ADMIN_API = 'http://localhost:3000/api/admin';\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Auth endpoints\r\n  signup(data: any) {\r\n    return this.http.post(`${this.AUTH_API}/signup`, data);\r\n  }\r\n\r\n  verifyEmail(data: { email: string; code: string }) {\r\n    return this.http.post(`${this.AUTH_API}/verify-email`, data);\r\n  }\r\n\r\n  login(data: any) {\r\n    return this.http.post(`${this.AUTH_API}/login`, data);\r\n  }\r\n\r\n  forgotPassword(email: string) {\r\n    return this.http.post(`${this.AUTH_API}/forgot-password`, { email });\r\n  }\r\n\r\n  resetPassword(data: { email: string; code: string; newPassword: string }) {\r\n    return this.http.post(`${this.AUTH_API}/reset-password`, data);\r\n  }\r\n\r\n  getProfile(token: string) {\r\n    return this.http.get(`${this.AUTH_API}/profile`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  updateProfile(formData: FormData, token: string) {\r\n    return this.http.put(`${this.AUTH_API}/update-profile`, formData, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  removeProfileImage(token: string) {\r\n    return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  changePassword(data: any, token: string) {\r\n    return this.http.put(`${this.AUTH_API}/change-password`, data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  // Admin endpoints\r\n  getAllUsers(token: string) {\r\n    return this.http.get(`${this.ADMIN_API}/users`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  createUser(userData: any, token: string) {\r\n    return this.http.post(`${this.ADMIN_API}/users`, userData, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  createUserWithGeneratedPassword(userData: any, token: string) {\r\n    return this.http.post(`${this.ADMIN_API}/users/create-with-password`, userData, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  updateUserRole(userId: string, role: string, token: string) {\r\n    return this.http.put(\r\n      `${this.ADMIN_API}/users/${userId}/role`,\r\n      { role },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n  }\r\n\r\n  deleteUser(userId: string, token: string) {\r\n    return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  toggleUserActivation(userId: string, isActive: boolean, token: string) {\r\n    return this.http.put(\r\n      `${this.ADMIN_API}/users/${userId}/activation`,\r\n      { isActive },\r\n      {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserById(userId: string, token: string) {\r\n    return this.http.get(`${this.ADMIN_API}/users/${userId}`, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    });\r\n  }\r\n\r\n  getUserRole(): string {\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    return user?.role || '';\r\n  }\r\n\r\n  isAdmin(): boolean {\r\n    return this.getUserRole() === 'admin';\r\n  }\r\n\r\n  // Complete user profile\r\n  completeProfile(profileData: any): Observable<any> {\r\n    return this.http.put(`${this.API_URL}/user/complete-profile`, profileData, {\r\n      headers: this.getAuthHeaders()\r\n    });\r\n  }\r\n\r\n  // Check if user needs to complete profile\r\n  shouldCompleteProfile(): boolean {\r\n    const user = this.getCurrentUser();\r\n    return user && (user.isFirstLogin || user.profileCompletionPercentage < 100);\r\n  }\r\n\r\n  // Get current user profile completion percentage\r\n  getProfileCompletionPercentage(): number {\r\n    const user = this.getCurrentUser();\r\n    return user?.profileCompletionPercentage || 0;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n  }\r\n\r\n  resendCode(email: string) {\r\n    return this.http.post(`${this.AUTH_API}/resend-code`, { email });\r\n  }\r\n}\r\n"], "mappings": ";;AAKA,OAAM,MAAOA,WAAW;EAItBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,QAAQ,GAAG,gCAAgC;IAC3C,KAAAC,SAAS,GAAG,iCAAiC;EAEd;EAEvC;EACAC,MAAMA,CAACC,IAAS;IACd,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,SAAS,EAAEG,IAAI,CAAC;EACxD;EAEAE,WAAWA,CAACF,IAAqC;IAC/C,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,eAAe,EAAEG,IAAI,CAAC;EAC9D;EAEAG,KAAKA,CAACH,IAAS;IACb,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,QAAQ,EAAEG,IAAI,CAAC;EACvD;EAEAI,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAACT,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,kBAAkB,EAAE;MAAEQ;IAAK,CAAE,CAAC;EACtE;EAEAC,aAAaA,CAACN,IAA0D;IACtE,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,iBAAiB,EAAEG,IAAI,CAAC;EAChE;EAEAO,UAAUA,CAACC,KAAa;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACZ,QAAQ,UAAU,EAAE;MAC/Ca,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAI,aAAaA,CAACC,QAAkB,EAAEL,KAAa;IAC7C,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,iBAAiB,EAAEgB,QAAQ,EAAE;MAChEH,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAO,kBAAkBA,CAACP,KAAa;IAC9B,OAAO,IAAI,CAACZ,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAACnB,QAAQ,uBAAuB,EAAE;MAC/Da,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAS,cAAcA,CAACjB,IAAS,EAAEQ,KAAa;IACrC,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,kBAAkB,EAAEG,IAAI,EAAE;MAC7DU,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEA;EACAU,WAAWA,CAACV,KAAa;IACvB,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACX,SAAS,QAAQ,EAAE;MAC9CY,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAW,UAAUA,CAACC,QAAa,EAAEZ,KAAa;IACrC,OAAO,IAAI,CAACZ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACH,SAAS,QAAQ,EAAEsB,QAAQ,EAAE;MACzDV,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAa,+BAA+BA,CAACD,QAAa,EAAEZ,KAAa;IAC1D,OAAO,IAAI,CAACZ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACH,SAAS,6BAA6B,EAAEsB,QAAQ,EAAE;MAC9EV,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAc,cAAcA,CAACC,MAAc,EAAEC,IAAY,EAAEhB,KAAa;IACxD,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAAChB,SAAS,UAAUyB,MAAM,OAAO,EACxC;MAAEC;IAAI,CAAE,EACR;MACEd,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CACF;EACH;EAEAiB,UAAUA,CAACF,MAAc,EAAEf,KAAa;IACtC,OAAO,IAAI,CAACZ,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAAClB,SAAS,UAAUyB,MAAM,EAAE,EAAE;MAC3Db,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAkB,oBAAoBA,CAACH,MAAc,EAAEI,QAAiB,EAAEnB,KAAa;IACnE,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAAChB,SAAS,UAAUyB,MAAM,aAAa,EAC9C;MAAEI;IAAQ,CAAE,EACZ;MACEjB,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CACF;EACH;EAEAoB,WAAWA,CAACL,MAAc,EAAEf,KAAa;IACvC,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACX,SAAS,UAAUyB,MAAM,EAAE,EAAE;MACxDb,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAE;KAC5C,CAAC;EACJ;EAEAqB,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D,OAAOJ,IAAI,EAAEN,IAAI,IAAI,EAAE;EACzB;EAEAW,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,WAAW,EAAE,KAAK,OAAO;EACvC;EAEA;EACAO,eAAeA,CAACC,WAAgB;IAC9B,OAAO,IAAI,CAACzC,IAAI,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACwB,OAAO,wBAAwB,EAAED,WAAW,EAAE;MACzE3B,OAAO,EAAE,IAAI,CAAC6B,cAAc;KAC7B,CAAC;EACJ;EAEA;EACAC,qBAAqBA,CAAA;IACnB,MAAMV,IAAI,GAAG,IAAI,CAACW,cAAc,EAAE;IAClC,OAAOX,IAAI,KAAKA,IAAI,CAACY,YAAY,IAAIZ,IAAI,CAACa,2BAA2B,GAAG,GAAG,CAAC;EAC9E;EAEA;EACAC,8BAA8BA,CAAA;IAC5B,MAAMd,IAAI,GAAG,IAAI,CAACW,cAAc,EAAE;IAClC,OAAOX,IAAI,EAAEa,2BAA2B,IAAI,CAAC;EAC/C;EAEAE,MAAMA,CAAA;IACJZ,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;IAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;EACjC;EAEAC,UAAUA,CAAC1C,KAAa;IACtB,OAAO,IAAI,CAACT,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,QAAQ,cAAc,EAAE;MAAEQ;IAAK,CAAE,CAAC;EAClE;;;uBAzIWX,WAAW,EAAAsD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXzD,WAAW;MAAA0D,OAAA,EAAX1D,WAAW,CAAA2D,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}