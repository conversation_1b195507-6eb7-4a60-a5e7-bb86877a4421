{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport class PlanningService {\n  constructor(http, jwtHelper) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n  }\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getAdminHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token}`,\n      role: 'admin',\n      'Content-Type': 'application/json'\n    });\n  }\n  getAllPlannings() {\n    console.log('Service - Récupération de tous les plannings');\n    // Utiliser l'endpoint getall qui existe dans l'API\n    return this.http.get(`${environment.urlBackend}plannings/getall`, {\n      headers: this.getUserHeaders()\n    }).pipe(tap(response => {\n      console.log('Service - Plannings récupérés:', response);\n    }), catchError(error => {\n      console.error('Service - Erreur lors de la récupération des plannings:', error);\n      return throwError(() => error);\n    }));\n  }\n  getPlanningById(id) {\n    return this.http.get(`${environment.urlBackend}plannings/getone/${id}`);\n  }\n  createPlanning(planning) {\n    return this.http.post(`${environment.urlBackend}plannings/add`, planning, {\n      headers: this.getUserHeaders()\n    });\n  }\n  updatePlanning(id, planning) {\n    console.log('Service - Mise à jour du planning:', id);\n    console.log('Service - Données envoyées:', planning);\n    console.log('Service - URL:', `${environment.urlBackend}plannings/update/${id}`);\n    // Vérifier le token avant d'envoyer la requête\n    try {\n      const headers = this.getUserHeaders();\n      console.log('Service - Headers:', headers);\n      return this.http.put(`${environment.urlBackend}plannings/update/${id}`, planning, {\n        headers: headers\n      }).pipe(tap(response => {\n        console.log('Service - Réponse du serveur:', response);\n      }), catchError(error => {\n        console.error('Service - Erreur lors de la mise à jour:', error);\n        return throwError(() => error);\n      }));\n    } catch (error) {\n      console.error('Service - Erreur lors de la préparation de la requête:', error);\n      return throwError(() => new Error('Erreur d\\'authentification: ' + (error instanceof Error ? error.message : String(error))));\n    }\n  }\n  deletePlanning(id) {\n    return this.http.delete(`${environment.urlBackend}plannings/delete/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningsByUser(userId) {\n    return this.http.get(`${environment.urlBackend}plannings/user/${userId}`, {\n      headers: this.getUserHeaders()\n    }).pipe(tap(response => {\n      console.log('Service - Plannings par utilisateur récupérés:', response);\n    }), catchError(error => {\n      console.error('Service - Erreur lors de la récupération des plannings par utilisateur:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Cette méthode est remplacée par getAllPlannings qui inclut maintenant les réunions\n  getPlanningWithReunions(id) {\n    return this.http.get(`${environment.urlBackend}plannings/with-reunions/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  // Méthode pour récupérer tous les plannings (admin seulement)\n  getAllPlanningsAdmin() {\n    return this.http.get(`${environment.urlBackend}plannings/admin/all`, {\n      headers: this.getAdminHeaders()\n    }).pipe(tap(response => {\n      console.log('Service - Tous les plannings (admin) récupérés:', response);\n    }), catchError(error => {\n      console.error('Service - Erreur lors de la récupération des plannings admin:', error);\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function PlanningService_Factory(t) {\n      return new (t || PlanningService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PlanningService,\n      factory: PlanningService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "tap", "catchError", "environment", "PlanningService", "constructor", "http", "jwtHelper", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getAdminHeaders", "role", "getAllPlannings", "console", "log", "get", "urlBackend", "headers", "pipe", "response", "error", "getPlanningById", "id", "createPlanning", "planning", "post", "updatePlanning", "put", "message", "String", "deletePlanning", "delete", "getPlanningsByUser", "userId", "getPlanningWithReunions", "getAllPlanningsAdmin", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\planning.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpHeaders, HttpClient, HttpResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { tap, catchError } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Planning, CreatePlanningRequest } from '../models/planning.model';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PlanningService {\r\n\r\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService)\r\n  {}\r\n   private getUserHeaders(): HttpHeaders {\r\n     const token = localStorage.getItem('token');\r\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n       throw new Error('Token invalide ou expiré');\r\n     }\r\n     return new HttpHeaders({\r\n       Authorization: `Bearer ${token || ''}`,\r\n       'Content-Type': 'application/json',\r\n     });\r\n   }\r\n\r\n   private getAdminHeaders(): HttpHeaders {\r\n     const token = localStorage.getItem('token');\r\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n       throw new Error('Token invalide ou expiré');\r\n     }\r\n     return new HttpHeaders({\r\n       Authorization: `Bearer ${token}`,\r\n       role: 'admin',\r\n       'Content-Type': 'application/json',\r\n     });\r\n   }\r\n\r\n  getAllPlannings(): Observable<any> {\r\n    console.log('Service - Récupération de tous les plannings');\r\n    // Utiliser l'endpoint getall qui existe dans l'API\r\n    return this.http.get<any>(\r\n      `${environment.urlBackend}plannings/getall`,\r\n      { headers: this.getUserHeaders() }\r\n    ).pipe(\r\n      tap(response => {\r\n        console.log('Service - Plannings récupérés:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Service - Erreur lors de la récupération des plannings:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  getPlanningById(id: string): Observable<Planning> {\r\n    return this.http.get<Planning>(`${environment.urlBackend}plannings/getone/${id}`);\r\n  }\r\n\r\n  createPlanning(planning: CreatePlanningRequest): Observable<Planning> {\r\n    return this.http.post<Planning>(`${environment.urlBackend}plannings/add`,planning,{headers: this.getUserHeaders()});\r\n  }\r\n\r\n  updatePlanning(id: string, planning: any): Observable<any> {\r\n    console.log('Service - Mise à jour du planning:', id);\r\n    console.log('Service - Données envoyées:', planning);\r\n    console.log('Service - URL:', `${environment.urlBackend}plannings/update/${id}`);\r\n\r\n    // Vérifier le token avant d'envoyer la requête\r\n    try {\r\n      const headers = this.getUserHeaders();\r\n      console.log('Service - Headers:', headers);\r\n\r\n      return this.http.put<any>(\r\n        `${environment.urlBackend}plannings/update/${id}`,\r\n        planning,\r\n        {headers: headers}\r\n      ).pipe(\r\n        tap(response => {\r\n          console.log('Service - Réponse du serveur:', response);\r\n        }),\r\n        catchError(error => {\r\n          console.error('Service - Erreur lors de la mise à jour:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n    } catch (error) {\r\n      console.error('Service - Erreur lors de la préparation de la requête:', error);\r\n      return throwError(() => new Error('Erreur d\\'authentification: ' + (error instanceof Error ? error.message : String(error))));\r\n    }\r\n  }\r\n\r\n  deletePlanning(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${environment.urlBackend}plannings/delete/${id}`,{headers: this.getUserHeaders()});\r\n  }\r\n\r\n\r\ngetPlanningsByUser(userId: string): Observable<any> {\r\n  return this.http.get<any>(\r\n    `${environment.urlBackend}plannings/user/${userId}`,\r\n    {\r\n      headers: this.getUserHeaders()\r\n    }\r\n  ).pipe(\r\n    tap(response => {\r\n      console.log('Service - Plannings par utilisateur récupérés:', response);\r\n    }),\r\n    catchError(error => {\r\n      console.error('Service - Erreur lors de la récupération des plannings par utilisateur:', error);\r\n      return throwError(() => error);\r\n    })\r\n  );\r\n}\r\n// Cette méthode est remplacée par getAllPlannings qui inclut maintenant les réunions\r\n\r\ngetPlanningWithReunions(id: string): Observable<Planning> {\r\n  return this.http.get<Planning>(\r\n    `${environment.urlBackend}plannings/with-reunions/${id}`,\r\n    { headers: this.getUserHeaders() }\r\n  );\r\n}\r\n\r\n// Méthode pour récupérer tous les plannings (admin seulement)\r\ngetAllPlanningsAdmin(): Observable<any> {\r\n  return this.http.get<any>(\r\n    `${environment.urlBackend}plannings/admin/all`,\r\n    { headers: this.getAdminHeaders() }\r\n  ).pipe(\r\n    tap(response => {\r\n      console.log('Service - Tous les plannings (admin) récupérés:', response);\r\n    }),\r\n    catchError(error => {\r\n      console.error('Service - Erreur lors de la récupération des plannings admin:', error);\r\n      return throwError(() => error);\r\n    })\r\n  );\r\n}\r\n}"], "mappings": "AACA,SAASA,WAAW,QAAkC,sBAAsB;AAC5E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;;AAO1D,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EACtD;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAId,WAAW,CAAC;MACrBe,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEQM,eAAeA,CAAA;IACrB,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAId,WAAW,CAAC;MACrBe,aAAa,EAAE,UAAUL,KAAK,EAAE;MAChCO,IAAI,EAAE,OAAO;MACb,cAAc,EAAE;KACjB,CAAC;EACJ;EAEDC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D;IACA,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAClB,GAAGjB,WAAW,CAACkB,UAAU,kBAAkB,EAC3C;MAAEC,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAE,CACnC,CAACe,IAAI,CACJtB,GAAG,CAACuB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAAC;IACzD,CAAC,CAAC,EACFtB,UAAU,CAACuB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAC,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACrB,IAAI,CAACc,GAAG,CAAW,GAAGjB,WAAW,CAACkB,UAAU,oBAAoBM,EAAE,EAAE,CAAC;EACnF;EAEAC,cAAcA,CAACC,QAA+B;IAC5C,OAAO,IAAI,CAACvB,IAAI,CAACwB,IAAI,CAAW,GAAG3B,WAAW,CAACkB,UAAU,eAAe,EAACQ,QAAQ,EAAC;MAACP,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EACrH;EAEAuB,cAAcA,CAACJ,EAAU,EAAEE,QAAa;IACtCX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,EAAE,CAAC;IACrDT,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,QAAQ,CAAC;IACpDX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,GAAGhB,WAAW,CAACkB,UAAU,oBAAoBM,EAAE,EAAE,CAAC;IAEhF;IACA,IAAI;MACF,MAAML,OAAO,GAAG,IAAI,CAACd,cAAc,EAAE;MACrCU,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEG,OAAO,CAAC;MAE1C,OAAO,IAAI,CAAChB,IAAI,CAAC0B,GAAG,CAClB,GAAG7B,WAAW,CAACkB,UAAU,oBAAoBM,EAAE,EAAE,EACjDE,QAAQ,EACR;QAACP,OAAO,EAAEA;MAAO,CAAC,CACnB,CAACC,IAAI,CACJtB,GAAG,CAACuB,QAAQ,IAAG;QACbN,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEK,QAAQ,CAAC;MACxD,CAAC,CAAC,EACFtB,UAAU,CAACuB,KAAK,IAAG;QACjBP,OAAO,CAACO,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;KACF,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;MAC9E,OAAOzB,UAAU,CAAC,MAAM,IAAIa,KAAK,CAAC,8BAA8B,IAAIY,KAAK,YAAYZ,KAAK,GAAGY,KAAK,CAACQ,OAAO,GAAGC,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEjI;EAEAU,cAAcA,CAACR,EAAU;IACvB,OAAO,IAAI,CAACrB,IAAI,CAAC8B,MAAM,CAAO,GAAGjC,WAAW,CAACkB,UAAU,oBAAoBM,EAAE,EAAE,EAAC;MAACL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EACnH;EAGF6B,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAAChC,IAAI,CAACc,GAAG,CAClB,GAAGjB,WAAW,CAACkB,UAAU,kBAAkBiB,MAAM,EAAE,EACnD;MACEhB,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CACF,CAACe,IAAI,CACJtB,GAAG,CAACuB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEK,QAAQ,CAAC;IACzE,CAAC,CAAC,EACFtB,UAAU,CAACuB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,yEAAyE,EAAEA,KAAK,CAAC;MAC/F,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EACA;EAEAc,uBAAuBA,CAACZ,EAAU;IAChC,OAAO,IAAI,CAACrB,IAAI,CAACc,GAAG,CAClB,GAAGjB,WAAW,CAACkB,UAAU,2BAA2BM,EAAE,EAAE,EACxD;MAAEL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAE,CACnC;EACH;EAEA;EACAgC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAClC,IAAI,CAACc,GAAG,CAClB,GAAGjB,WAAW,CAACkB,UAAU,qBAAqB,EAC9C;MAAEC,OAAO,EAAE,IAAI,CAACP,eAAe;IAAE,CAAE,CACpC,CAACQ,IAAI,CACJtB,GAAG,CAACuB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,QAAQ,CAAC;IAC1E,CAAC,CAAC,EACFtB,UAAU,CAACuB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;MACrF,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;;;uBA7HarB,eAAe,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAf1C,eAAe;MAAA2C,OAAA,EAAf3C,eAAe,CAAA4C,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}