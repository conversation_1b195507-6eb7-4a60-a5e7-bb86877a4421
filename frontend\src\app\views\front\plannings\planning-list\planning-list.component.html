<div class="container mx-auto px-4 py-6">
    <!-- En-tête avec animation -->
    <div class="flex justify-between items-center mb-6" [@fadeInDown]>
        <h1 class="text-2xl font-bold text-gray-800 relative planning-header">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500">Mes Plannings</span>
            <span class="underline-animation"></span>
        </h1>
        <a routerLink="/plannings/nouveau"
           class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-md hover:from-purple-700 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg add-button">
            <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Nouveau Planning
            </span>
        </a>
    </div>

    <!-- Chargement -->
    <div *ngIf="loading" class="text-center py-12">
        <div class="relative mx-auto w-20 h-20">
            <div class="absolute top-0 left-0 w-full h-full border-4 border-purple-200 rounded-full"></div>
            <div class="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-purple-600 rounded-full animate-spin"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-purple-600 font-semibold">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        </div>
        <p class="mt-4 text-gray-600 animate-pulse">Chargement des plannings...</p>
    </div>



    <!-- Liste vide -->
    <div *ngIf="!loading && plannings.length === 0" class="text-center py-12 bg-white rounded-lg shadow-md">
        <svg class="mx-auto h-16 w-16 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 class="mt-4 text-xl font-medium text-gray-900">Aucun planning disponible</h3>
        <p class="mt-2 text-gray-600">Créez votre premier planning pour commencer à organiser vos réunions.</p>
        <a routerLink="/plannings/nouveau"
           class="mt-6 inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-all duration-300 transform hover:scale-105">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Créer un planning
        </a>
    </div>



    <!-- Liste des plannings avec animation professionnelle -->
    <div *ngIf="!loading && plannings.length > 0"
         class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
         [@staggerAnimation]="plannings.length">
        <div *ngFor="let planning of plannings; let i = index; trackBy: trackByFn"
             [@cardHover]="getCardState(i)"
             (mouseenter)="onMouseEnter(i)"
             (mouseleave)="onMouseLeave()"
             class="bg-white rounded-lg shadow-md p-4 cursor-pointer transform transition-all duration-300 relative">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800">
                        <a class="hover:text-purple-600 planning-title">
                            {{ planning.titre }}
                        </a>
                    </h3>
                    <p class="text-sm mt-1" [innerHTML]="(planning.description || 'Aucune description') | highlightPresence"></p>
                </div>
                <button (click)="deletePlanning(planning._id); $event.stopPropagation();"
                        class="text-red-500 hover:text-red-700 transition-colors duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>

            <div class="mt-3 flex items-center text-sm text-purple-700 font-medium">
                <svg class="h-4 w-4 mr-1 text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {{ planning.dateDebut | date:'mediumDate' }} - {{ planning.dateFin | date:'mediumDate' }}
            </div>

            <div class="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                <span class="text-sm font-medium reunion-count"
                      [ngClass]="{'text-gray-800': (planning.reunions?.length || 0) > 0, 'text-gray-400': (planning.reunions?.length || 0) === 0}">
                    <span class="flex items-center">
                        <!-- Icône de réunion (calendrier avec horloge) -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                             [ngClass]="{'text-gray-800': (planning.reunions?.length || 0) > 0, 'text-gray-400': (planning.reunions?.length || 0) === 0}">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            <circle cx="12" cy="14" r="3" stroke-width="1.5" />
                            <path stroke-linecap="round" stroke-width="1.5" d="M12 12v2h2" />
                        </svg>

                        <strong>{{ planning.reunions?.length || 0 }}</strong>&nbsp;réunion(s)
                    </span>
                </span>
                <a (click)="GotoDetail(planning._id)"
                   class="text-sm hover:text-purple-900 font-medium details-link"
                   style="color: #6b46c1 !important;">
                    Voir détails →
                </a>
            </div>
        </div>
    </div>


</div>