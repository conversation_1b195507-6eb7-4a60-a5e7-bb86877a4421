{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../call-interface/call-interface.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 47);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵelement(4, \"div\", 50)(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_23_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      ctx_r15.toggleSearch();\n      return i0.ɵɵresetView(ctx_r15.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 56);\n    i0.ɵɵelementStart(4, \"span\", 57);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 58);\n    i0.ɵɵelement(7, \"i\", 59);\n    i0.ɵɵelementStart(8, \"span\", 57);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 60);\n    i0.ɵɵelementStart(11, \"button\", 58);\n    i0.ɵɵelement(12, \"i\", 61);\n    i0.ɵɵelementStart(13, \"span\", 57);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementStart(3, \"p\", 65);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 66);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelement(1, \"div\", 68);\n    i0.ɵɵelementStart(2, \"span\", 69);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 73);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"span\", 89);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.formatDateSeparator(message_r19.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"img\", 91);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.openUserProfile(message_r19.sender == null ? null : message_r19.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r19.sender == null ? null : message_r19.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r19.sender == null ? null : message_r19.sender.username);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r23.getUserColor(message_r19.sender == null ? null : message_r19.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r19.sender == null ? null : message_r19.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"div\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r24.formatMessageContent(message_r19.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 98);\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r34.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r34.formatMessageContent(message_r19.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"img\", 96);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.openImageViewer(message_r19));\n    })(\"load\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.onImageLoad($event, message_r19));\n    })(\"error\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const message_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.onImageError($event, message_r19));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r25.getImageUrl(message_r19), i0.ɵɵsanitizeUrl)(\"alt\", message_r19.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.content);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 104);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 106);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 107);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template, 1, 0, \"i\", 100);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template, 1, 0, \"i\", 101);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template, 1, 0, \"i\", 102);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template, 1, 0, \"i\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_1_Template, 4, 1, \"div\", 78);\n    i0.ɵɵelementStart(2, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const message_r19 = restoredCtx.$implicit;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.onMessageClick(message_r19, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const message_r19 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.onMessageContextMenu(message_r19, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_3_Template, 2, 2, \"div\", 80);\n    i0.ɵɵelementStart(4, \"div\", 81);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_5_Template, 2, 3, \"div\", 82);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_29_ng_container_1_div_6_Template, 2, 1, \"div\", 83);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_29_ng_container_1_div_7_Template, 3, 3, \"div\", 84);\n    i0.ɵɵelementStart(8, \"div\", 85)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, MessageChatComponent_div_29_ng_container_1_div_11_Template, 5, 4, \"div\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r19 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.shouldShowDateSeparator(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r19.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r19.sender == null ? null : message_r19.sender.id) !== ctx_r17.currentUserId && ctx_r17.shouldShowAvatar(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isGroupConversation() && (message_r19.sender == null ? null : message_r19.sender.id) !== ctx_r17.currentUserId && ctx_r17.shouldShowSenderName(i_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.getMessageType(message_r19) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.hasImage(message_r19));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r17.formatMessageTime(message_r19.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r19.sender == null ? null : message_r19.sender.id) === ctx_r17.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"img\", 109);\n    i0.ɵɵelementStart(2, \"div\", 110)(3, \"div\", 111);\n    i0.ɵɵelement(4, \"div\", 112)(5, \"div\", 113)(6, \"div\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r18.otherParticipant == null ? null : ctx_r18.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r18.otherParticipant == null ? null : ctx_r18.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_Template, 12, 14, \"ng-container\", 76);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_div_2_Template, 7, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages)(\"ngForTrackBy\", ctx_r8.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_i_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 115);\n  }\n}\nfunction MessageChatComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 116);\n  }\n}\nfunction MessageChatComponent_div_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const emoji_r53 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.insertEmoji(emoji_r53));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r53 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r53.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r53.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118)(2, \"h4\", 119);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 120);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_42_button_5_Template, 2, 2, \"button\", 121);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.getEmojisForCategory(ctx_r11.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 118)(2, \"h4\", 119);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 124)(5, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 126);\n    i0.ɵɵelement(7, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 128);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 129);\n    i0.ɵɵelement(12, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 128);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 131);\n    i0.ɵɵelement(17, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 128);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 133);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, router, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.MessageService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.MessageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'VIDEO';\n    this.isInCall = true;\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    this.callType = 'AUDIO';\n    this.isInCall = true;\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n  }\n  endCall() {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    console.log('🔙 Going back to conversations');\n    // Naviguer vers la liste des conversations\n    this.router.navigate(['/front/messages/conversations']).then(() => {\n      console.log('✅ Navigation to conversations successful');\n    }).catch(error => {\n      console.error('❌ Navigation error:', error);\n      // Fallback: essayer la route parent\n      this.router.navigate(['/front/messages']).catch(() => {\n        // Dernier recours: recharger la page\n        window.location.href = '/front/messages/conversations';\n      });\n    });\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    if (!this.otherParticipant) {\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    // Utiliser le vrai service WebRTC\n    this.MessageService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ Call initiated successfully:', call);\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n      },\n      error: error => {\n        console.error('❌ Error initiating call:', error);\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.MessageService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du service\n    this.MessageService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 48,\n      vars: 46,\n      consts: [[2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid #10b981\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid #ffffff;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed #10b981;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [3, \"isVisible\", \"activeCall\", \"callType\", \"otherParticipant\", \"callEnded\", \"callAccepted\", \"callRejected\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid #ffffff\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed #10b981\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #10b981\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 15, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"main\", 23, 24);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_24_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_24_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_24_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_24_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 0, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 4, 0, \"div\", 26);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 7, 1, \"div\", 27);\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 3, 3, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"footer\", 29)(31, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_31_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(32, \"div\", 31)(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(36, \"i\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 36)(38, \"textarea\", 37);\n          i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_38_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"input\", function MessageChatComponent_Template_textarea_input_38_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_38_listener() {\n            return ctx.onInputFocus();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"button\", 38);\n          i0.ɵɵtemplate(40, MessageChatComponent_i_40_Template, 1, 0, \"i\", 39);\n          i0.ɵɵtemplate(41, MessageChatComponent_div_41_Template, 1, 0, \"div\", 40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 6, 1, \"div\", 41);\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 20, 0, \"div\", 42);\n          i0.ɵɵelementStart(44, \"input\", 43, 44);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_44_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 1, 0, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"app-call-interface\", 46);\n          i0.ɵɵlistener(\"callEnded\", function MessageChatComponent_Template_app_call_interface_callEnded_47_listener() {\n            return ctx.endCall();\n          })(\"callAccepted\", function MessageChatComponent_Template_app_call_interface_callAccepted_47_listener($event) {\n            return ctx.onCallAccepted($event);\n          })(\"callRejected\", function MessageChatComponent_Template_app_call_interface_callRejected_47_listener() {\n            return ctx.onCallRejected();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n          i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isVisible\", ctx.isInCall)(\"activeCall\", ctx.activeCall)(\"callType\", ctx.callType)(\"otherParticipant\", ctx.otherParticipant);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.CallInterfaceComponent],\n      styles: [\"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_23_Template_button_click_2_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r7", "username", "ctx_r21", "formatDateSeparator", "message_r19", "timestamp", "MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener", "_r30", "$implicit", "ctx_r28", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r23", "getUserColor", "ctx_r24", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r34", "currentUserId", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener", "_r38", "ctx_r36", "openImageViewer", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r39", "onImageLoad", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r41", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template", "ctx_r25", "getImageUrl", "MessageChatComponent_div_29_ng_container_1_div_11_i_1_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_2_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_3_Template", "MessageChatComponent_div_29_ng_container_1_div_11_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_29_ng_container_1_div_1_Template", "MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r50", "ctx_r49", "onMessageClick", "MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r51", "onMessageContextMenu", "MessageChatComponent_div_29_ng_container_1_div_3_Template", "MessageChatComponent_div_29_ng_container_1_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_6_Template", "MessageChatComponent_div_29_ng_container_1_div_7_Template", "MessageChatComponent_div_29_ng_container_1_div_11_Template", "ɵɵelementContainerEnd", "ctx_r17", "shouldShowDateSeparator", "i_r20", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r18", "MessageChatComponent_div_29_ng_container_1_Template", "MessageChatComponent_div_29_div_2_Template", "ctx_r8", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_42_button_5_Template_button_click_0_listener", "_r55", "emoji_r53", "ctx_r54", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_42_button_5_Template", "ctx_r11", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_43_Template_button_click_5_listener", "_r57", "ctx_r56", "triggerFileInput", "MessageChatComponent_div_43_Template_button_click_10_listener", "ctx_r58", "MessageChatComponent_div_43_Template_button_click_15_listener", "ctx_r59", "openCamera", "MessageChatComponent_div_46_Template_div_click_0_listener", "_r61", "ctx_r60", "closeAllMenus", "MessageChatComponent", "constructor", "fb", "route", "router", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "voiceWaves", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojiCategories", "icon", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeComponent", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "add", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "length", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "hasFile", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "startVoiceCall", "endCall", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "document", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "showSuccess", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "toggleEmojiPicker", "selectEmojiCategory", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFileAttachment", "imageAttachment", "getFileName", "getFileSize", "getFileIcon", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "closeImageViewer", "downloadImage", "searchMessages", "filter", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "initiateCall", "recipientId", "VIDEO", "startCallTimer", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "formatRecordingDuration", "onRecordStart", "showWarning", "showInfo", "onRecordEnd", "onRecordCancel", "getRecordingFormat", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "toggleVoicePlayback", "isVoicePlaying", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "addEventListener", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "getVoiceDuration", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_div_23_Template", "MessageChatComponent_Template_main_scroll_24_listener", "MessageChatComponent_Template_main_dragover_24_listener", "MessageChatComponent_Template_main_dragleave_24_listener", "MessageChatComponent_Template_main_drop_24_listener", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_form_ngSubmit_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_button_click_35_listener", "MessageChatComponent_Template_textarea_keydown_38_listener", "MessageChatComponent_Template_textarea_input_38_listener", "MessageChatComponent_Template_textarea_focus_38_listener", "MessageChatComponent_i_40_Template", "MessageChatComponent_div_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_input_change_44_listener", "MessageChatComponent_div_46_Template", "MessageChatComponent_Template_app_call_interface_callEnded_47_listener", "MessageChatComponent_Template_app_call_interface_callAccepted_47_listener", "MessageChatComponent_Template_app_call_interface_callRejected_47_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>Child,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n})\r\nexport class MessageChatComponent implements OnInit, OnDestroy {\r\n  // === RÉFÉRENCES DOM ===\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput', { static: false })\r\n  fileInput!: ElementRef<HTMLInputElement>;\r\n\r\n  // === DONNÉES PRINCIPALES ===\r\n  conversation: any = null;\r\n  messages: any[] = [];\r\n  currentUserId: string | null = null;\r\n  currentUsername = 'You';\r\n  messageForm: FormGroup;\r\n  otherParticipant: any = null;\r\n\r\n  // === ÉTATS DE L'INTERFACE ===\r\n  isLoading = false;\r\n  isLoadingMore = false;\r\n  hasMoreMessages = true;\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  showSearch = false;\r\n  searchQuery = '';\r\n  searchResults: any[] = [];\r\n  searchMode = false;\r\n  isSendingMessage = false;\r\n  otherUserIsTyping = false;\r\n  showMainMenu = false;\r\n  showMessageContextMenu = false;\r\n  selectedMessage: any = null;\r\n  contextMenuPosition = { x: 0, y: 0 };\r\n  showReactionPicker = false;\r\n  reactionPickerMessage: any = null;\r\n\r\n  showImageViewer = false;\r\n  selectedImage: any = null;\r\n  uploadProgress = 0;\r\n  isUploading = false;\r\n  isDragOver = false;\r\n\r\n  // === GESTION VOCALE OPTIMISÉE ===\r\n  isRecordingVoice = false;\r\n  voiceRecordingDuration = 0;\r\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\r\n  private mediaRecorder: MediaRecorder | null = null;\r\n  private audioChunks: Blob[] = [];\r\n  private recordingTimer: any = null;\r\n  voiceWaves: number[] = [\r\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\r\n  ];\r\n\r\n  // Lecture des messages vocaux\r\n  private currentAudio: HTMLAudioElement | null = null;\r\n  private playingMessageId: string | null = null;\r\n  private voicePlayback: {\r\n    [messageId: string]: {\r\n      progress: number;\r\n      duration: number;\r\n      currentTime: number;\r\n      speed: number;\r\n    };\r\n  } = {};\r\n\r\n  // === APPELS WEBRTC ===\r\n  isInCall = false;\r\n  callType: 'VIDEO' | 'AUDIO' | null = null;\r\n  callDuration = 0;\r\n  private callTimer: any = null;\r\n\r\n  // État de l'appel WebRTC\r\n  activeCall: any = null;\r\n  isCallConnected = false;\r\n  isMuted = false;\r\n  isVideoEnabled = true;\r\n  localVideoElement: HTMLVideoElement | null = null;\r\n  remoteVideoElement: HTMLVideoElement | null = null;\r\n\r\n  // === ÉMOJIS ===\r\n  emojiCategories: any[] = [\r\n    {\r\n      id: 'smileys',\r\n      name: 'Smileys',\r\n      icon: '😀',\r\n      emojis: [\r\n        { emoji: '😀', name: 'grinning face' },\r\n        { emoji: '😃', name: 'grinning face with big eyes' },\r\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\r\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\r\n        { emoji: '😆', name: 'grinning squinting face' },\r\n        { emoji: '😅', name: 'grinning face with sweat' },\r\n        { emoji: '😂', name: 'face with tears of joy' },\r\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\r\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\r\n        { emoji: '😇', name: 'smiling face with halo' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'people',\r\n      name: 'People',\r\n      icon: '👤',\r\n      emojis: [\r\n        { emoji: '👶', name: 'baby' },\r\n        { emoji: '🧒', name: 'child' },\r\n        { emoji: '👦', name: 'boy' },\r\n        { emoji: '👧', name: 'girl' },\r\n        { emoji: '🧑', name: 'person' },\r\n        { emoji: '👨', name: 'man' },\r\n        { emoji: '👩', name: 'woman' },\r\n        { emoji: '👴', name: 'old man' },\r\n        { emoji: '👵', name: 'old woman' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'nature',\r\n      name: 'Nature',\r\n      icon: '🌿',\r\n      emojis: [\r\n        { emoji: '🐶', name: 'dog face' },\r\n        { emoji: '🐱', name: 'cat face' },\r\n        { emoji: '🐭', name: 'mouse face' },\r\n        { emoji: '🐹', name: 'hamster' },\r\n        { emoji: '🐰', name: 'rabbit face' },\r\n        { emoji: '🦊', name: 'fox' },\r\n        { emoji: '🐻', name: 'bear' },\r\n        { emoji: '🐼', name: 'panda' },\r\n      ],\r\n    },\r\n  ];\r\n  selectedEmojiCategory = this.emojiCategories[0];\r\n\r\n  // === PAGINATION ===\r\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\r\n  private currentPage = 1;\r\n\r\n  // === AUTRES ÉTATS ===\r\n  isTyping = false;\r\n  isUserTyping = false;\r\n  private typingTimeout: any = null;\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private MessageService: MessageService,\r\n    private toastService: ToastService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.messageForm = this.fb.group({\r\n      content: ['', [Validators.required, Validators.minLength(1)]],\r\n    });\r\n  }\r\n\r\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\r\n  isInputDisabled(): boolean {\r\n    return (\r\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\r\n    );\r\n  }\r\n\r\n  // Méthode pour gérer l'état du contrôle de saisie\r\n  private updateInputState(): void {\r\n    const contentControl = this.messageForm.get('content');\r\n    if (this.isInputDisabled()) {\r\n      contentControl?.disable();\r\n    } else {\r\n      contentControl?.enable();\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('🚀 MessageChatComponent initialized');\r\n    this.initializeComponent();\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    this.loadCurrentUser();\r\n    this.loadConversation();\r\n    this.setupCallSubscriptions();\r\n  }\r\n\r\n  private setupCallSubscriptions(): void {\r\n    // S'abonner aux appels entrants\r\n    this.subscriptions.add(\r\n      this.MessageService.incomingCall$.subscribe({\r\n        next: (incomingCall) => {\r\n          if (incomingCall) {\r\n            console.log('📞 Incoming call received:', incomingCall);\r\n            this.handleIncomingCall(incomingCall);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in incoming call subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // S'abonner aux changements d'état d'appel\r\n    this.subscriptions.add(\r\n      this.MessageService.activeCall$.subscribe({\r\n        next: (call) => {\r\n          if (call) {\r\n            console.log('📞 Active call updated:', call);\r\n            this.activeCall = call;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in active call subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private handleIncomingCall(incomingCall: IncomingCall): void {\r\n    // Afficher une notification ou modal d'appel entrant\r\n    // Pour l'instant, on log juste\r\n    console.log(\r\n      '🔔 Handling incoming call from:',\r\n      incomingCall.caller.username\r\n    );\r\n\r\n    // Jouer la sonnerie\r\n    this.MessageService.play('ringtone');\r\n\r\n    // Ici on pourrait afficher une modal ou notification\r\n    // Pour l'instant, on accepte automatiquement pour tester\r\n    // this.acceptCall(incomingCall);\r\n  }\r\n\r\n  private loadCurrentUser(): void {\r\n    try {\r\n      const userString = localStorage.getItem('user');\r\n      console.log('🔍 Raw user from localStorage:', userString);\r\n\r\n      if (!userString || userString === 'null' || userString === 'undefined') {\r\n        console.error('❌ No user data in localStorage');\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n        return;\r\n      }\r\n\r\n      const user = JSON.parse(userString);\r\n      console.log('🔍 Parsed user object:', user);\r\n\r\n      // Essayer différentes propriétés pour l'ID utilisateur\r\n      const userId = user._id || user.id || user.userId;\r\n      console.log('🔍 Trying to extract user ID:', {\r\n        _id: user._id,\r\n        id: user.id,\r\n        userId: user.userId,\r\n        extracted: userId,\r\n      });\r\n\r\n      if (userId) {\r\n        this.currentUserId = userId;\r\n        this.currentUsername = user.username || user.name || 'You';\r\n        console.log('✅ Current user loaded successfully:', {\r\n          id: this.currentUserId,\r\n          username: this.currentUsername,\r\n        });\r\n      } else {\r\n        console.error('❌ No valid user ID found in user object:', user);\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error parsing user from localStorage:', error);\r\n      this.currentUserId = null;\r\n      this.currentUsername = 'You';\r\n    }\r\n  }\r\n\r\n  private loadConversation(): void {\r\n    const conversationId = this.route.snapshot.paramMap.get('id');\r\n    console.log('Loading conversation with ID:', conversationId);\r\n\r\n    if (!conversationId) {\r\n      this.toastService.showError('ID de conversation manquant');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.MessageService.getConversation(conversationId).subscribe({\r\n      next: (conversation) => {\r\n        console.log('🔍 Conversation loaded successfully:', conversation);\r\n        console.log('🔍 Conversation structure:', {\r\n          id: conversation?.id,\r\n          participants: conversation?.participants,\r\n          participantsCount: conversation?.participants?.length,\r\n          isGroup: conversation?.isGroup,\r\n          messages: conversation?.messages,\r\n          messagesCount: conversation?.messages?.length,\r\n        });\r\n        this.conversation = conversation;\r\n        this.setOtherParticipant();\r\n        this.loadMessages();\r\n\r\n        // Configurer les subscriptions temps réel après le chargement de la conversation\r\n        this.setupSubscriptions();\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement de la conversation:', error);\r\n        this.toastService.showError(\r\n          'Erreur lors du chargement de la conversation'\r\n        );\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  private setOtherParticipant(): void {\r\n    if (\r\n      !this.conversation?.participants ||\r\n      this.conversation.participants.length === 0\r\n    ) {\r\n      console.warn('No participants found in conversation');\r\n      this.otherParticipant = null;\r\n      return;\r\n    }\r\n\r\n    console.log('Setting other participant...');\r\n    console.log('Current user ID:', this.currentUserId);\r\n    console.log('All participants:', this.conversation.participants);\r\n\r\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\r\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\r\n\r\n    if (this.conversation.isGroup) {\r\n      // Pour les groupes, on pourrait afficher le nom du groupe\r\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    } else {\r\n      // Pour les conversations 1-à-1, on prend l'autre participant\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        console.log(\r\n          'Comparing participant ID:',\r\n          participantId,\r\n          'with current user ID:',\r\n          this.currentUserId\r\n        );\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    }\r\n\r\n    // Fallback si aucun autre participant n'est trouvé\r\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\r\n      console.log('Fallback: using first participant');\r\n      this.otherParticipant = this.conversation.participants[0];\r\n\r\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\r\n      if (this.conversation.participants.length > 1) {\r\n        const firstParticipantId =\r\n          this.otherParticipant.id || this.otherParticipant._id;\r\n        if (String(firstParticipantId) === String(this.currentUserId)) {\r\n          console.log(\r\n            'First participant is current user, using second participant'\r\n          );\r\n          this.otherParticipant = this.conversation.participants[1];\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vérification finale et logs\r\n    if (this.otherParticipant) {\r\n      console.log('✅ Other participant set successfully:', {\r\n        id: this.otherParticipant.id || this.otherParticipant._id,\r\n        username: this.otherParticipant.username,\r\n        image: this.otherParticipant.image,\r\n        isOnline: this.otherParticipant.isOnline,\r\n      });\r\n\r\n      // Log très visible pour debug\r\n      console.log(\r\n        '🎯 FINAL RESULT: otherParticipant =',\r\n        this.otherParticipant.username\r\n      );\r\n      console.log(\r\n        '🎯 Should display in sidebar:',\r\n        this.otherParticipant.username\r\n      );\r\n    } else {\r\n      console.error('❌ No other participant found! This should not happen.');\r\n      console.log('Conversation participants:', this.conversation.participants);\r\n      console.log('Current user ID:', this.currentUserId);\r\n\r\n      // Log très visible pour debug\r\n      console.log('🚨 ERROR: No otherParticipant found!');\r\n    }\r\n\r\n    // Mettre à jour l'état du champ de saisie\r\n    this.updateInputState();\r\n  }\r\n\r\n  private loadMessages(): void {\r\n    if (!this.conversation?.id) return;\r\n\r\n    // Les messages sont déjà chargés avec la conversation\r\n    let messages = this.conversation.messages || [];\r\n\r\n    // Trier les messages par timestamp (plus anciens en premier)\r\n    this.messages = messages.sort((a: any, b: any) => {\r\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\r\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\r\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\r\n    });\r\n\r\n    console.log('📋 Messages loaded and sorted:', {\r\n      total: this.messages.length,\r\n      first: this.messages[0]?.content,\r\n      last: this.messages[this.messages.length - 1]?.content,\r\n    });\r\n\r\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\r\n    this.isLoading = false;\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  loadMoreMessages(): void {\r\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\r\n      return;\r\n\r\n    this.isLoadingMore = true;\r\n    this.currentPage++;\r\n\r\n    // Calculer l'offset basé sur les messages déjà chargés\r\n    const offset = this.messages.length;\r\n\r\n    this.MessageService.getMessages(\r\n      this.currentUserId!, // senderId\r\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\r\n      this.conversation.id,\r\n      this.currentPage,\r\n      this.MAX_MESSAGES_TO_LOAD\r\n    ).subscribe({\r\n      next: (newMessages: any[]) => {\r\n        if (newMessages && newMessages.length > 0) {\r\n          // Ajouter les nouveaux messages au début de la liste\r\n          this.messages = [...newMessages.reverse(), ...this.messages];\r\n          this.hasMoreMessages =\r\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\r\n        } else {\r\n          this.hasMoreMessages = false;\r\n        }\r\n        this.isLoadingMore = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des messages:', error);\r\n        this.toastService.showError('Erreur lors du chargement des messages');\r\n        this.isLoadingMore = false;\r\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\r\n      },\r\n    });\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    if (!this.conversation?.id) {\r\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      '🔄 Setting up real-time subscriptions for conversation:',\r\n      this.conversation.id\r\n    );\r\n\r\n    // Subscription pour les nouveaux messages\r\n    console.log('📨 Setting up message subscription...');\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToNewMessages(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (newMessage: any) => {\r\n          console.log('📨 New message received via subscription:', newMessage);\r\n          console.log('📨 Message structure:', {\r\n            id: newMessage.id,\r\n            type: newMessage.type,\r\n            content: newMessage.content,\r\n            sender: newMessage.sender,\r\n            senderId: newMessage.senderId,\r\n            receiverId: newMessage.receiverId,\r\n            attachments: newMessage.attachments,\r\n          });\r\n\r\n          // Debug des attachments\r\n          console.log(\r\n            '📨 [Debug] Message type detected:',\r\n            this.getMessageType(newMessage)\r\n          );\r\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\r\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\r\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\r\n          if (newMessage.attachments) {\r\n            newMessage.attachments.forEach((att: any, index: number) => {\r\n              console.log(`📨 [Debug] Attachment ${index}:`, {\r\n                type: att.type,\r\n                url: att.url,\r\n                path: att.path,\r\n                name: att.name,\r\n                size: att.size,\r\n              });\r\n            });\r\n          }\r\n\r\n          // Ajouter le message à la liste s'il n'existe pas déjà\r\n          const messageExists = this.messages.some(\r\n            (msg) => msg.id === newMessage.id\r\n          );\r\n          if (!messageExists) {\r\n            // Ajouter le nouveau message à la fin (en bas)\r\n            this.messages.push(newMessage);\r\n            console.log(\r\n              '✅ Message added to list, total messages:',\r\n              this.messages.length\r\n            );\r\n\r\n            // Forcer la détection de changements\r\n            this.cdr.detectChanges();\r\n\r\n            // Scroll vers le bas après un court délai\r\n            setTimeout(() => {\r\n              this.scrollToBottom();\r\n            }, 50);\r\n\r\n            // Marquer comme lu si ce n'est pas notre message\r\n            const senderId = newMessage.sender?.id || newMessage.senderId;\r\n            console.log('📨 Checking if message should be marked as read:', {\r\n              senderId,\r\n              currentUserId: this.currentUserId,\r\n              shouldMarkAsRead: senderId !== this.currentUserId,\r\n            });\r\n\r\n            if (senderId && senderId !== this.currentUserId) {\r\n              this.markMessageAsRead(newMessage.id);\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in message subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n    console.log('📝 Setting up typing indicator subscription...');\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToTypingIndicator(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (typingData: any) => {\r\n          console.log('📝 Typing indicator received:', typingData);\r\n\r\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\r\n          if (typingData.userId !== this.currentUserId) {\r\n            this.otherUserIsTyping = typingData.isTyping;\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in typing subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les mises à jour de conversation\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToConversationUpdates(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (conversationUpdate: any) => {\r\n          console.log('📋 Conversation update:', conversationUpdate);\r\n\r\n          // Mettre à jour la conversation si nécessaire\r\n          if (conversationUpdate.id === this.conversation.id) {\r\n            this.conversation = { ...this.conversation, ...conversationUpdate };\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in conversation subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private markMessageAsRead(messageId: string): void {\r\n    this.MessageService.markMessageAsRead(messageId).subscribe({\r\n      next: () => {\r\n        console.log('✅ Message marked as read:', messageId);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error marking message as read:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // === ENVOI DE MESSAGES ===\r\n  sendMessage(): void {\r\n    if (!this.messageForm.valid || !this.conversation?.id) return;\r\n\r\n    const content = this.messageForm.get('content')?.value?.trim();\r\n    if (!content) return;\r\n\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Désactiver le bouton d'envoi\r\n    this.isSendingMessage = true;\r\n    this.updateInputState();\r\n\r\n    console.log('📤 Sending message:', {\r\n      content,\r\n      receiverId,\r\n      conversationId: this.conversation.id,\r\n    });\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      content,\r\n      undefined,\r\n      'TEXT' as any,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        console.log('✅ Message sent successfully:', message);\r\n\r\n        // Ajouter le message à la liste s'il n'y est pas déjà\r\n        const messageExists = this.messages.some(\r\n          (msg) => msg.id === message.id\r\n        );\r\n        if (!messageExists) {\r\n          this.messages.push(message);\r\n          console.log(\r\n            '📋 Message added to local list, total:',\r\n            this.messages.length\r\n          );\r\n        }\r\n\r\n        // Réinitialiser le formulaire\r\n        this.messageForm.reset();\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n\r\n        // Forcer la détection de changements et scroll\r\n        this.cdr.detectChanges();\r\n        setTimeout(() => {\r\n          this.scrollToBottom();\r\n        }, 50);\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n      },\r\n    });\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    setTimeout(() => {\r\n      if (this.messagesContainer) {\r\n        const element = this.messagesContainer.nativeElement;\r\n        element.scrollTop = element.scrollHeight;\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\r\n  formatLastActive(lastActive: string | Date | null): string {\r\n    if (!lastActive) return 'Hors ligne';\r\n\r\n    const diffMins = Math.floor(\r\n      (Date.now() - new Date(lastActive).getTime()) / 60000\r\n    );\r\n\r\n    if (diffMins < 1) return \"À l'instant\";\r\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\r\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\r\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\r\n  }\r\n\r\n  // Méthodes utilitaires pour les messages vocaux\r\n  getVoicePlaybackData(messageId: string) {\r\n    return (\r\n      this.voicePlayback[messageId] || {\r\n        progress: 0,\r\n        duration: 0,\r\n        currentTime: 0,\r\n        speed: 1,\r\n      }\r\n    );\r\n  }\r\n\r\n  private setVoicePlaybackData(\r\n    messageId: string,\r\n    data: Partial<(typeof this.voicePlayback)[string]>\r\n  ) {\r\n    this.voicePlayback[messageId] = {\r\n      ...this.getVoicePlaybackData(messageId),\r\n      ...data,\r\n    };\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\r\n\r\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\r\n\r\n  startVideoCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    this.callType = 'VIDEO';\r\n    this.isInCall = true;\r\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\r\n  }\r\n\r\n  startVoiceCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    this.callType = 'AUDIO';\r\n    this.isInCall = true;\r\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\r\n  }\r\n\r\n  endCall(): void {\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.activeCall = null;\r\n    console.log('📞 Call ended');\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // onCallAccepted, onCallRejected - définies plus loin\r\n\r\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\r\n\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  downloadFile(message: any): void {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (fileAttachment?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = fileAttachment.url;\r\n      link.download = fileAttachment.name || 'file';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\r\n\r\n  toggleSearch(): void {\r\n    this.searchMode = !this.searchMode;\r\n    this.showSearch = this.searchMode;\r\n  }\r\n\r\n  toggleMainMenu(): void {\r\n    this.showMainMenu = !this.showMainMenu;\r\n  }\r\n\r\n  goBackToConversations(): void {\r\n    console.log('🔙 Going back to conversations');\r\n    // Naviguer vers la liste des conversations\r\n    this.router\r\n      .navigate(['/front/messages/conversations'])\r\n      .then(() => {\r\n        console.log('✅ Navigation to conversations successful');\r\n      })\r\n      .catch((error) => {\r\n        console.error('❌ Navigation error:', error);\r\n        // Fallback: essayer la route parent\r\n        this.router.navigate(['/front/messages']).catch(() => {\r\n          // Dernier recours: recharger la page\r\n          window.location.href = '/front/messages/conversations';\r\n        });\r\n      });\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\r\n\r\n  closeAllMenus(): void {\r\n    this.showEmojiPicker = false;\r\n    this.showAttachmentMenu = false;\r\n    this.showMainMenu = false;\r\n    this.showMessageContextMenu = false;\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  onMessageContextMenu(message: any, event: MouseEvent): void {\r\n    event.preventDefault();\r\n    this.selectedMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showMessageContextMenu = true;\r\n  }\r\n\r\n  showQuickReactions(message: any, event: MouseEvent): void {\r\n    event.stopPropagation();\r\n    this.reactionPickerMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showReactionPicker = true;\r\n  }\r\n\r\n  quickReact(emoji: string): void {\r\n    if (this.reactionPickerMessage) {\r\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\r\n    }\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  toggleReaction(messageId: string, emoji: string): void {\r\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\r\n    // Implémentation de la réaction\r\n  }\r\n\r\n  hasUserReacted(reaction: any, userId: string): boolean {\r\n    return reaction.userId === userId;\r\n  }\r\n\r\n  replyToMessage(message: any): void {\r\n    console.log('↩️ Replying to message:', message.id);\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  forwardMessage(message: any): void {\r\n    console.log('➡️ Forwarding message:', message.id);\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  deleteMessage(message: any): void {\r\n    console.log('🗑️ Deleting message:', message.id);\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  selectEmojiCategory(category: any): void {\r\n    this.selectedEmojiCategory = category;\r\n  }\r\n\r\n  getEmojisForCategory(category: any): any[] {\r\n    return category?.emojis || [];\r\n  }\r\n\r\n  insertEmoji(emoji: any): void {\r\n    const currentContent = this.messageForm.get('content')?.value || '';\r\n    const newContent = currentContent + emoji.emoji;\r\n    this.messageForm.patchValue({ content: newContent });\r\n    this.showEmojiPicker = false;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // handleTypingIndicator - définie plus loin\r\n\r\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\r\n\r\n  trackByMessageId(index: number, message: any): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  testAddMessage(): void {\r\n    console.log('🧪 Test: Adding message');\r\n    const testMessage = {\r\n      id: `test-${Date.now()}`,\r\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\r\n      timestamp: new Date().toISOString(),\r\n      sender: {\r\n        id: this.otherParticipant?.id || 'test-user',\r\n        username: this.otherParticipant?.username || 'Test User',\r\n        image:\r\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\r\n      },\r\n      type: 'TEXT',\r\n      isRead: false,\r\n    };\r\n    this.messages.push(testMessage);\r\n    this.cdr.detectChanges();\r\n    setTimeout(() => this.scrollToBottom(), 50);\r\n  }\r\n\r\n  isGroupConversation(): boolean {\r\n    return (\r\n      this.conversation?.isGroup ||\r\n      this.conversation?.participants?.length > 2 ||\r\n      false\r\n    );\r\n  }\r\n\r\n  openCamera(): void {\r\n    console.log('📷 Opening camera');\r\n    this.showAttachmentMenu = false;\r\n    // TODO: Implémenter l'ouverture de la caméra\r\n  }\r\n\r\n  zoomImage(factor: number): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      const currentTransform = imageElement.style.transform || 'scale(1)';\r\n      const currentScale = parseFloat(\r\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\r\n      );\r\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\r\n      imageElement.style.transform = `scale(${newScale})`;\r\n      if (newScale > 1) {\r\n        imageElement.classList.add('zoomed');\r\n      } else {\r\n        imageElement.classList.remove('zoomed');\r\n      }\r\n    }\r\n  }\r\n\r\n  resetZoom(): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      imageElement.style.transform = 'scale(1)';\r\n      imageElement.classList.remove('zoomed');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\r\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  triggerFileInput(type?: string): void {\r\n    const input = this.fileInput?.nativeElement;\r\n    if (!input) {\r\n      console.error('File input element not found');\r\n      return;\r\n    }\r\n\r\n    // Configurer le type de fichier accepté\r\n    if (type === 'image') {\r\n      input.accept = 'image/*';\r\n    } else if (type === 'video') {\r\n      input.accept = 'video/*';\r\n    } else if (type === 'document') {\r\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\r\n    } else {\r\n      input.accept = '*/*';\r\n    }\r\n\r\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\r\n    input.value = '';\r\n\r\n    // Déclencher la sélection de fichier\r\n    input.click();\r\n    this.showAttachmentMenu = false;\r\n  }\r\n\r\n  formatMessageTime(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleTimeString('fr-FR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  }\r\n\r\n  formatDateSeparator(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    const today = new Date();\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    if (date.toDateString() === today.toDateString()) {\r\n      return \"Aujourd'hui\";\r\n    } else if (date.toDateString() === yesterday.toDateString()) {\r\n      return 'Hier';\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR');\r\n    }\r\n  }\r\n\r\n  formatMessageContent(content: string): string {\r\n    if (!content) return '';\r\n\r\n    // Remplacer les URLs par des liens\r\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n    return content.replace(\r\n      urlRegex,\r\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\r\n    );\r\n  }\r\n\r\n  shouldShowDateSeparator(index: number): boolean {\r\n    if (index === 0) return true;\r\n\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\r\n\r\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\r\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\r\n\r\n    return currentDate !== previousDate;\r\n  }\r\n\r\n  shouldShowAvatar(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const nextMessage = this.messages[index + 1];\r\n\r\n    if (!nextMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\r\n  }\r\n\r\n  shouldShowSenderName(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!previousMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\r\n  }\r\n\r\n  getMessageType(message: any): string {\r\n    // Vérifier d'abord le type de message explicite\r\n    if (message.type) {\r\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\r\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\r\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\r\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\r\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\r\n    }\r\n\r\n    // Ensuite vérifier les attachments\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      const attachment = message.attachments[0];\r\n      if (attachment.type?.startsWith('image/')) return 'image';\r\n      if (attachment.type?.startsWith('video/')) return 'video';\r\n      if (attachment.type?.startsWith('audio/')) return 'audio';\r\n      return 'file';\r\n    }\r\n\r\n    // Vérifier si c'est un message vocal basé sur les propriétés\r\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\r\n\r\n    return 'text';\r\n  }\r\n\r\n  hasImage(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'IMAGE' || message.type === 'image') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const hasImageAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\r\n      }) || false;\r\n\r\n    // Vérifier les propriétés directes d'image\r\n    const hasImageUrl = !!(message.imageUrl || message.image);\r\n\r\n    return hasImageAttachment || hasImageUrl;\r\n  }\r\n\r\n  hasFile(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'FILE' || message.type === 'file') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments non-image\r\n    const hasFileAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\r\n      }) || false;\r\n\r\n    return hasFileAttachment;\r\n  }\r\n\r\n  getImageUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'image\r\n    if (message.imageUrl) {\r\n      return message.imageUrl;\r\n    }\r\n    if (message.image) {\r\n      return message.image;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const imageAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\r\n    );\r\n\r\n    if (imageAttachment) {\r\n      return imageAttachment.url || imageAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getFileName(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    return fileAttachment?.name || 'Fichier';\r\n  }\r\n\r\n  getFileSize(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.size) return '';\r\n\r\n    const bytes = fileAttachment.size;\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  getFileIcon(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.type) return 'fas fa-file';\r\n\r\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\r\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  getUserColor(userId: string): string {\r\n    // Générer une couleur basée sur l'ID utilisateur\r\n    const colors = [\r\n      '#FF6B6B',\r\n      '#4ECDC4',\r\n      '#45B7D1',\r\n      '#96CEB4',\r\n      '#FFEAA7',\r\n      '#DDA0DD',\r\n      '#98D8C8',\r\n    ];\r\n    const index = userId.charCodeAt(0) % colors.length;\r\n    return colors[index];\r\n  }\r\n\r\n  // === MÉTHODES D'INTERACTION ===\r\n  onMessageClick(message: any, event: any): void {\r\n    console.log('Message clicked:', message);\r\n  }\r\n\r\n  onInputChange(event: any): void {\r\n    // Gérer les changements dans le champ de saisie\r\n    this.handleTypingIndicator();\r\n  }\r\n\r\n  onInputKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  onInputFocus(): void {\r\n    // Gérer le focus sur le champ de saisie\r\n  }\r\n\r\n  onInputBlur(): void {\r\n    // Gérer la perte de focus sur le champ de saisie\r\n  }\r\n\r\n  onScroll(event: any): void {\r\n    // Gérer le scroll pour charger plus de messages\r\n    const element = event.target;\r\n    if (\r\n      element.scrollTop === 0 &&\r\n      this.hasMoreMessages &&\r\n      !this.isLoadingMore\r\n    ) {\r\n      this.loadMoreMessages();\r\n    }\r\n  }\r\n\r\n  openUserProfile(userId: string): void {\r\n    console.log('Opening user profile for:', userId);\r\n  }\r\n\r\n  onImageLoad(event: any, message: any): void {\r\n    console.log(\r\n      '🖼️ [Debug] Image loaded successfully for message:',\r\n      message.id,\r\n      event.target.src\r\n    );\r\n  }\r\n\r\n  onImageError(event: any, message: any): void {\r\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\r\n      src: event.target.src,\r\n      error: event,\r\n    });\r\n    // Optionnel : afficher une image de remplacement\r\n    event.target.src =\r\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\r\n  }\r\n\r\n  openImageViewer(message: any): void {\r\n    const imageAttachment = message.attachments?.find((att: any) =>\r\n      att.type?.startsWith('image/')\r\n    );\r\n    if (imageAttachment?.url) {\r\n      this.selectedImage = {\r\n        url: imageAttachment.url,\r\n        name: imageAttachment.name || 'Image',\r\n        size: this.formatFileSize(imageAttachment.size || 0),\r\n        message: message,\r\n      };\r\n      this.showImageViewer = true;\r\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\r\n    }\r\n  }\r\n\r\n  closeImageViewer(): void {\r\n    this.showImageViewer = false;\r\n    this.selectedImage = null;\r\n    console.log('🖼️ [ImageViewer] Closed');\r\n  }\r\n\r\n  downloadImage(): void {\r\n    if (this.selectedImage?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = this.selectedImage.url;\r\n      link.download = this.selectedImage.name || 'image';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n      console.log(\r\n        '🖼️ [ImageViewer] Download started:',\r\n        this.selectedImage.name\r\n      );\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  searchMessages(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.searchResults = [];\r\n      return;\r\n    }\r\n\r\n    this.searchResults = this.messages.filter(\r\n      (message) =>\r\n        message.content\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase()) ||\r\n        message.sender?.username\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase())\r\n    );\r\n  }\r\n\r\n  onSearchQueryChange(): void {\r\n    this.searchMessages();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.searchResults = [];\r\n  }\r\n\r\n  jumpToMessage(messageId: string): void {\r\n    const messageElement = document.getElementById(`message-${messageId}`);\r\n    if (messageElement) {\r\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n      // Highlight temporairement le message\r\n      messageElement.classList.add('highlight');\r\n      setTimeout(() => {\r\n        messageElement.classList.remove('highlight');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  closeContextMenu(): void {\r\n    this.showMessageContextMenu = false;\r\n    this.selectedMessage = null;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\r\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // triggerFileInput - définie plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\r\n  // goBackToConversations, startVideoCall, startVoiceCall\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  private initiateCall(callType: CallType): void {\r\n    if (!this.otherParticipant) {\r\n      this.toastService.showError('Aucun destinataire sélectionné');\r\n      return;\r\n    }\r\n\r\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\r\n    if (!recipientId) {\r\n      this.toastService.showError('ID du destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    console.log(`🔄 Initiating ${callType} call to user:`, recipientId);\r\n\r\n    this.isInCall = true;\r\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n    this.callDuration = 0;\r\n\r\n    // Démarrer le timer d'appel\r\n    this.startCallTimer();\r\n\r\n    // Utiliser le vrai service WebRTC\r\n    this.MessageService.initiateCall(\r\n      recipientId,\r\n      callType,\r\n      this.conversation?.id\r\n    ).subscribe({\r\n      next: (call: Call) => {\r\n        console.log('✅ Call initiated successfully:', call);\r\n        this.activeCall = call;\r\n        this.isCallConnected = false;\r\n        this.toastService.showSuccess(\r\n          `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error initiating call:', error);\r\n        this.endCall();\r\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  acceptCall(incomingCall: IncomingCall): void {\r\n    console.log('🔄 Accepting incoming call:', incomingCall);\r\n\r\n    this.MessageService.acceptCall(incomingCall).subscribe({\r\n      next: (call: Call) => {\r\n        console.log('✅ Call accepted successfully:', call);\r\n        this.activeCall = call;\r\n        this.isInCall = true;\r\n        this.isCallConnected = true;\r\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n        this.startCallTimer();\r\n        this.toastService.showSuccess('Appel accepté');\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error accepting call:', error);\r\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  rejectCall(incomingCall: IncomingCall): void {\r\n    console.log('🔄 Rejecting incoming call:', incomingCall);\r\n\r\n    this.MessageService.rejectCall(incomingCall.id, 'User rejected').subscribe({\r\n      next: () => {\r\n        console.log('✅ Call rejected successfully');\r\n        this.toastService.showSuccess('Appel rejeté');\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error rejecting call:', error);\r\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\r\n\r\n  private startCallTimer(): void {\r\n    this.callDuration = 0;\r\n    this.callTimer = setInterval(() => {\r\n      this.callDuration++;\r\n      this.cdr.detectChanges();\r\n    }, 1000);\r\n  }\r\n\r\n  private resetCallState(): void {\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n      this.callTimer = null;\r\n    }\r\n\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.callDuration = 0;\r\n    this.activeCall = null;\r\n    this.isCallConnected = false;\r\n    this.isMuted = false;\r\n    this.isVideoEnabled = true;\r\n  }\r\n\r\n  // === CONTRÔLES D'APPEL ===\r\n  toggleMute(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isMuted = !this.isMuted;\r\n\r\n    // Utiliser la méthode toggleMedia du service\r\n    this.MessageService.toggleMedia(\r\n      this.activeCall.id,\r\n      undefined, // video unchanged\r\n      !this.isMuted // audio state\r\n    ).subscribe({\r\n      next: () => {\r\n        this.toastService.showSuccess(\r\n          this.isMuted ? 'Micro coupé' : 'Micro activé'\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error toggling mute:', error);\r\n        // Revert state on error\r\n        this.isMuted = !this.isMuted;\r\n        this.toastService.showError('Erreur lors du changement du micro');\r\n      },\r\n    });\r\n  }\r\n\r\n  toggleVideo(): void {\r\n    if (!this.activeCall) return;\r\n\r\n    this.isVideoEnabled = !this.isVideoEnabled;\r\n\r\n    // Utiliser la méthode toggleMedia du service\r\n    this.MessageService.toggleMedia(\r\n      this.activeCall.id,\r\n      this.isVideoEnabled, // video state\r\n      undefined // audio unchanged\r\n    ).subscribe({\r\n      next: () => {\r\n        this.toastService.showSuccess(\r\n          this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error toggling video:', error);\r\n        // Revert state on error\r\n        this.isVideoEnabled = !this.isVideoEnabled;\r\n        this.toastService.showError('Erreur lors du changement de la caméra');\r\n      },\r\n    });\r\n  }\r\n\r\n  formatCallDuration(duration: number): string {\r\n    const hours = Math.floor(duration / 3600);\r\n    const minutes = Math.floor((duration % 3600) / 60);\r\n    const seconds = duration % 60;\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\r\n        .toString()\r\n        .padStart(2, '0')}`;\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    console.log('🎤 [Voice] Starting voice recording...');\r\n\r\n    try {\r\n      // Vérifier le support du navigateur\r\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\r\n        throw new Error(\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\r\n        );\r\n      }\r\n\r\n      // Vérifier si MediaRecorder est supporté\r\n      if (!window.MediaRecorder) {\r\n        throw new Error(\r\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\r\n        );\r\n      }\r\n\r\n      console.log('🎤 [Voice] Requesting microphone access...');\r\n\r\n      // Demander l'accès au microphone avec des contraintes optimisées\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        audio: {\r\n          echoCancellation: true,\r\n          noiseSuppression: true,\r\n          autoGainControl: true,\r\n          sampleRate: 44100,\r\n          channelCount: 1,\r\n        },\r\n      });\r\n\r\n      console.log('🎤 [Voice] Microphone access granted');\r\n\r\n      // Vérifier les types MIME supportés\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n        mimeType = 'audio/webm';\r\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n          mimeType = 'audio/mp4';\r\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n            mimeType = ''; // Laisser le navigateur choisir\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\r\n\r\n      // Créer le MediaRecorder\r\n      this.mediaRecorder = new MediaRecorder(stream, {\r\n        mimeType: mimeType || undefined,\r\n      });\r\n\r\n      // Initialiser les variables\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = true;\r\n      this.voiceRecordingDuration = 0;\r\n      this.voiceRecordingState = 'recording';\r\n\r\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\r\n\r\n      // Démarrer le timer\r\n      this.recordingTimer = setInterval(() => {\r\n        this.voiceRecordingDuration++;\r\n        // Animer les waves\r\n        this.animateVoiceWaves();\r\n        this.cdr.detectChanges();\r\n      }, 1000);\r\n\r\n      // Gérer les événements du MediaRecorder\r\n      this.mediaRecorder.ondataavailable = (event) => {\r\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\r\n        if (event.data.size > 0) {\r\n          this.audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      this.mediaRecorder.onstop = () => {\r\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\r\n        this.processRecordedAudio();\r\n      };\r\n\r\n      this.mediaRecorder.onerror = (event: any) => {\r\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\r\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\r\n        this.cancelVoiceRecording();\r\n      };\r\n\r\n      // Démarrer l'enregistrement\r\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\r\n      console.log('🎤 [Voice] Recording started successfully');\r\n\r\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error starting recording:', error);\r\n\r\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\r\n\r\n      if (error.name === 'NotAllowedError') {\r\n        errorMessage =\r\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\r\n      } else if (error.name === 'NotFoundError') {\r\n        errorMessage =\r\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\r\n      } else if (error.name === 'NotSupportedError') {\r\n        errorMessage =\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      this.toastService.showError(errorMessage);\r\n      this.cancelVoiceRecording();\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\r\n      this.mediaRecorder.stop();\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingState = 'processing';\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n      this.mediaRecorder = null;\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingDuration = 0;\r\n    this.voiceRecordingState = 'idle';\r\n    this.audioChunks = [];\r\n  }\r\n\r\n  private async processRecordedAudio(): Promise<void> {\r\n    console.log('🎤 [Voice] Processing recorded audio...');\r\n\r\n    try {\r\n      // Vérifier qu'on a des données audio\r\n      if (this.audioChunks.length === 0) {\r\n        console.error('🎤 [Voice] No audio chunks available');\r\n        this.toastService.showError('Aucun audio enregistré');\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      console.log(\r\n        '🎤 [Voice] Audio chunks:',\r\n        this.audioChunks.length,\r\n        'Duration:',\r\n        this.voiceRecordingDuration\r\n      );\r\n\r\n      // Vérifier la durée minimale\r\n      if (this.voiceRecordingDuration < 1) {\r\n        console.error(\r\n          '🎤 [Voice] Recording too short:',\r\n          this.voiceRecordingDuration\r\n        );\r\n        this.toastService.showError(\r\n          'Enregistrement trop court (minimum 1 seconde)'\r\n        );\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      // Déterminer le type MIME du blob\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (this.mediaRecorder?.mimeType) {\r\n        mimeType = this.mediaRecorder.mimeType;\r\n      }\r\n\r\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\r\n\r\n      // Créer le blob audio\r\n      const audioBlob = new Blob(this.audioChunks, {\r\n        type: mimeType,\r\n      });\r\n\r\n      console.log('🎤 [Voice] Audio blob created:', {\r\n        size: audioBlob.size,\r\n        type: audioBlob.type,\r\n      });\r\n\r\n      // Déterminer l'extension du fichier\r\n      let extension = '.webm';\r\n      if (mimeType.includes('mp4')) {\r\n        extension = '.mp4';\r\n      } else if (mimeType.includes('wav')) {\r\n        extension = '.wav';\r\n      } else if (mimeType.includes('ogg')) {\r\n        extension = '.ogg';\r\n      }\r\n\r\n      // Créer le fichier\r\n      const audioFile = new File(\r\n        [audioBlob],\r\n        `voice_${Date.now()}${extension}`,\r\n        {\r\n          type: mimeType,\r\n        }\r\n      );\r\n\r\n      console.log('🎤 [Voice] Audio file created:', {\r\n        name: audioFile.name,\r\n        size: audioFile.size,\r\n        type: audioFile.type,\r\n      });\r\n\r\n      // Envoyer le message vocal\r\n      this.voiceRecordingState = 'processing';\r\n      await this.sendVoiceMessage(audioFile);\r\n\r\n      console.log('🎤 [Voice] Voice message sent successfully');\r\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error processing audio:', error);\r\n      this.toastService.showError(\r\n        \"Erreur lors de l'envoi du message vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    } finally {\r\n      // Nettoyer l'état\r\n      this.voiceRecordingState = 'idle';\r\n      this.voiceRecordingDuration = 0;\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = false;\r\n\r\n      console.log('🎤 [Voice] Audio processing completed, state reset');\r\n    }\r\n  }\r\n\r\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      throw new Error('Destinataire introuvable');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.MessageService.sendMessage(\r\n        receiverId,\r\n        '',\r\n        audioFile,\r\n        'AUDIO' as any,\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (message: any) => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          resolve();\r\n        },\r\n        error: (error: any) => {\r\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\r\n          reject(error);\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  formatRecordingDuration(duration: number): string {\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\r\n\r\n  onRecordStart(event: Event): void {\r\n    event.preventDefault();\r\n    console.log('🎤 [Voice] Record start triggered');\r\n    console.log('🎤 [Voice] Current state:', {\r\n      isRecordingVoice: this.isRecordingVoice,\r\n      voiceRecordingState: this.voiceRecordingState,\r\n      voiceRecordingDuration: this.voiceRecordingDuration,\r\n      mediaRecorder: !!this.mediaRecorder,\r\n    });\r\n\r\n    // Vérifier si on peut enregistrer\r\n    if (this.voiceRecordingState === 'processing') {\r\n      console.log('🎤 [Voice] Already processing, ignoring start');\r\n      this.toastService.showWarning('Traitement en cours...');\r\n      return;\r\n    }\r\n\r\n    if (this.isRecordingVoice) {\r\n      console.log('🎤 [Voice] Already recording, ignoring start');\r\n      this.toastService.showWarning('Enregistrement déjà en cours...');\r\n      return;\r\n    }\r\n\r\n    // Afficher un message de début\r\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\r\n\r\n    // Démarrer l'enregistrement\r\n    this.startVoiceRecording().catch((error) => {\r\n      console.error('🎤 [Voice] Failed to start recording:', error);\r\n      this.toastService.showError(\r\n        \"Impossible de démarrer l'enregistrement vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    });\r\n  }\r\n\r\n  onRecordEnd(event: Event): void {\r\n    event.preventDefault();\r\n    console.log('🎤 [Voice] Record end triggered');\r\n\r\n    if (!this.isRecordingVoice) {\r\n      console.log('🎤 [Voice] Not recording, ignoring end');\r\n      return;\r\n    }\r\n\r\n    // Arrêter l'enregistrement et envoyer\r\n    this.stopVoiceRecording();\r\n  }\r\n\r\n  onRecordCancel(event: Event): void {\r\n    event.preventDefault();\r\n    console.log('🎤 [Voice] Record cancel triggered');\r\n\r\n    if (!this.isRecordingVoice) {\r\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\r\n      return;\r\n    }\r\n\r\n    // Annuler l'enregistrement\r\n    this.cancelVoiceRecording();\r\n  }\r\n\r\n  getRecordingFormat(): string {\r\n    if (this.mediaRecorder?.mimeType) {\r\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\r\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\r\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\r\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\r\n    }\r\n    return 'Auto';\r\n  }\r\n\r\n  // === ANIMATION DES WAVES VOCALES ===\r\n\r\n  private animateVoiceWaves(): void {\r\n    // Animer les waves pendant l'enregistrement\r\n    this.voiceWaves = this.voiceWaves.map(() => {\r\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    console.log('📁 [Upload] File selection triggered');\r\n    const files = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      console.log('📁 [Upload] No files selected');\r\n      return;\r\n    }\r\n\r\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\r\n\r\n    for (let file of files) {\r\n      console.log(\r\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\r\n      );\r\n      this.uploadFile(file);\r\n    }\r\n  }\r\n\r\n  private uploadFile(file: File): void {\r\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\r\n\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      console.error('📁 [Upload] No receiver ID found');\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\r\n\r\n    // Vérifier la taille du fichier (max 50MB)\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n    if (file.size > maxSize) {\r\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\r\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\r\n      return;\r\n    }\r\n\r\n    // 🖼️ Compression d'image si nécessaire\r\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\r\n      // > 1MB\r\n      console.log(\r\n        '🖼️ [Compression] Compressing image:',\r\n        file.name,\r\n        'Original size:',\r\n        file.size\r\n      );\r\n      this.compressImage(file)\r\n        .then((compressedFile) => {\r\n          console.log(\r\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\r\n            compressedFile.size\r\n          );\r\n          this.sendFileToServer(compressedFile, receiverId);\r\n        })\r\n        .catch((error) => {\r\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\r\n          // Envoyer le fichier original en cas d'erreur\r\n          this.sendFileToServer(file, receiverId);\r\n        });\r\n      return;\r\n    }\r\n\r\n    // Envoyer le fichier sans compression\r\n    this.sendFileToServer(file, receiverId);\r\n  }\r\n\r\n  private sendFileToServer(file: File, receiverId: string): void {\r\n    const messageType = this.getFileMessageType(file);\r\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\r\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\r\n\r\n    this.isSendingMessage = true;\r\n    this.isUploading = true;\r\n    this.uploadProgress = 0;\r\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\r\n\r\n    // Simuler la progression d'upload\r\n    const progressInterval = setInterval(() => {\r\n      this.uploadProgress += Math.random() * 15;\r\n      if (this.uploadProgress >= 90) {\r\n        clearInterval(progressInterval);\r\n      }\r\n      this.cdr.detectChanges();\r\n    }, 300);\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      '',\r\n      file,\r\n      messageType,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\r\n        console.log('📁 [Debug] Sent message structure:', {\r\n          id: message.id,\r\n          type: message.type,\r\n          attachments: message.attachments,\r\n          hasImage: this.hasImage(message),\r\n          hasFile: this.hasFile(message),\r\n          imageUrl: this.getImageUrl(message),\r\n        });\r\n\r\n        clearInterval(progressInterval);\r\n        this.uploadProgress = 100;\r\n\r\n        setTimeout(() => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          this.toastService.showSuccess('Fichier envoyé avec succès');\r\n          this.resetUploadState();\r\n        }, 500);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('📁 [Upload] ❌ Error sending file:', error);\r\n        clearInterval(progressInterval);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\r\n        this.resetUploadState();\r\n      },\r\n    });\r\n  }\r\n\r\n  private getFileMessageType(file: File): any {\r\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\r\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\r\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\r\n    return 'FILE' as any;\r\n  }\r\n\r\n  getFileAcceptTypes(): string {\r\n    return '*/*';\r\n  }\r\n\r\n  resetUploadState(): void {\r\n    this.isSendingMessage = false;\r\n    this.isUploading = false;\r\n    this.uploadProgress = 0;\r\n  }\r\n\r\n  // === DRAG & DROP ===\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\r\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n    const x = event.clientX;\r\n    const y = event.clientY;\r\n\r\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n      this.isDragOver = false;\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files && files.length > 0) {\r\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\r\n\r\n      // Traiter chaque fichier\r\n      Array.from(files).forEach((file) => {\r\n        console.log(\r\n          '📁 [Drag&Drop] Processing file:',\r\n          file.name,\r\n          file.type,\r\n          file.size\r\n        );\r\n        this.uploadFile(file);\r\n      });\r\n\r\n      this.toastService.showSuccess(\r\n        `${files.length} fichier(s) en cours d'envoi`\r\n      );\r\n    }\r\n  }\r\n\r\n  // === COMPRESSION D'IMAGES ===\r\n\r\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\r\n    return new Promise((resolve, reject) => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      const img = new Image();\r\n\r\n      img.onload = () => {\r\n        // Calculer les nouvelles dimensions (max 1920x1080)\r\n        const maxWidth = 1920;\r\n        const maxHeight = 1080;\r\n        let { width, height } = img;\r\n\r\n        if (width > maxWidth || height > maxHeight) {\r\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\r\n          width *= ratio;\r\n          height *= ratio;\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Dessiner l'image redimensionnée\r\n        ctx?.drawImage(img, 0, 0, width, height);\r\n\r\n        // Convertir en blob avec compression\r\n        canvas.toBlob(\r\n          (blob) => {\r\n            if (blob) {\r\n              const compressedFile = new File([blob], file.name, {\r\n                type: file.type,\r\n                lastModified: Date.now(),\r\n              });\r\n              resolve(compressedFile);\r\n            } else {\r\n              reject(new Error('Failed to compress image'));\r\n            }\r\n          },\r\n          file.type,\r\n          quality\r\n        );\r\n      };\r\n\r\n      img.onerror = () => reject(new Error('Failed to load image'));\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  }\r\n\r\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\r\n\r\n  private handleTypingIndicator(): void {\r\n    if (!this.isTyping) {\r\n      this.isTyping = true;\r\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\r\n      this.sendTypingIndicator(true);\r\n    }\r\n\r\n    // Reset le timer\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.isTyping = false;\r\n      // Arrêter l'indicateur de frappe\r\n      this.sendTypingIndicator(false);\r\n    }, 2000);\r\n  }\r\n\r\n  private sendTypingIndicator(isTyping: boolean): void {\r\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n    if (receiverId && this.conversation?.id) {\r\n      console.log(\r\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\r\n      );\r\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\r\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\r\n\r\n  onCallAccepted(call: Call): void {\r\n    console.log('🔄 Call accepted from interface:', call);\r\n    this.activeCall = call;\r\n    this.isInCall = true;\r\n    this.isCallConnected = true;\r\n    this.startCallTimer();\r\n    this.toastService.showSuccess('Appel accepté');\r\n  }\r\n\r\n  onCallRejected(): void {\r\n    console.log('🔄 Call rejected from interface');\r\n    this.endCall();\r\n    this.toastService.showInfo('Appel rejeté');\r\n  }\r\n\r\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\r\n\r\n  playVoiceMessage(message: any): void {\r\n    console.log('🎵 [Voice] Playing voice message:', message.id);\r\n    this.toggleVoicePlayback(message);\r\n  }\r\n\r\n  isVoicePlaying(messageId: string): boolean {\r\n    return this.playingMessageId === messageId;\r\n  }\r\n\r\n  toggleVoicePlayback(message: any): void {\r\n    const messageId = message.id;\r\n    const audioUrl = this.getVoiceUrl(message);\r\n\r\n    if (!audioUrl) {\r\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\r\n      this.toastService.showError('Fichier audio introuvable');\r\n      return;\r\n    }\r\n\r\n    // Si c'est déjà en cours de lecture, arrêter\r\n    if (this.isVoicePlaying(messageId)) {\r\n      this.stopVoicePlayback();\r\n      return;\r\n    }\r\n\r\n    // Arrêter toute autre lecture en cours\r\n    this.stopVoicePlayback();\r\n\r\n    // Démarrer la nouvelle lecture\r\n    this.startVoicePlayback(message, audioUrl);\r\n  }\r\n\r\n  private startVoicePlayback(message: any, audioUrl: string): void {\r\n    const messageId = message.id;\r\n\r\n    try {\r\n      console.log(\r\n        '🎵 [Voice] Starting playback for:',\r\n        messageId,\r\n        'URL:',\r\n        audioUrl\r\n      );\r\n\r\n      this.currentAudio = new Audio(audioUrl);\r\n      this.playingMessageId = messageId;\r\n\r\n      // Initialiser les valeurs par défaut avec la nouvelle structure\r\n      const currentData = this.getVoicePlaybackData(messageId);\r\n      this.setVoicePlaybackData(messageId, {\r\n        progress: 0,\r\n        currentTime: 0,\r\n        speed: currentData.speed || 1,\r\n        duration: currentData.duration || 0,\r\n      });\r\n\r\n      // Configurer la vitesse de lecture\r\n      this.currentAudio.playbackRate = currentData.speed || 1;\r\n\r\n      // Événements audio\r\n      this.currentAudio.addEventListener('loadedmetadata', () => {\r\n        if (this.currentAudio) {\r\n          this.setVoicePlaybackData(messageId, {\r\n            duration: this.currentAudio.duration,\r\n          });\r\n          console.log(\r\n            '🎵 [Voice] Audio loaded, duration:',\r\n            this.currentAudio.duration\r\n          );\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('timeupdate', () => {\r\n        if (this.currentAudio && this.playingMessageId === messageId) {\r\n          const currentTime = this.currentAudio.currentTime;\r\n          const progress = (currentTime / this.currentAudio.duration) * 100;\r\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('ended', () => {\r\n        console.log('🎵 [Voice] Playback ended for:', messageId);\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      this.currentAudio.addEventListener('error', (error) => {\r\n        console.error('🎵 [Voice] Audio error:', error);\r\n        this.toastService.showError('Erreur lors de la lecture audio');\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      // Démarrer la lecture\r\n      this.currentAudio\r\n        .play()\r\n        .then(() => {\r\n          console.log('🎵 [Voice] Playback started successfully');\r\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\r\n        })\r\n        .catch((error) => {\r\n          console.error('🎵 [Voice] Error starting playback:', error);\r\n          this.toastService.showError('Impossible de lire le message vocal');\r\n          this.stopVoicePlayback();\r\n        });\r\n    } catch (error) {\r\n      console.error('🎵 [Voice] Error creating audio:', error);\r\n      this.toastService.showError('Erreur lors de la lecture audio');\r\n      this.stopVoicePlayback();\r\n    }\r\n  }\r\n\r\n  private stopVoicePlayback(): void {\r\n    if (this.currentAudio) {\r\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\r\n      this.currentAudio.pause();\r\n      this.currentAudio.currentTime = 0;\r\n      this.currentAudio = null;\r\n    }\r\n    this.playingMessageId = null;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getVoiceUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'audio\r\n    if (message.voiceUrl) return message.voiceUrl;\r\n    if (message.audioUrl) return message.audioUrl;\r\n    if (message.voice) return message.voice;\r\n\r\n    // Vérifier les attachments audio\r\n    const audioAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\r\n    );\r\n\r\n    if (audioAttachment) {\r\n      return audioAttachment.url || audioAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getVoiceWaves(message: any): number[] {\r\n    // Générer des waves basées sur l'ID du message pour la cohérence\r\n    const messageId = message.id || '';\r\n    const seed = messageId\r\n      .split('')\r\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\r\n    const waves: number[] = [];\r\n\r\n    for (let i = 0; i < 16; i++) {\r\n      const height = 4 + ((seed + i * 7) % 20);\r\n      waves.push(height);\r\n    }\r\n\r\n    return waves;\r\n  }\r\n\r\n  getVoiceProgress(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const totalWaves = 16;\r\n    return Math.floor((data.progress / 100) * totalWaves);\r\n  }\r\n\r\n  getVoiceCurrentTime(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return this.formatAudioTime(data.currentTime);\r\n  }\r\n\r\n  getVoiceDuration(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const duration = data.duration || message.metadata?.duration || 0;\r\n\r\n    if (typeof duration === 'string') {\r\n      return duration; // Déjà formaté\r\n    }\r\n\r\n    return this.formatAudioTime(duration);\r\n  }\r\n\r\n  private formatAudioTime(seconds: number): string {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  seekVoiceMessage(message: any, waveIndex: number): void {\r\n    const messageId = message.id;\r\n\r\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\r\n      return;\r\n    }\r\n\r\n    const totalWaves = 16;\r\n    const seekPercentage = (waveIndex / totalWaves) * 100;\r\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\r\n\r\n    this.currentAudio.currentTime = seekTime;\r\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\r\n  }\r\n\r\n  toggleVoiceSpeed(message: any): void {\r\n    const messageId = message.id;\r\n    const data = this.getVoicePlaybackData(messageId);\r\n\r\n    // Cycle entre 1x, 1.5x, 2x\r\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\r\n\r\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\r\n\r\n    if (this.currentAudio && this.playingMessageId === messageId) {\r\n      this.currentAudio.playbackRate = newSpeed;\r\n    }\r\n\r\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n\r\n    // Nettoyer les timers\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n    }\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n    }\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    // Nettoyer les ressources audio\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    // Nettoyer la lecture audio\r\n    this.stopVoicePlayback();\r\n  }\r\n}\r\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\r\n<div\r\n  style=\"\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100vh;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    color: #1f2937;\r\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\r\n  \"\r\n>\r\n  <!-- ===== ANIMATIONS CSS ===== -->\r\n  <style>\r\n    @keyframes pulse {\r\n      0%,\r\n      100% {\r\n        opacity: 1;\r\n      }\r\n      50% {\r\n        opacity: 0.5;\r\n      }\r\n    }\r\n    @keyframes bounce {\r\n      0%,\r\n      20%,\r\n      53%,\r\n      80%,\r\n      100% {\r\n        transform: translateY(0);\r\n      }\r\n      40%,\r\n      43% {\r\n        transform: translateY(-8px);\r\n      }\r\n      70% {\r\n        transform: translateY(-4px);\r\n      }\r\n    }\r\n    @keyframes spin {\r\n      from {\r\n        transform: rotate(0deg);\r\n      }\r\n      to {\r\n        transform: rotate(360deg);\r\n      }\r\n    }\r\n    @keyframes ping {\r\n      75%,\r\n      100% {\r\n        transform: scale(2);\r\n        opacity: 0;\r\n      }\r\n    }\r\n  </style>\r\n\r\n  <!-- ===== HEADER SECTION ===== -->\r\n  <header\r\n    style=\"\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 16px;\r\n      background: #ffffff;\r\n      border-bottom: 1px solid #e5e7eb;\r\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n      z-index: 10;\r\n    \"\r\n  >\r\n    <!-- Bouton retour -->\r\n    <button\r\n      (click)=\"goBackToConversations()\"\r\n      style=\"\r\n        padding: 10px;\r\n        margin-right: 12px;\r\n        border-radius: 50%;\r\n        border: none;\r\n        background: transparent;\r\n        cursor: pointer;\r\n        transition: all 0.2s ease;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        min-width: 40px;\r\n        min-height: 40px;\r\n      \"\r\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\r\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\r\n      title=\"Retour aux conversations\"\r\n    >\r\n      <i\r\n        class=\"fas fa-arrow-left\"\r\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\r\n      ></i>\r\n    </button>\r\n\r\n    <!-- Info utilisateur -->\r\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\r\n      <!-- Avatar avec statut -->\r\n      <div style=\"position: relative; margin-right: 12px\">\r\n        <img\r\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\r\n          [alt]=\"otherParticipant?.username\"\r\n          style=\"\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 50%;\r\n            object-fit: cover;\r\n            border: 2px solid #10b981;\r\n            cursor: pointer;\r\n            transition: transform 0.2s ease;\r\n          \"\r\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\r\n          onmouseover=\"this.style.transform='scale(1.05)'\"\r\n          onmouseout=\"this.style.transform='scale(1)'\"\r\n          title=\"Voir le profil\"\r\n        />\r\n        <!-- Indicateur en ligne -->\r\n        <div\r\n          *ngIf=\"otherParticipant?.isOnline\"\r\n          style=\"\r\n            position: absolute;\r\n            bottom: 0;\r\n            right: 0;\r\n            width: 12px;\r\n            height: 12px;\r\n            background: #10b981;\r\n            border: 2px solid #ffffff;\r\n            border-radius: 50%;\r\n            animation: pulse 2s infinite;\r\n          \"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- Nom et statut -->\r\n      <div style=\"flex: 1; min-width: 0\">\r\n        <h3\r\n          style=\"\r\n            font-weight: 600;\r\n            color: #111827;\r\n            margin: 0;\r\n            font-size: 16px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          \"\r\n        >\r\n          {{ otherParticipant?.username || \"Utilisateur\" }}\r\n        </h3>\r\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\r\n          <!-- Indicateur de frappe -->\r\n          <div\r\n            *ngIf=\"isUserTyping\"\r\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\r\n          >\r\n            <span>En train d'écrire</span>\r\n            <div style=\"display: flex; gap: 2px\">\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite;\r\n                \"\r\n              ></div>\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite 0.1s;\r\n                \"\r\n              ></div>\r\n              <div\r\n                style=\"\r\n                  width: 4px;\r\n                  height: 4px;\r\n                  background: #10b981;\r\n                  border-radius: 50%;\r\n                  animation: bounce 1s infinite 0.2s;\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n          <!-- Statut en ligne -->\r\n          <span *ngIf=\"!isUserTyping\">\r\n            {{\r\n              otherParticipant?.isOnline\r\n                ? \"En ligne\"\r\n                : formatLastActive(otherParticipant?.lastActive)\r\n            }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Actions -->\r\n    <div style=\"display: flex; align-items: center; gap: 8px\">\r\n      <!-- Appel vidéo -->\r\n      <button\r\n        (click)=\"startVideoCall()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        title=\"Appel vidéo\"\r\n        onmouseover=\"this.style.background='#f3f4f6'\"\r\n        onmouseout=\"this.style.background='transparent'\"\r\n      >\r\n        <i class=\"fas fa-video\"></i>\r\n      </button>\r\n\r\n      <!-- Appel vocal -->\r\n      <button\r\n        (click)=\"startVoiceCall()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        title=\"Appel vocal\"\r\n        onmouseover=\"this.style.background='#f3f4f6'\"\r\n        onmouseout=\"this.style.background='transparent'\"\r\n      >\r\n        <i class=\"fas fa-phone\"></i>\r\n      </button>\r\n\r\n      <!-- Recherche -->\r\n      <button\r\n        (click)=\"toggleSearch()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n        \"\r\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\r\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\r\n        title=\"Rechercher\"\r\n      >\r\n        <i class=\"fas fa-search\"></i>\r\n      </button>\r\n\r\n      <!-- Menu principal -->\r\n      <button\r\n        (click)=\"toggleMainMenu()\"\r\n        style=\"\r\n          padding: 8px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: transparent;\r\n          color: #6b7280;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          position: relative;\r\n        \"\r\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\r\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\r\n        title=\"Menu\"\r\n      >\r\n        <i class=\"fas fa-ellipsis-v\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Menu dropdown -->\r\n    <div\r\n      *ngIf=\"showMainMenu\"\r\n      style=\"\r\n        position: absolute;\r\n        top: 64px;\r\n        right: 16px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        min-width: 192px;\r\n      \"\r\n    >\r\n      <div style=\"padding: 8px\">\r\n        <button\r\n          (click)=\"toggleSearch(); showMainMenu = false\"\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\r\n          <span style=\"color: #374151\">Rechercher</span>\r\n        </button>\r\n        <button\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\r\n          <span style=\"color: #374151\">Voir le profil</span>\r\n        </button>\r\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\r\n        <button\r\n          style=\"\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 12px;\r\n            padding: 8px 12px;\r\n            border-radius: 8px;\r\n            border: none;\r\n            background: transparent;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n            text-align: left;\r\n          \"\r\n          onmouseover=\"this.style.background='#f3f4f6'\"\r\n          onmouseout=\"this.style.background='transparent'\"\r\n        >\r\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\r\n          <span style=\"color: #374151\">Paramètres</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\r\n  <main\r\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\r\n    #messagesContainer\r\n    (scroll)=\"onScroll($event)\"\r\n    (dragover)=\"onDragOver($event)\"\r\n    (dragleave)=\"onDragLeave($event)\"\r\n    (drop)=\"onDrop($event)\"\r\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\r\n  >\r\n    <!-- Drag & Drop Overlay -->\r\n    <div\r\n      *ngIf=\"isDragOver\"\r\n      style=\"\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: rgba(34, 197, 94, 0.2);\r\n        border: 2px dashed #10b981;\r\n        border-radius: 8px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 50;\r\n        backdrop-filter: blur(2px);\r\n        animation: pulse 2s infinite;\r\n      \"\r\n    >\r\n      <div\r\n        style=\"\r\n          text-align: center;\r\n          background: #ffffff;\r\n          padding: 24px;\r\n          border-radius: 12px;\r\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\r\n          border: 1px solid #10b981;\r\n        \"\r\n      >\r\n        <i\r\n          class=\"fas fa-cloud-upload-alt\"\r\n          style=\"\r\n            font-size: 48px;\r\n            color: #10b981;\r\n            margin-bottom: 12px;\r\n            animation: bounce 1s infinite;\r\n          \"\r\n        ></i>\r\n        <p\r\n          style=\"\r\n            font-size: 20px;\r\n            font-weight: bold;\r\n            color: #047857;\r\n            margin-bottom: 8px;\r\n          \"\r\n        >\r\n          Déposez vos fichiers ici\r\n        </p>\r\n        <p style=\"font-size: 14px; color: #10b981\">\r\n          Images, vidéos, documents...\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div\r\n      *ngIf=\"isLoading\"\r\n      style=\"\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 32px 0;\r\n      \"\r\n    >\r\n      <div\r\n        style=\"\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 2px solid #e5e7eb;\r\n          border-bottom-color: #10b981;\r\n          border-radius: 50%;\r\n          animation: spin 1s linear infinite;\r\n          margin-bottom: 16px;\r\n        \"\r\n      ></div>\r\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\r\n    </div>\r\n\r\n    <!-- Empty State -->\r\n    <div\r\n      *ngIf=\"!isLoading && messages.length === 0\"\r\n      style=\"\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 64px 0;\r\n      \"\r\n    >\r\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\r\n        <i class=\"fas fa-comments\"></i>\r\n      </div>\r\n      <h3\r\n        style=\"\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n        \"\r\n      >\r\n        Aucun message\r\n      </h3>\r\n      <p style=\"color: #6b7280; text-align: center\">\r\n        Commencez votre conversation avec {{ otherParticipant?.username }}\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Messages List -->\r\n    <div\r\n      *ngIf=\"!isLoading && messages.length > 0\"\r\n      style=\"display: flex; flex-direction: column; gap: 8px\"\r\n    >\r\n      <ng-container\r\n        *ngFor=\"\r\n          let message of messages;\r\n          let i = index;\r\n          trackBy: trackByMessageId\r\n        \"\r\n      >\r\n        <!-- Date Separator -->\r\n        <div\r\n          *ngIf=\"shouldShowDateSeparator(i)\"\r\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\r\n        >\r\n          <div\r\n            style=\"\r\n              background: #ffffff;\r\n              padding: 4px 12px;\r\n              border-radius: 20px;\r\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n            \"\r\n          >\r\n            <span style=\"font-size: 12px; color: #6b7280\">\r\n              {{ formatDateSeparator(message.timestamp) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Message Container -->\r\n        <div\r\n          style=\"display: flex\"\r\n          [style.justify-content]=\"\r\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\r\n          \"\r\n          [id]=\"'message-' + message.id\"\r\n          (click)=\"onMessageClick(message, $event)\"\r\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\r\n        >\r\n          <!-- Avatar for others -->\r\n          <div\r\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\r\n            style=\"margin-right: 8px; flex-shrink: 0\"\r\n          >\r\n            <img\r\n              [src]=\"\r\n                message.sender?.image || 'assets/images/default-avatar.png'\r\n              \"\r\n              [alt]=\"message.sender?.username\"\r\n              style=\"\r\n                width: 32px;\r\n                height: 32px;\r\n                border-radius: 50%;\r\n                object-fit: cover;\r\n                cursor: pointer;\r\n                transition: transform 0.2s;\r\n              \"\r\n              (click)=\"openUserProfile(message.sender?.id!)\"\r\n              onmouseover=\"this.style.transform='scale(1.05)'\"\r\n              onmouseout=\"this.style.transform='scale(1)'\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Message Bubble -->\r\n          <div\r\n            [style.background-color]=\"\r\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\r\n            \"\r\n            [style.color]=\"\r\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\r\n            \"\r\n            style=\"\r\n              max-width: 320px;\r\n              padding: 12px 16px;\r\n              border-radius: 18px;\r\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n              position: relative;\r\n              word-wrap: break-word;\r\n              overflow-wrap: break-word;\r\n              border: none;\r\n            \"\r\n          >\r\n            <!-- Sender Name (for groups) -->\r\n            <div\r\n              *ngIf=\"\r\n                isGroupConversation() &&\r\n                message.sender?.id !== currentUserId &&\r\n                shouldShowSenderName(i)\r\n              \"\r\n              style=\"\r\n                font-size: 12px;\r\n                font-weight: 600;\r\n                margin-bottom: 4px;\r\n                opacity: 0.75;\r\n              \"\r\n              [style.color]=\"getUserColor(message.sender?.id!)\"\r\n            >\r\n              {{ message.sender?.username }}\r\n            </div>\r\n\r\n            <!-- Text Content -->\r\n            <div\r\n              *ngIf=\"getMessageType(message) === 'text'\"\r\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\r\n            >\r\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\r\n            </div>\r\n\r\n            <!-- Image Content -->\r\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\r\n              <img\r\n                [src]=\"getImageUrl(message)\"\r\n                [alt]=\"message.content || 'Image'\"\r\n                (click)=\"openImageViewer(message)\"\r\n                (load)=\"onImageLoad($event, message)\"\r\n                (error)=\"onImageError($event, message)\"\r\n                style=\"\r\n                  max-width: 280px;\r\n                  height: auto;\r\n                  border-radius: 12px;\r\n                  cursor: pointer;\r\n                  transition: transform 0.2s;\r\n                \"\r\n                onmouseover=\"this.style.transform='scale(1.02)'\"\r\n                onmouseout=\"this.style.transform='scale(1)'\"\r\n              />\r\n              <!-- Image Caption -->\r\n              <div\r\n                *ngIf=\"message.content\"\r\n                [style.color]=\"\r\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\r\n                \"\r\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\r\n                [innerHTML]=\"formatMessageContent(message.content)\"\r\n              ></div>\r\n            </div>\r\n\r\n            <!-- Message Metadata -->\r\n            <div\r\n              style=\"\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: flex-end;\r\n                gap: 4px;\r\n                margin-top: 4px;\r\n                font-size: 12px;\r\n                opacity: 0.75;\r\n              \"\r\n            >\r\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\r\n              <div\r\n                *ngIf=\"message.sender?.id === currentUserId\"\r\n                style=\"display: flex; align-items: center\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-clock\"\r\n                  *ngIf=\"message.status === 'SENDING'\"\r\n                  title=\"Envoi en cours\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check\"\r\n                  *ngIf=\"message.status === 'SENT'\"\r\n                  title=\"Envoyé\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check-double\"\r\n                  *ngIf=\"message.status === 'DELIVERED'\"\r\n                  title=\"Livré\"\r\n                ></i>\r\n                <i\r\n                  class=\"fas fa-check-double\"\r\n                  style=\"color: #3b82f6\"\r\n                  *ngIf=\"message.status === 'READ'\"\r\n                  title=\"Lu\"\r\n                ></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <!-- Typing Indicator -->\r\n      <div\r\n        *ngIf=\"otherUserIsTyping\"\r\n        style=\"display: flex; align-items: start; gap: 8px\"\r\n      >\r\n        <img\r\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\r\n          [alt]=\"otherParticipant?.username\"\r\n          style=\"\r\n            width: 32px;\r\n            height: 32px;\r\n            border-radius: 50%;\r\n            object-fit: cover;\r\n          \"\r\n        />\r\n        <div\r\n          style=\"\r\n            background: #ffffff;\r\n            padding: 12px 16px;\r\n            border-radius: 18px;\r\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n          \"\r\n        >\r\n          <div style=\"display: flex; gap: 4px\">\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite;\r\n              \"\r\n            ></div>\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite 0.1s;\r\n              \"\r\n            ></div>\r\n            <div\r\n              style=\"\r\n                width: 8px;\r\n                height: 8px;\r\n                background: #6b7280;\r\n                border-radius: 50%;\r\n                animation: bounce 1s infinite 0.2s;\r\n              \"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </main>\r\n\r\n  <!-- ===== FOOTER INPUT SECTION ===== -->\r\n  <footer\r\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\r\n  >\r\n    <form\r\n      [formGroup]=\"messageForm\"\r\n      (ngSubmit)=\"sendMessage()\"\r\n      style=\"display: flex; align-items: end; gap: 12px\"\r\n    >\r\n      <!-- Left Actions -->\r\n      <div style=\"display: flex; gap: 8px\">\r\n        <!-- Emoji Button -->\r\n        <button\r\n          type=\"button\"\r\n          (click)=\"toggleEmojiPicker()\"\r\n          style=\"\r\n            padding: 8px;\r\n            border-radius: 50%;\r\n            border: none;\r\n            background: transparent;\r\n            color: #6b7280;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n          \"\r\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\r\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\r\n          title=\"Émojis\"\r\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\r\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\r\n        >\r\n          <i class=\"fas fa-smile\"></i>\r\n        </button>\r\n\r\n        <!-- Attachment Button -->\r\n        <button\r\n          type=\"button\"\r\n          (click)=\"toggleAttachmentMenu()\"\r\n          style=\"\r\n            padding: 8px;\r\n            border-radius: 50%;\r\n            border: none;\r\n            background: transparent;\r\n            color: #6b7280;\r\n            cursor: pointer;\r\n            transition: all 0.2s;\r\n          \"\r\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\r\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\r\n          title=\"Pièces jointes\"\r\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\r\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\r\n        >\r\n          <i class=\"fas fa-paperclip\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Message Input -->\r\n      <div style=\"flex: 1; position: relative\">\r\n        <textarea\r\n          formControlName=\"content\"\r\n          placeholder=\"Tapez votre message...\"\r\n          (keydown)=\"onInputKeyDown($event)\"\r\n          (input)=\"onInputChange($event)\"\r\n          (focus)=\"onInputFocus()\"\r\n          style=\"\r\n            width: 100%;\r\n            min-height: 44px;\r\n            max-height: 120px;\r\n            padding: 12px 16px;\r\n            border: 1px solid #e5e7eb;\r\n            border-radius: 22px;\r\n            resize: none;\r\n            outline: none;\r\n            font-family: inherit;\r\n            font-size: 14px;\r\n            line-height: 1.4;\r\n            background: #ffffff;\r\n            color: #111827;\r\n            transition: all 0.2s;\r\n          \"\r\n          [disabled]=\"isInputDisabled()\"\r\n        ></textarea>\r\n      </div>\r\n\r\n      <!-- Send Button -->\r\n      <button\r\n        type=\"submit\"\r\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\r\n        style=\"\r\n          padding: 12px;\r\n          border-radius: 50%;\r\n          border: none;\r\n          background: #3b82f6;\r\n          color: #ffffff;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-width: 44px;\r\n          min-height: 44px;\r\n        \"\r\n        [style.background]=\"\r\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\r\n        \"\r\n        [style.cursor]=\"\r\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\r\n        \"\r\n        title=\"Envoyer\"\r\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\r\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\r\n      >\r\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\r\n        <div\r\n          *ngIf=\"isSendingMessage\"\r\n          style=\"\r\n            width: 16px;\r\n            height: 16px;\r\n            border: 2px solid #ffffff;\r\n            border-top-color: transparent;\r\n            border-radius: 50%;\r\n            animation: spin 1s linear infinite;\r\n          \"\r\n        ></div>\r\n      </button>\r\n    </form>\r\n\r\n    <!-- Emoji Picker -->\r\n    <div\r\n      *ngIf=\"showEmojiPicker\"\r\n      style=\"\r\n        position: absolute;\r\n        bottom: 80px;\r\n        left: 16px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        width: 320px;\r\n        max-height: 300px;\r\n        overflow-y: auto;\r\n      \"\r\n    >\r\n      <div style=\"padding: 16px\">\r\n        <h4\r\n          style=\"\r\n            margin: 0 0 12px 0;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            color: #374151;\r\n          \"\r\n        >\r\n          Émojis\r\n        </h4>\r\n        <div\r\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\r\n        >\r\n          <button\r\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\r\n            (click)=\"insertEmoji(emoji)\"\r\n            style=\"\r\n              padding: 8px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 8px;\r\n              cursor: pointer;\r\n              font-size: 20px;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n            [title]=\"emoji.name\"\r\n          >\r\n            {{ emoji.emoji }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Attachment Menu -->\r\n    <div\r\n      *ngIf=\"showAttachmentMenu\"\r\n      style=\"\r\n        position: absolute;\r\n        bottom: 80px;\r\n        left: 60px;\r\n        background: #ffffff;\r\n        border-radius: 16px;\r\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid #e5e7eb;\r\n        z-index: 50;\r\n        min-width: 200px;\r\n      \"\r\n    >\r\n      <div style=\"padding: 16px\">\r\n        <h4\r\n          style=\"\r\n            margin: 0 0 12px 0;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            color: #374151;\r\n          \"\r\n        >\r\n          Pièces jointes\r\n        </h4>\r\n        <div\r\n          style=\"\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 12px;\r\n          \"\r\n        >\r\n          <!-- Images -->\r\n          <button\r\n            (click)=\"triggerFileInput('image')\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #dbeafe;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-image\"\r\n                style=\"color: #3b82f6; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Images</span\r\n            >\r\n          </button>\r\n\r\n          <!-- Documents -->\r\n          <button\r\n            (click)=\"triggerFileInput('document')\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #fef3c7;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-file-alt\"\r\n                style=\"color: #f59e0b; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Documents</span\r\n            >\r\n          </button>\r\n\r\n          <!-- Camera -->\r\n          <button\r\n            (click)=\"openCamera()\"\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              gap: 8px;\r\n              padding: 16px;\r\n              border: none;\r\n              background: transparent;\r\n              border-radius: 12px;\r\n              cursor: pointer;\r\n              transition: all 0.2s;\r\n            \"\r\n            onmouseover=\"this.style.background='#f3f4f6'\"\r\n            onmouseout=\"this.style.background='transparent'\"\r\n          >\r\n            <div\r\n              style=\"\r\n                width: 48px;\r\n                height: 48px;\r\n                background: #dcfce7;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n              \"\r\n            >\r\n              <i\r\n                class=\"fas fa-camera\"\r\n                style=\"color: #10b981; font-size: 20px\"\r\n              ></i>\r\n            </div>\r\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\r\n              >Caméra</span\r\n            >\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Hidden File Input -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      style=\"display: none\"\r\n      (change)=\"onFileSelected($event)\"\r\n      [accept]=\"getFileAcceptTypes()\"\r\n      multiple\r\n    />\r\n  </footer>\r\n\r\n  <!-- Overlay to close menus -->\r\n  <div\r\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\r\n    style=\"\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: rgba(0, 0, 0, 0.25);\r\n      z-index: 40;\r\n    \"\r\n    (click)=\"closeAllMenus()\"\r\n  ></div>\r\n</div>\r\n\r\n<!-- Call Interface Component -->\r\n<app-call-interface\r\n  [isVisible]=\"isInCall\"\r\n  [activeCall]=\"activeCall\"\r\n  [callType]=\"callType\"\r\n  [otherParticipant]=\"otherParticipant\"\r\n  (callEnded)=\"endCall()\"\r\n  (callAccepted)=\"onCallAccepted($event)\"\r\n  (callRejected)=\"onCallRejected()\"\r\n></app-call-interface>\r\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;ICwGvEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAoBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAGRJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IAsFNX,EAAA,CAAAE,cAAA,cAaC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,EAAA,CAAAC,SAAA,YAAoD;IACpDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhDJ,EAAA,CAAAE,cAAA,iBAgBC;IACCF,EAAA,CAAAC,SAAA,YAAkD;IAClDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,SAAA,cAAmD;IACnDD,EAAA,CAAAE,cAAA,kBAgBC;IACCF,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBpDJ,EAAA,CAAAE,cAAA,cAkBC;IAWGF,EAAA,CAAAC,SAAA,YAQK;IACLD,EAAA,CAAAE,cAAA,YAOC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAA2C;IACzCF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAKRJ,EAAA,CAAAE,cAAA,cASC;IACCF,EAAA,CAAAC,SAAA,cAUO;IACPD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIhEJ,EAAA,CAAAE,cAAA,cASC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,EAAA,CAAAE,cAAA,cAGC;IAUKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,EAAA,CAAAE,cAAA,cAGC;IAcGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IAbhDjC,EAAA,CAAAI,YAAA,EAgBE;;;;IAfAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,EAAA,CAAAE,cAAA,cAaC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,cAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,cAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,kBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;IA0BtB1C,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAKK;;;;;IAxBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAI,8DAAA,iBAIK;IACL7D,EAAA,CAAAyD,UAAA,IAAAK,8DAAA,iBAIK;IACL9D,EAAA,CAAAyD,UAAA,IAAAM,8DAAA,iBAIK;IACL/D,EAAA,CAAAyD,UAAA,IAAAO,8DAAA,iBAKK;IACPhE,EAAA,CAAAI,YAAA,EAAM;;;;IAnBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,eAAkC;IAKlCjE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,YAA+B;IAK/BjE,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,iBAAoC;IAMpCjE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAwC,MAAA,YAA+B;;;;;;IAzK5CjE,EAAA,CAAAkE,uBAAA,GAMC;IAEClE,EAAA,CAAAyD,UAAA,IAAAU,yDAAA,kBAgBM;IAGNnE,EAAA,CAAAE,cAAA,cAQC;IAFCF,EAAA,CAAAY,UAAA,mBAAAwD,yEAAAjB,MAAA;MAAA,MAAAkB,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAwD,IAAA;MAAA,MAAA7C,WAAA,GAAA4C,WAAA,CAAAxC,SAAA;MAAA,MAAA0C,OAAA,GAAAvE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAA/C,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAAsB,+EAAAtB,MAAA;MAAA,MAAAkB,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAwD,IAAA;MAAA,MAAA7C,WAAA,GAAA4C,WAAA,CAAAxC,SAAA;MAAA,MAAA6C,OAAA,GAAA1E,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAuD,OAAA,CAAAC,oBAAA,CAAAlD,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAmB,yDAAA,kBAqBM;IAGN5E,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAoB,yDAAA,kBAeM;IAGN7E,EAAA,CAAAyD,UAAA,IAAAqB,yDAAA,kBAKM;IAGN9E,EAAA,CAAAyD,UAAA,IAAAsB,yDAAA,kBA0BM;IAGN/E,EAAA,CAAAE,cAAA,cAUC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAAuB,0DAAA,kBAyBM;IACRhF,EAAA,CAAAI,YAAA,EAAM;IAGZJ,EAAA,CAAAiF,qBAAA,EAAe;;;;;;IAvKVjF,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCpF,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,qBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,6BAEC;IACD7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,IAAAqC,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlEpF,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAIF;IAJEL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAI,mBAAA,OAAA7D,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,IAAAqC,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAIF;IAcEpF,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAM,cAAA,CAAA/D,WAAA,aAAwC;IAOrCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAgD,OAAA,CAAAO,QAAA,CAAAhE,WAAA,EAAuB;IAwCrBzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA0F,iBAAA,CAAAR,OAAA,CAAAS,iBAAA,CAAAlE,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAiD,OAAA,CAAArC,aAAA,CAA0C;;;;;IA+BrD7C,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eASE;IACFD,EAAA,CAAAE,cAAA,eAOC;IAEGF,EAAA,CAAAC,SAAA,eAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;IA7CNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAA0D,OAAA,CAAApF,gBAAA,kBAAAoF,OAAA,CAAApF,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAAwD,OAAA,CAAApF,gBAAA,kBAAAoF,OAAA,CAAApF,gBAAA,CAAAc,QAAA;;;;;IA5L3EtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAoC,mDAAA,6BAgLe;IAGf7F,EAAA,CAAAyD,UAAA,IAAAqC,0CAAA,kBAoDM;IACR9F,EAAA,CAAAI,YAAA,EAAM;;;;IAtOuBJ,EAAA,CAAAK,SAAA,GACb;IADaL,EAAA,CAAAkC,UAAA,YAAA6D,MAAA,CAAAC,QAAA,CACb,iBAAAD,MAAA,CAAAE,gBAAA;IAiLXjG,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA6D,MAAA,CAAAG,iBAAA,CAAuB;;;;;IAuKxBlG,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,eAUO;;;;;;IAmCLD,EAAA,CAAAE,cAAA,kBAeC;IAbCF,EAAA,CAAAY,UAAA,mBAAAuF,sEAAA;MAAA,MAAA9B,WAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAsF,IAAA;MAAA,MAAAC,SAAA,GAAAhC,WAAA,CAAAxC,SAAA;MAAA,MAAAyE,OAAA,GAAAtG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAmF,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5BrG,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAmE,SAAA,CAAAG,IAAA,CAAoB;IAEpBxG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+F,SAAA,CAAAI,KAAA,MACF;;;;;IA/CNzG,EAAA,CAAAE,cAAA,eAeC;IAUKF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAyD,UAAA,IAAAiD,6CAAA,sBAiBS;IACX1G,EAAA,CAAAI,YAAA,EAAM;;;;IAjBgBJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAAyE,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxE7G,EAAA,CAAAE,cAAA,eAaC;IAUKF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAMC;IAGGF,EAAA,CAAAY,UAAA,mBAAAkG,6DAAA;MAAA9G,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAC,OAAA,GAAAhH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6F,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCjH,EAAA,CAAAE,cAAA,eAUC;IACCF,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAAsG,8DAAA;MAAAlH,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAI,OAAA,GAAAnH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAgG,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCjH,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAAwG,8DAAA;MAAApH,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAM,OAAA,GAAArH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkG,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtBtH,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAkBXJ,EAAA,CAAAE,cAAA,eAYC;IADCF,EAAA,CAAAY,UAAA,mBAAA2G,0DAAA;MAAAvH,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsG,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B1H,EAAA,CAAAI,YAAA,EAAM;;;ADhiCT,OAAM,MAAOuH,oBAAoB;EA2I/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IALtB,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA3Ib;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAnC,QAAQ,GAAU,EAAE;IACpB,KAAAnD,aAAa,GAAkB,IAAI;IACnC,KAAAuF,eAAe,GAAG,KAAK;IAEvB,KAAA5H,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAA6H,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA5C,iBAAiB,GAAG,KAAK;IACzB,KAAA9E,YAAY,GAAG,KAAK;IACpB,KAAA2H,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAAC,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAC,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAC,eAAe,GAAU,CACvB;MACE9I,EAAE,EAAE,SAAS;MACbuE,IAAI,EAAE,SAAS;MACfwE,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAExE,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACEvE,EAAE,EAAE,QAAQ;MACZuE,IAAI,EAAE,QAAQ;MACdwE,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAExE,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACEvE,EAAE,EAAE,QAAQ;MACZuE,IAAI,EAAE,QAAQ;MACdwE,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAExE,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAK,qBAAqB,GAAG,IAAI,CAACkE,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAG,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIzL,YAAY,EAAE;IAUxC,IAAI,CAAC0L,WAAW,GAAG,IAAI,CAAC3D,EAAE,CAAC4D,KAAK,CAAC;MAC/B/I,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC6L,QAAQ,EAAE7L,UAAU,CAAC8L,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAACpL,gBAAgB,IAAI,IAAI,CAACmJ,gBAAgB,IAAI,IAAI,CAACb,gBAAgB;EAE5E;EAEA;EACQ+C,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACjB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACzE,cAAc,CAAC0E,aAAa,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChBV,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAES,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACzE,cAAc,CAACgF,WAAW,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEa,IAAI,CAAC;UAC5C,IAAI,CAACxC,UAAU,GAAGwC,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACAV,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCS,YAAY,CAACK,MAAM,CAAC5L,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC0G,cAAc,CAACmF,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQb,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CnB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgB,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEjB,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAClK,aAAa,GAAG,IAAI;QACzB,IAAI,CAACuF,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMmF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnCjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACtL,EAAE,IAAIsL,IAAI,CAACG,MAAM;MACjDvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CuB,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACb1L,EAAE,EAAEsL,IAAI,CAACtL,EAAE;QACXyL,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC7K,aAAa,GAAG6K,MAAM;QAC3B,IAAI,CAACtF,eAAe,GAAGmF,IAAI,CAACjM,QAAQ,IAAIiM,IAAI,CAAC/G,IAAI,IAAI,KAAK;QAC1D2F,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDnK,EAAE,EAAE,IAAI,CAACY,aAAa;UACtBvB,QAAQ,EAAE,IAAI,CAAC8G;SAChB,CAAC;OACH,MAAM;QACL+D,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEQ,IAAI,CAAC;QAC/D,IAAI,CAAC1K,aAAa,GAAG,IAAI;QACzB,IAAI,CAACuF,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO2E,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAClK,aAAa,GAAG,IAAI;MACzB,IAAI,CAACuF,eAAe,GAAG,KAAK;;EAEhC;EAEQmE,gBAAgBA,CAAA;IACtB,MAAMsB,cAAc,GAAG,IAAI,CAAC/F,KAAK,CAACgG,QAAQ,CAACC,QAAQ,CAAChC,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyB,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC5F,YAAY,CAAC+F,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC3F,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,cAAc,CAACiG,eAAe,CAACJ,cAAc,CAAC,CAAClB,SAAS,CAAC;MAC5DC,IAAI,EAAGzE,YAAY,IAAI;QACrBgE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEjE,YAAY,CAAC;QACjEgE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCnK,EAAE,EAAEkG,YAAY,EAAElG,EAAE;UACpBiM,YAAY,EAAE/F,YAAY,EAAE+F,YAAY;UACxCC,iBAAiB,EAAEhG,YAAY,EAAE+F,YAAY,EAAEE,MAAM;UACrDC,OAAO,EAAElG,YAAY,EAAEkG,OAAO;UAC9BrI,QAAQ,EAAEmC,YAAY,EAAEnC,QAAQ;UAChCsI,aAAa,EAAEnG,YAAY,EAAEnC,QAAQ,EAAEoI;SACxC,CAAC;QACF,IAAI,CAACjG,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACoG,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC3F,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQkG,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACpG,YAAY,EAAE+F,YAAY,IAChC,IAAI,CAAC/F,YAAY,CAAC+F,YAAY,CAACE,MAAM,KAAK,CAAC,EAC3C;MACAjC,OAAO,CAACuC,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAAClO,gBAAgB,GAAG,IAAI;MAC5B;;IAGF2L,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACvJ,aAAa,CAAC;IACnDsJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACjE,YAAY,CAAC+F,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAAC/F,YAAY,CAACkG,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC7N,gBAAgB,GAAG,IAAI,CAAC2H,YAAY,CAAC+F,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC3M,EAAE,IAAI2M,CAAC,CAACjB,GAAG;QACnC,OAAOmB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACjM,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAAC2H,YAAY,CAAC+F,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC3M,EAAE,IAAI2M,CAAC,CAACjB,GAAG;QACnCxB,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3ByC,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAChM,aAAa,CACnB;QACD,OAAOiM,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACjM,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAAC2H,YAAY,CAAC+F,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;MACvEjC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC5L,gBAAgB,GAAG,IAAI,CAAC2H,YAAY,CAAC+F,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAC/F,YAAY,CAAC+F,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMW,kBAAkB,GACtB,IAAI,CAACvO,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACmN,GAAG;QACvD,IAAImB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAACjM,aAAa,CAAC,EAAE;UAC7DsJ,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC5L,gBAAgB,GAAG,IAAI,CAAC2H,YAAY,CAAC+F,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAC1N,gBAAgB,EAAE;MACzB2L,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDnK,EAAE,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACmN,GAAG;QACzDrM,QAAQ,EAAE,IAAI,CAACd,gBAAgB,CAACc,QAAQ;QACxCa,KAAK,EAAE,IAAI,CAAC3B,gBAAgB,CAAC2B,KAAK;QAClC1B,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACA0L,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC5L,gBAAgB,CAACc,QAAQ,CAC/B;MACD6K,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC5L,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACL6K,OAAO,CAACY,KAAK,CAAC,uDAAuD,CAAC;MACtEZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACjE,YAAY,CAAC+F,YAAY,CAAC;MACzE/B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACvJ,aAAa,CAAC;MAEnD;MACAsJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQ2C,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrG,YAAY,EAAElG,EAAE,EAAE;IAE5B;IACA,IAAI+D,QAAQ,GAAG,IAAI,CAACmC,YAAY,CAACnC,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACgJ,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACvN,SAAS,IAAIuN,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAACxN,SAAS,IAAIwN,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFpD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CoD,KAAK,EAAE,IAAI,CAACxJ,QAAQ,CAACoI,MAAM;MAC3BqB,KAAK,EAAE,IAAI,CAACzJ,QAAQ,CAAC,CAAC,CAAC,EAAEtD,OAAO;MAChCgN,IAAI,EAAE,IAAI,CAAC1J,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACoI,MAAM,GAAG,CAAC,CAAC,EAAE1L;KAChD,CAAC;IAEF,IAAI,CAAC6F,eAAe,GAAG,IAAI,CAACvC,QAAQ,CAACoI,MAAM,KAAK,IAAI,CAAClD,oBAAoB;IACzE,IAAI,CAAC7C,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsH,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACtH,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAElG,EAAE,EACvE;IAEF,IAAI,CAACqG,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC6C,WAAW,EAAE;IAElB;IACA,MAAM0E,MAAM,GAAG,IAAI,CAAC7J,QAAQ,CAACoI,MAAM;IAEnC,IAAI,CAACpG,cAAc,CAAC8H,WAAW,CAC7B,IAAI,CAACjN,aAAc;IAAE;IACrB,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEmN,GAAI;IAAE;IAC1D,IAAI,CAACxF,YAAY,CAAClG,EAAE,EACpB,IAAI,CAACkJ,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACyB,SAAS,CAAC;MACVC,IAAI,EAAGmD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACpI,QAAQ,GAAG,CAAC,GAAG+J,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAChK,QAAQ,CAAC;UAC5D,IAAI,CAACuC,eAAe,GAClBwH,WAAW,CAAC3B,MAAM,KAAK,IAAI,CAAClD,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAC3C,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDyE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAAC1F,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC6C,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQsD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACtG,YAAY,EAAElG,EAAE,EAAE;MAC1BkK,OAAO,CAACuC,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFvC,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAACjE,YAAY,CAAClG,EAAE,CACrB;IAED;IACAkK,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACzE,cAAc,CAACiI,sBAAsB,CACxC,IAAI,CAAC9H,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAGsD,UAAe,IAAI;QACxB/D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE8D,UAAU,CAAC;QACpE/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCnK,EAAE,EAAEiO,UAAU,CAACjO,EAAE;UACjBkO,IAAI,EAAED,UAAU,CAACC,IAAI;UACrBzN,OAAO,EAAEwN,UAAU,CAACxN,OAAO;UAC3BV,MAAM,EAAEkO,UAAU,CAAClO,MAAM;UACzBoO,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACAnE,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAAC5G,cAAc,CAAC0K,UAAU,CAAC,CAChC;QACD/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3G,QAAQ,CAACyK,UAAU,CAAC,CAAC;QAC/D/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACmE,OAAO,CAACL,UAAU,CAAC,CAAC;QAC7D/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxI,WAAW,CAACsM,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACE,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzDvE,OAAO,CAACC,GAAG,CAAC,yBAAyBsE,KAAK,GAAG,EAAE;cAC7CP,IAAI,EAAEM,GAAG,CAACN,IAAI;cACdQ,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACdpK,IAAI,EAAEiK,GAAG,CAACjK,IAAI;cACdqK,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAAC9K,QAAQ,CAAC+K,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAC/O,EAAE,KAAKiO,UAAU,CAACjO,EAAE,CAClC;QACD,IAAI,CAAC6O,aAAa,EAAE;UAClB;UACA,IAAI,CAAC9K,QAAQ,CAACiL,IAAI,CAACf,UAAU,CAAC;UAC9B/D,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAACpG,QAAQ,CAACoI,MAAM,CACrB;UAED;UACA,IAAI,CAAClG,GAAG,CAACgJ,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACxB,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMS,QAAQ,GAAGF,UAAU,CAAClO,MAAM,EAAEC,EAAE,IAAIiO,UAAU,CAACE,QAAQ;UAC7DjE,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DgE,QAAQ;YACRvN,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCuO,gBAAgB,EAAEhB,QAAQ,KAAK,IAAI,CAACvN;WACrC,CAAC;UAEF,IAAIuN,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACvN,aAAa,EAAE;YAC/C,IAAI,CAACwO,iBAAiB,CAACnB,UAAU,CAACjO,EAAE,CAAC;;;MAG3C,CAAC;MACD8K,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACAZ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACzE,cAAc,CAACsJ,0BAA0B,CAC5C,IAAI,CAACnJ,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAG2E,UAAe,IAAI;QACxBpF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmF,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAAC7D,MAAM,KAAK,IAAI,CAAC7K,aAAa,EAAE;UAC5C,IAAI,CAACqD,iBAAiB,GAAGqL,UAAU,CAACnG,QAAQ;UAC5C,IAAI,CAAClD,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACzE,cAAc,CAACwJ,8BAA8B,CAChD,IAAI,CAACrJ,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAG6E,kBAAuB,IAAI;QAChCtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqF,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAACxP,EAAE,KAAK,IAAI,CAACkG,YAAY,CAAClG,EAAE,EAAE;UAClD,IAAI,CAACkG,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGsJ;UAAkB,CAAE;UACnE,IAAI,CAACvJ,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQsE,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAAC1J,cAAc,CAACqJ,iBAAiB,CAACK,SAAS,CAAC,CAAC/E,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsF,SAAS,CAAC;MACrD,CAAC;MACD3E,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA4E,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnG,WAAW,CAACoG,KAAK,IAAI,CAAC,IAAI,CAACzJ,YAAY,EAAElG,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAAC8I,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE8F,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACpP,OAAO,EAAE;IAEd,MAAM2N,UAAU,GAAG,IAAI,CAAC7P,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEmN,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACf,IAAI,CAACpI,YAAY,CAAC+F,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAClF,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC+C,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC1J,OAAO;MACP2N,UAAU;MACVxC,cAAc,EAAE,IAAI,CAAC1F,YAAY,CAAClG;KACnC,CAAC;IAEF,IAAI,CAAC+F,cAAc,CAAC2J,WAAW,CAC7BtB,UAAU,EACV3N,OAAO,EACPqP,SAAS,EACT,MAAa,EACb,IAAI,CAAC5J,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrB7F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4F,OAAO,CAAC;QAEpD;QACA,MAAMlB,aAAa,GAAG,IAAI,CAAC9K,QAAQ,CAAC+K,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAC/O,EAAE,KAAK+P,OAAO,CAAC/P,EAAE,CAC/B;QACD,IAAI,CAAC6O,aAAa,EAAE;UAClB,IAAI,CAAC9K,QAAQ,CAACiL,IAAI,CAACe,OAAO,CAAC;UAC3B7F,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACpG,QAAQ,CAACoI,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC5C,WAAW,CAACyG,KAAK,EAAE;QACxB,IAAI,CAACnJ,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC+C,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAAC3D,GAAG,CAACgJ,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACxB,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD5C,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAClF,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC+C,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA8D,cAAcA,CAAA;IACZwB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACe,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA5R,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAM4R,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACrD,IAAI,CAACsD,GAAG,EAAE,GAAG,IAAItD,IAAI,CAACzO,UAAU,CAAC,CAAC2O,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIiD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAACjB,SAAiB;IACpC,OACE,IAAI,CAACtH,aAAa,CAACsH,SAAS,CAAC,IAAI;MAC/BkB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BtB,SAAiB,EACjBuB,IAAkD;IAElD,IAAI,CAAC7I,aAAa,CAACsH,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACiB,oBAAoB,CAACjB,SAAS,CAAC;MACvC,GAAGuB;KACJ;EACH;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC1S,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACgG,YAAY,CAAC+F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAAC1D,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB8B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5L,gBAAgB,CAACc,QAAQ,CAAC;EAC7E;EAEA6R,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC3S,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACgG,YAAY,CAAC+F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAAC1D,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB8B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5L,gBAAgB,CAACc,QAAQ,CAAC;EAC7E;EAEA8R,OAAOA,CAAA;IACL,IAAI,CAAC/I,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,UAAU,GAAG,IAAI;IACtB0B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;EAEA;EACA;EAEA;EAEAiH,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAACxB,OAAY;IACvB,MAAMyB,cAAc,GAAGzB,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAE9C,GAAG,EAAE;MACvB,MAAMgD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGL,cAAc,CAAC9C,GAAG;MAC9BgD,IAAI,CAACI,QAAQ,GAAGN,cAAc,CAACjN,IAAI,IAAI,MAAM;MAC7CmN,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAAC1L,YAAY,CAACoM,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAnT,YAAYA,CAAA;IACV,IAAI,CAAC2H,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAyL,cAAcA,CAAA;IACZ,IAAI,CAAClT,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAmT,qBAAqBA,CAAA;IACnBpI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C;IACA,IAAI,CAACrE,MAAM,CACRyM,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK;MACTtI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,CACDsI,KAAK,CAAE3H,KAAK,IAAI;MACfZ,OAAO,CAACY,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAAChF,MAAM,CAACyM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACd,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEApM,aAAaA,CAAA;IACX,IAAI,CAACc,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACrH,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC2H,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAzE,oBAAoBA,CAACqN,OAAY,EAAE6C,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC9L,eAAe,GAAGgJ,OAAO;IAC9B,IAAI,CAAC/I,mBAAmB,GAAG;MAAEC,CAAC,EAAE2L,KAAK,CAACE,OAAO;MAAE5L,CAAC,EAAE0L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACjM,sBAAsB,GAAG,IAAI;EACpC;EAEAkM,kBAAkBA,CAACjD,OAAY,EAAE6C,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC7L,qBAAqB,GAAG2I,OAAO;IACpC,IAAI,CAAC/I,mBAAmB,GAAG;MAAEC,CAAC,EAAE2L,KAAK,CAACE,OAAO;MAAE5L,CAAC,EAAE0L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC5L,kBAAkB,GAAG,IAAI;EAChC;EAEA+L,UAAUA,CAAC1O,KAAa;IACtB,IAAI,IAAI,CAAC4C,qBAAqB,EAAE;MAC9B,IAAI,CAAC+L,cAAc,CAAC,IAAI,CAAC/L,qBAAqB,CAACpH,EAAE,EAAEwE,KAAK,CAAC;;IAE3D,IAAI,CAAC2C,kBAAkB,GAAG,KAAK;EACjC;EAEAgM,cAAcA,CAAC1D,SAAiB,EAAEjL,KAAa;IAC7C0F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE3F,KAAK,EAAE,cAAc,EAAEiL,SAAS,CAAC;IACtE;EACF;;EAEA2D,cAAcA,CAACC,QAAa,EAAE5H,MAAc;IAC1C,OAAO4H,QAAQ,CAAC5H,MAAM,KAAKA,MAAM;EACnC;EAEA6H,cAAcA,CAACvD,OAAY;IACzB7F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4F,OAAO,CAAC/P,EAAE,CAAC;IAClD,IAAI,CAACyF,aAAa,EAAE;EACtB;EAEA8N,cAAcA,CAACxD,OAAY;IACzB7F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4F,OAAO,CAAC/P,EAAE,CAAC;IACjD,IAAI,CAACyF,aAAa,EAAE;EACtB;EAEA+N,aAAaA,CAACzD,OAAY;IACxB7F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4F,OAAO,CAAC/P,EAAE,CAAC;IAChD,IAAI,CAACyF,aAAa,EAAE;EACtB;EAEA;EAEAgO,iBAAiBA,CAAA;IACf,IAAI,CAAClN,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAmN,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAAC/O,qBAAqB,GAAG+O,QAAQ;EACvC;EAEAhP,oBAAoBA,CAACgP,QAAa;IAChC,OAAOA,QAAQ,EAAE3K,MAAM,IAAI,EAAE;EAC/B;EAEA1E,WAAWA,CAACE,KAAU;IACpB,MAAMoP,cAAc,GAAG,IAAI,CAACrK,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE8F,KAAK,IAAI,EAAE;IACnE,MAAMiE,UAAU,GAAGD,cAAc,GAAGpP,KAAK,CAACA,KAAK;IAC/C,IAAI,CAAC+E,WAAW,CAACuK,UAAU,CAAC;MAAErT,OAAO,EAAEoT;IAAU,CAAE,CAAC;IACpD,IAAI,CAACtN,eAAe,GAAG,KAAK;EAC9B;EAEAwN,oBAAoBA,CAAA;IAClB,IAAI,CAACvN,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EAEAxC,gBAAgBA,CAACyK,KAAa,EAAEsB,OAAY;IAC1C,OAAOA,OAAO,CAAC/P,EAAE,IAAI+P,OAAO,CAACrE,GAAG,IAAI+C,KAAK,CAACuF,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZ/J,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAM+J,WAAW,GAAG;MAClBlU,EAAE,EAAE,QAAQmN,IAAI,CAACsD,GAAG,EAAE,EAAE;MACxBhQ,OAAO,EAAE,mBAAmB,IAAI0M,IAAI,EAAE,CAACgH,kBAAkB,EAAE,EAAE;MAC7D1U,SAAS,EAAE,IAAI0N,IAAI,EAAE,CAACiH,WAAW,EAAE;MACnCrU,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,IAAI,WAAW;QAC5CX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDa,KAAK,EACH,IAAI,CAAC3B,gBAAgB,EAAE2B,KAAK,IAAI;OACnC;MACDgO,IAAI,EAAE,MAAM;MACZmG,MAAM,EAAE;KACT;IACD,IAAI,CAACtQ,QAAQ,CAACiL,IAAI,CAACkF,WAAW,CAAC;IAC/B,IAAI,CAACjO,GAAG,CAACgJ,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAACxB,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEArK,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC6C,YAAY,EAAEkG,OAAO,IAC1B,IAAI,CAAClG,YAAY,EAAE+F,YAAY,EAAEE,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEA9G,UAAUA,CAAA;IACR6E,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAAC3D,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEA8N,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAG7C,QAAQ,CAAC8C,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAGzE,IAAI,CAAC0E,GAAG,CAAC,GAAG,EAAE1E,IAAI,CAAC2E,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAACW,SAAS,CAAC3K,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLgK,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMb,YAAY,GAAG7C,QAAQ,CAAC8C,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEApQ,gBAAgBA,CAACkJ,IAAa;IAC5B,MAAMoH,KAAK,GAAG,IAAI,CAACC,SAAS,EAAEpF,aAAa;IAC3C,IAAI,CAACmF,KAAK,EAAE;MACVpL,OAAO,CAACY,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIoD,IAAI,KAAK,OAAO,EAAE;MACpBoH,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAItH,IAAI,KAAK,OAAO,EAAE;MAC3BoH,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAItH,IAAI,KAAK,UAAU,EAAE;MAC9BoH,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC1F,KAAK,GAAG,EAAE;IAEhB;IACA0F,KAAK,CAACpD,KAAK,EAAE;IACb,IAAI,CAAC1L,kBAAkB,GAAG,KAAK;EACjC;EAEA9C,iBAAiBA,CAACjE,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMgW,IAAI,GAAG,IAAItI,IAAI,CAAC1N,SAAS,CAAC;IAChC,OAAOgW,IAAI,CAACtB,kBAAkB,CAAC,OAAO,EAAE;MACtCuB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEApW,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMgW,IAAI,GAAG,IAAItI,IAAI,CAAC1N,SAAS,CAAC;IAChC,MAAMmW,KAAK,GAAG,IAAIzI,IAAI,EAAE;IACxB,MAAM0I,SAAS,GAAG,IAAI1I,IAAI,CAACyI,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAzV,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMyV,QAAQ,GAAG,sBAAsB;IACvC,OAAOzV,OAAO,CAAC0V,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAhT,uBAAuBA,CAACuL,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAM2H,cAAc,GAAG,IAAI,CAACrS,QAAQ,CAAC0K,KAAK,CAAC;IAC3C,MAAM4H,eAAe,GAAG,IAAI,CAACtS,QAAQ,CAAC0K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC2H,cAAc,EAAE3W,SAAS,IAAI,CAAC4W,eAAe,EAAE5W,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM6W,WAAW,GAAG,IAAInJ,IAAI,CAACiJ,cAAc,CAAC3W,SAAS,CAAC,CAACuW,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAIpJ,IAAI,CAACkJ,eAAe,CAAC5W,SAAS,CAAC,CAACuW,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEAnT,gBAAgBA,CAACqL,KAAa;IAC5B,MAAM2H,cAAc,GAAG,IAAI,CAACrS,QAAQ,CAAC0K,KAAK,CAAC;IAC3C,MAAM+H,WAAW,GAAG,IAAI,CAACzS,QAAQ,CAAC0K,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC+H,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAACrW,MAAM,EAAEC,EAAE,KAAKwW,WAAW,CAACzW,MAAM,EAAEC,EAAE;EAC7D;EAEAsD,oBAAoBA,CAACmL,KAAa;IAChC,MAAM2H,cAAc,GAAG,IAAI,CAACrS,QAAQ,CAAC0K,KAAK,CAAC;IAC3C,MAAM4H,eAAe,GAAG,IAAI,CAACtS,QAAQ,CAAC0K,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC4H,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACrW,MAAM,EAAEC,EAAE,KAAKqW,eAAe,CAACtW,MAAM,EAAEC,EAAE;EACjE;EAEAuD,cAAcA,CAACwM,OAAY;IACzB;IACA,IAAIA,OAAO,CAAC7B,IAAI,EAAE;MAChB,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAI6B,OAAO,CAAC1B,WAAW,IAAI0B,OAAO,CAAC1B,WAAW,CAAClC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMsK,UAAU,GAAG1G,OAAO,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIoI,UAAU,CAACvI,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIgF,UAAU,CAACvI,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIgF,UAAU,CAACvI,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAI1B,OAAO,CAAC2G,QAAQ,IAAI3G,OAAO,CAAC4G,QAAQ,IAAI5G,OAAO,CAAC6G,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEApT,QAAQA,CAACuM,OAAY;IACnB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAM2I,kBAAkB,GACtB9G,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,IAAIjD,GAAG,CAACN,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAM4I,WAAW,GAAG,CAAC,EAAE/G,OAAO,CAACgH,QAAQ,IAAIhH,OAAO,CAAC7P,KAAK,CAAC;IAEzD,OAAO2W,kBAAkB,IAAIC,WAAW;EAC1C;EAEAxI,OAAOA,CAACyB,OAAY;IAClB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAM8I,iBAAiB,GACrBjH,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,IAAIjD,GAAG,CAACN,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAO8I,iBAAiB;EAC1B;EAEArV,WAAWA,CAACoO,OAAY;IACtB;IACA,IAAIA,OAAO,CAACgH,QAAQ,EAAE;MACpB,OAAOhH,OAAO,CAACgH,QAAQ;;IAEzB,IAAIhH,OAAO,CAAC7P,KAAK,EAAE;MACjB,OAAO6P,OAAO,CAAC7P,KAAK;;IAGtB;IACA,MAAM+W,eAAe,GAAGlH,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,IAAIjD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAI+I,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACvI,GAAG,IAAIuI,eAAe,CAACtI,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAuI,WAAWA,CAACnH,OAAY;IACtB,MAAMyB,cAAc,GAAGzB,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEjN,IAAI,IAAI,SAAS;EAC1C;EAEA4S,WAAWA,CAACpH,OAAY;IACtB,MAAMyB,cAAc,GAAGzB,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE5C,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMyC,KAAK,GAAGG,cAAc,CAAC5C,IAAI;IACjC,IAAIyC,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOd,IAAI,CAACe,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEA+F,WAAWA,CAACrH,OAAY;IACtB,MAAMyB,cAAc,GAAGzB,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAEtD,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIsD,cAAc,CAACtD,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACtD,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACtD,IAAI,CAACmJ,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAI7F,cAAc,CAACtD,IAAI,CAACmJ,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAI7F,cAAc,CAACtD,IAAI,CAACmJ,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEA/W,YAAYA,CAACmL,MAAc;IACzB;IACA,MAAM6L,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM7I,KAAK,GAAGhD,MAAM,CAAC8L,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACnL,MAAM;IAClD,OAAOmL,MAAM,CAAC7I,KAAK,CAAC;EACtB;EAEA;EACAlM,cAAcA,CAACwN,OAAY,EAAE6C,KAAU;IACrC1I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4F,OAAO,CAAC;EAC1C;EAEAyH,aAAaA,CAAC5E,KAAU;IACtB;IACA,IAAI,CAAC6E,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAAC9E,KAAoB;IACjC,IAAIA,KAAK,CAAC+E,GAAG,KAAK,OAAO,IAAI,CAAC/E,KAAK,CAACgF,QAAQ,EAAE;MAC5ChF,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACnD,WAAW,EAAE;;EAEtB;EAEAmI,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACnF,KAAU;IACjB;IACA,MAAM1C,OAAO,GAAG0C,KAAK,CAACb,MAAM;IAC5B,IACE7B,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAAC9J,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACsH,gBAAgB,EAAE;;EAE3B;EAEA7N,eAAeA,CAAC2L,MAAc;IAC5BvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,MAAM,CAAC;EAClD;EAEArK,WAAWA,CAACwR,KAAU,EAAE7C,OAAY;IAClC7F,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpD4F,OAAO,CAAC/P,EAAE,EACV4S,KAAK,CAACb,MAAM,CAACiG,GAAG,CACjB;EACH;EAEAzW,YAAYA,CAACqR,KAAU,EAAE7C,OAAY;IACnC7F,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAEiF,OAAO,CAAC/P,EAAE,EAAE;MACzEgY,GAAG,EAAEpF,KAAK,CAACb,MAAM,CAACiG,GAAG;MACrBlN,KAAK,EAAE8H;KACR,CAAC;IACF;IACAA,KAAK,CAACb,MAAM,CAACiG,GAAG,GACd,4WAA4W;EAChX;EAEAhX,eAAeA,CAAC+O,OAAY;IAC1B,MAAMkH,eAAe,GAAGlH,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAAE8B,GAAQ,IACzDA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAIwF,eAAe,EAAEvI,GAAG,EAAE;MACxB,IAAI,CAACpH,aAAa,GAAG;QACnBoH,GAAG,EAAEuI,eAAe,CAACvI,GAAG;QACxBnK,IAAI,EAAE0S,eAAe,CAAC1S,IAAI,IAAI,OAAO;QACrCqK,IAAI,EAAE,IAAI,CAACwC,cAAc,CAAC6F,eAAe,CAACrI,IAAI,IAAI,CAAC,CAAC;QACpDmB,OAAO,EAAEA;OACV;MACD,IAAI,CAAC1I,eAAe,GAAG,IAAI;MAC3B6C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC7C,aAAa,CAAC;;EAEvE;EAEA2Q,gBAAgBA,CAAA;IACd,IAAI,CAAC5Q,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB4C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA+N,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5Q,aAAa,EAAEoH,GAAG,EAAE;MAC3B,MAAMgD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,IAAI,CAACvK,aAAa,CAACoH,GAAG;MAClCgD,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACxK,aAAa,CAAC/C,IAAI,IAAI,OAAO;MAClDmN,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAAC1L,YAAY,CAACoM,WAAW,CAAC,wBAAwB,CAAC;MACvDlI,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC7C,aAAa,CAAC/C,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEA4T,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACzR,WAAW,CAACmJ,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAClJ,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC5C,QAAQ,CAACqU,MAAM,CACtCrI,OAAO,IACNA,OAAO,CAACtP,OAAO,EACX4X,WAAW,EAAE,CACdhB,QAAQ,CAAC,IAAI,CAAC3Q,WAAW,CAAC2R,WAAW,EAAE,CAAC,IAC3CtI,OAAO,CAAChQ,MAAM,EAAEV,QAAQ,EACpBgZ,WAAW,EAAE,CACdhB,QAAQ,CAAC,IAAI,CAAC3Q,WAAW,CAAC2R,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC7R,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEA6R,aAAaA,CAAC/I,SAAiB;IAC7B,MAAMgJ,cAAc,GAAG9G,QAAQ,CAAC+G,cAAc,CAAC,WAAWjJ,SAAS,EAAE,CAAC;IACtE,IAAIgJ,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAACtD,SAAS,CAAC3K,GAAG,CAAC,WAAW,CAAC;MACzC0E,UAAU,CAAC,MAAK;QACduJ,cAAc,CAACtD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEA0D,gBAAgBA,CAAA;IACd,IAAI,CAAChS,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQgS,YAAYA,CAAC1Q,QAAkB;IACrC,IAAI,CAAC,IAAI,CAAC9J,gBAAgB,EAAE;MAC1B,IAAI,CAACyH,YAAY,CAAC+F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMiN,WAAW,GAAG,IAAI,CAACza,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACmN,GAAG;IACzE,IAAI,CAACsN,WAAW,EAAE;MAChB,IAAI,CAAChT,YAAY,CAAC+F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF7B,OAAO,CAACC,GAAG,CAAC,iBAAiB9B,QAAQ,gBAAgB,EAAE2Q,WAAW,CAAC;IAEnE,IAAI,CAAC5Q,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKvK,QAAQ,CAACmb,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAC3Q,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAAC4Q,cAAc,EAAE;IAErB;IACA,IAAI,CAACnT,cAAc,CAACgT,YAAY,CAC9BC,WAAW,EACX3Q,QAAQ,EACR,IAAI,CAACnC,YAAY,EAAElG,EAAE,CACtB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEa,IAAI,CAAC;QACnD,IAAI,CAACxC,UAAU,GAAGwC,IAAI;QACtB,IAAI,CAACvC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACzC,YAAY,CAACoM,WAAW,CAC3B,SAAS/J,QAAQ,KAAKvK,QAAQ,CAACmb,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;MACH,CAAC;MACDnO,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACqG,OAAO,EAAE;QACd,IAAI,CAACnL,YAAY,CAAC+F,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEAoN,UAAUA,CAACvO,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAC7E,cAAc,CAACoT,UAAU,CAACvO,YAAY,CAAC,CAACF,SAAS,CAAC;MACrDC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,IAAI,CAAC;QAClD,IAAI,CAACxC,UAAU,GAAGwC,IAAI;QACtB,IAAI,CAAC5C,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAG2C,IAAI,CAACkD,IAAI,KAAKpQ,QAAQ,CAACmb,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAAClT,YAAY,CAACoM,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDtH,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEAqN,UAAUA,CAACxO,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAC7E,cAAc,CAACqT,UAAU,CAACxO,YAAY,CAAC5K,EAAE,EAAE,eAAe,CAAC,CAAC0K,SAAS,CAAC;MACzEC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACnE,YAAY,CAACoM,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDtH,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA;EAEQmN,cAAcA,CAAA;IACpB,IAAI,CAAC5Q,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAG8Q,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC/Q,YAAY,EAAE;MACnB,IAAI,CAACrC,GAAG,CAACgJ,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQqK,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC/Q,SAAS,EAAE;MAClBgR,aAAa,CAAC,IAAI,CAAChR,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA6Q,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAChR,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAAC3C,cAAc,CAAC0T,WAAW,CAC7B,IAAI,CAACjR,UAAU,CAACxI,EAAE,EAClB8P,SAAS;IAAE;IACX,CAAC,IAAI,CAACpH,OAAO,CAAC;KACf,CAACgC,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC3E,YAAY,CAACoM,WAAW,CAC3B,IAAI,CAAC1J,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDoC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACpC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAAC1C,YAAY,CAAC+F,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACJ;EAEA2N,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAClR,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAAC5C,cAAc,CAAC0T,WAAW,CAC7B,IAAI,CAACjR,UAAU,CAACxI,EAAE,EAClB,IAAI,CAAC2I,cAAc;IAAE;IACrBmH,SAAS,CAAC;KACX,CAACpF,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC3E,YAAY,CAACoM,WAAW,CAC3B,IAAI,CAACzJ,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDmC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACnC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAC3C,YAAY,CAAC+F,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEA4N,kBAAkBA,CAAC/I,QAAgB;IACjC,MAAMgJ,KAAK,GAAGrJ,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMiJ,OAAO,GAAGtJ,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMkJ,OAAO,GAAGlJ,QAAQ,GAAG,EAAE;IAE7B,IAAIgJ,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC7F,QAAQ,EAAE,CAAC+F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9D9F,QAAQ,EAAE,CACV+F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAC9F,QAAQ,EAAE,CAAC+F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvBhQ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAACgQ,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAAC5H,MAAM,CAAC6H,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGHpQ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAMqQ,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF5Q,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAI4Q,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrB7Q,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4Q,QAAQ,CAAC;QAEpD;QACAd,KAAI,CAACpS,aAAa,GAAG,IAAI0S,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAIjL;SACvB,CAAC;QAEF;QACAmK,KAAI,CAACnS,WAAW,GAAG,EAAE;QACrBmS,KAAI,CAACvS,gBAAgB,GAAG,IAAI;QAC5BuS,KAAI,CAACtS,sBAAsB,GAAG,CAAC;QAC/BsS,KAAI,CAACrS,mBAAmB,GAAG,WAAW;QAEtCsC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACA8P,KAAI,CAAClS,cAAc,GAAGsR,WAAW,CAAC,MAAK;UACrCY,KAAI,CAACtS,sBAAsB,EAAE;UAC7B;UACAsS,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAAChU,GAAG,CAACgJ,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAgL,KAAI,CAACpS,aAAa,CAACqT,eAAe,GAAItI,KAAK,IAAI;UAC7C1I,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyI,KAAK,CAAC5B,IAAI,CAACpC,IAAI,EAAE,OAAO,CAAC;UACnE,IAAIgE,KAAK,CAAC5B,IAAI,CAACpC,IAAI,GAAG,CAAC,EAAE;YACvBqL,KAAI,CAACnS,WAAW,CAACkH,IAAI,CAAC4D,KAAK,CAAC5B,IAAI,CAAC;;QAErC,CAAC;QAEDiJ,KAAI,CAACpS,aAAa,CAACsT,MAAM,GAAG,MAAK;UAC/BjR,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpE8P,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAACpS,aAAa,CAACwT,OAAO,GAAIzI,KAAU,IAAI;UAC1C1I,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAE8H,KAAK,CAAC9H,KAAK,CAAC;UAC7DmP,KAAI,CAACjU,YAAY,CAAC+F,SAAS,CAAC,iCAAiC,CAAC;UAC9DkO,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAACpS,aAAa,CAAC0T,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/BrR,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExD8P,KAAI,CAACjU,YAAY,CAACoM,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOtH,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAI0Q,YAAY,GAAG,+CAA+C;QAElE,IAAI1Q,KAAK,CAACvG,IAAI,KAAK,iBAAiB,EAAE;UACpCiX,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAI1Q,KAAK,CAACvG,IAAI,KAAK,eAAe,EAAE;UACzCiX,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAI1Q,KAAK,CAACvG,IAAI,KAAK,mBAAmB,EAAE;UAC7CiX,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAI1Q,KAAK,CAACiF,OAAO,EAAE;UACxByL,YAAY,GAAG1Q,KAAK,CAACiF,OAAO;;QAG9BkK,KAAI,CAACjU,YAAY,CAAC+F,SAAS,CAACyP,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC5T,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC6T,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAC7T,aAAa,CAAC8T,IAAI,EAAE;MACzB,IAAI,CAAC9T,aAAa,CAAC2S,MAAM,CAACoB,SAAS,EAAE,CAACrN,OAAO,CAAEsN,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC5T,cAAc,EAAE;MACvBwR,aAAa,CAAC,IAAI,CAACxR,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,mBAAmB,GAAG,YAAY;EACzC;EAEA0T,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACzT,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC6T,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC7T,aAAa,CAAC8T,IAAI,EAAE;;MAE3B,IAAI,CAAC9T,aAAa,CAAC2S,MAAM,CAACoB,SAAS,EAAE,CAACrN,OAAO,CAAEsN,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC9T,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBwR,aAAa,CAAC,IAAI,CAACxR,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcsT,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA5B,iBAAA;MAChChQ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAI2R,MAAI,CAAChU,WAAW,CAACqE,MAAM,KAAK,CAAC,EAAE;UACjCjC,OAAO,CAACY,KAAK,CAAC,sCAAsC,CAAC;UACrDgR,MAAI,CAAC9V,YAAY,CAAC+F,SAAS,CAAC,wBAAwB,CAAC;UACrD+P,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFpR,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1B2R,MAAI,CAAChU,WAAW,CAACqE,MAAM,EACvB,WAAW,EACX2P,MAAI,CAACnU,sBAAsB,CAC5B;QAED;QACA,IAAImU,MAAI,CAACnU,sBAAsB,GAAG,CAAC,EAAE;UACnCuC,OAAO,CAACY,KAAK,CACX,iCAAiC,EACjCgR,MAAI,CAACnU,sBAAsB,CAC5B;UACDmU,MAAI,CAAC9V,YAAY,CAAC+F,SAAS,CACzB,+CAA+C,CAChD;UACD+P,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAACjU,aAAa,EAAEkT,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAACjU,aAAa,CAACkT,QAAQ;;QAGxC7Q,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE4Q,QAAQ,CAAC;QAEvE;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAAChU,WAAW,EAAE;UAC3CoG,IAAI,EAAE6M;SACP,CAAC;QAEF7Q,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CyE,IAAI,EAAEmN,SAAS,CAACnN,IAAI;UACpBV,IAAI,EAAE6N,SAAS,CAAC7N;SACjB,CAAC;QAEF;QACA,IAAI+N,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5B4E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC4E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC4E,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAAS5O,IAAI,CAACsD,GAAG,EAAE,GAAGwL,SAAS,EAAE,EACjC;UACE/N,IAAI,EAAE6M;SACP,CACF;QAED7Q,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5C5F,IAAI,EAAE2X,SAAS,CAAC3X,IAAI;UACpBqK,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBV,IAAI,EAAEgO,SAAS,CAAChO;SACjB,CAAC;QAEF;QACA4N,MAAI,CAAClU,mBAAmB,GAAG,YAAY;QACvC,MAAMkU,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtChS,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD2R,MAAI,CAAC9V,YAAY,CAACoM,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOtH,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DgR,MAAI,CAAC9V,YAAY,CAAC+F,SAAS,CACzB,2CAA2C,IACxCjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACA+L,MAAI,CAAClU,mBAAmB,GAAG,MAAM;QACjCkU,MAAI,CAACnU,sBAAsB,GAAG,CAAC;QAC/BmU,MAAI,CAAChU,WAAW,GAAG,EAAE;QACrBgU,MAAI,CAACpU,gBAAgB,GAAG,KAAK;QAE7BwC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEciS,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAM9L,UAAU,GAAGiO,MAAI,CAAC9d,gBAAgB,EAAEyB,EAAE,IAAIqc,MAAI,CAAC9d,gBAAgB,EAAEmN,GAAG;MAE1E,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAIkM,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIgC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAACtW,cAAc,CAAC2J,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACF8N,SAAS,EACT,OAAc,EACdG,MAAI,CAACnW,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;UACVC,IAAI,EAAGoF,OAAY,IAAI;YACrBsM,MAAI,CAACtY,QAAQ,CAACiL,IAAI,CAACe,OAAO,CAAC;YAC3BsM,MAAI,CAAC3O,cAAc,EAAE;YACrB6O,OAAO,EAAE;UACX,CAAC;UACDzR,KAAK,EAAGA,KAAU,IAAI;YACpBZ,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE0R,MAAM,CAAC1R,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA2R,uBAAuBA,CAAC7L,QAAgB;IACtC,MAAMiJ,OAAO,GAAGtJ,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMkJ,OAAO,GAAGlJ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGiJ,OAAO,IAAIC,OAAO,CAAC9F,QAAQ,EAAE,CAAC+F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA2C,aAAaA,CAAC9J,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtB3I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCzC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCE,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CD,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDE,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7CsC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACnE,YAAY,CAAC2W,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACjV,gBAAgB,EAAE;MACzBwC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACnE,YAAY,CAAC2W,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAAC3W,YAAY,CAAC4W,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC5C,mBAAmB,EAAE,CAACvH,KAAK,CAAE3H,KAAK,IAAI;MACzCZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CACzB,iDAAiD,IAC9CjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEA8M,WAAWA,CAACjK,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtB3I,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAACzC,gBAAgB,EAAE;MAC1BwC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAACsR,kBAAkB,EAAE;EAC3B;EAEAqB,cAAcA,CAAClK,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtB3I,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACzC,gBAAgB,EAAE;MAC1BwC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACmR,oBAAoB,EAAE;EAC7B;EAEAyB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAClV,aAAa,EAAEkT,QAAQ,EAAE;MAChC,IAAI,IAAI,CAAClT,aAAa,CAACkT,QAAQ,CAAC1D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAACxP,aAAa,CAACkT,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACxP,aAAa,CAACkT,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAACxP,aAAa,CAACkT,QAAQ,CAAC1D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ4D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACjT,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgV,GAAG,CAAC,MAAK;MACzC,OAAOzM,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC0M,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAACtK,KAAU;IACvB1I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAMgT,KAAK,GAAGvK,KAAK,CAACb,MAAM,CAACoL,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAChR,MAAM,KAAK,CAAC,EAAE;MAChCjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAegT,KAAK,CAAChR,MAAM,oBAAoB,EAAEgR,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtBjT,OAAO,CAACC,GAAG,CACT,gCAAgCiT,IAAI,CAAC7Y,IAAI,WAAW6Y,IAAI,CAACxO,IAAI,WAAWwO,IAAI,CAAClP,IAAI,EAAE,CACpF;MACD,IAAI,CAACmP,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3BlT,OAAO,CAACC,GAAG,CAAC,yCAAyCiT,IAAI,CAAC7Y,IAAI,EAAE,CAAC;IAEjE,MAAM6J,UAAU,GAAG,IAAI,CAAC7P,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEmN,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACflE,OAAO,CAACY,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF7B,OAAO,CAACC,GAAG,CAAC,4BAA4BiE,UAAU,EAAE,CAAC;IAErD;IACA,MAAMkP,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAACxO,IAAI,GAAG0O,OAAO,EAAE;MACvBpT,OAAO,CAACY,KAAK,CAAC,+BAA+BsS,IAAI,CAACxO,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAAC5I,YAAY,CAAC+F,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAIqR,IAAI,CAAClP,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,IAAI2L,IAAI,CAACxO,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACA1E,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCiT,IAAI,CAAC7Y,IAAI,EACT,gBAAgB,EAChB6Y,IAAI,CAACxO,IAAI,CACV;MACD,IAAI,CAAC2O,aAAa,CAACH,IAAI,CAAC,CACrB5K,IAAI,CAAEgL,cAAc,IAAI;QACvBtT,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DqT,cAAc,CAAC5O,IAAI,CACpB;QACD,IAAI,CAAC6O,gBAAgB,CAACD,cAAc,EAAEpP,UAAU,CAAC;MACnD,CAAC,CAAC,CACDqE,KAAK,CAAE3H,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC2S,gBAAgB,CAACL,IAAI,EAAEhP,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACqP,gBAAgB,CAACL,IAAI,EAAEhP,UAAU,CAAC;EACzC;EAEQqP,gBAAgBA,CAACL,IAAU,EAAEhP,UAAkB;IACrD,MAAMsP,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IACjDlT,OAAO,CAACC,GAAG,CAAC,wCAAwCuT,WAAW,EAAE,CAAC;IAClExT,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACjE,YAAY,CAAClG,EAAE,EAAE,CAAC;IAEnE,IAAI,CAAC6G,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IACvB2C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAMyT,gBAAgB,GAAGvE,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC9R,cAAc,IAAIgJ,IAAI,CAAC0M,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAAC1V,cAAc,IAAI,EAAE,EAAE;QAC7BgS,aAAa,CAACqE,gBAAgB,CAAC;;MAEjC,IAAI,CAAC3X,GAAG,CAACgJ,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAAClJ,cAAc,CAAC2J,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACFgP,IAAI,EACJM,WAAW,EACX,IAAI,CAACxX,YAAY,CAAClG,EAAE,CACrB,CAAC0K,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrB7F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4F,OAAO,CAAC;QAC7D7F,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDnK,EAAE,EAAE+P,OAAO,CAAC/P,EAAE;UACdkO,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;UAClBG,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;UAChC7K,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACuM,OAAO,CAAC;UAChCzB,OAAO,EAAE,IAAI,CAACA,OAAO,CAACyB,OAAO,CAAC;UAC9BgH,QAAQ,EAAE,IAAI,CAACpV,WAAW,CAACoO,OAAO;SACnC,CAAC;QAEFwJ,aAAa,CAACqE,gBAAgB,CAAC;QAC/B,IAAI,CAACrW,cAAc,GAAG,GAAG;QAEzB2H,UAAU,CAAC,MAAK;UACd,IAAI,CAACnL,QAAQ,CAACiL,IAAI,CAACe,OAAO,CAAC;UAC3B,IAAI,CAACrC,cAAc,EAAE;UACrB,IAAI,CAAC1H,YAAY,CAACoM,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACyL,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD/S,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDyO,aAAa,CAACqE,gBAAgB,CAAC;QAC/B,IAAI,CAAC5X,YAAY,CAAC+F,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC8R,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAAClP,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI2L,IAAI,CAAClP,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAI2L,IAAI,CAAClP,IAAI,CAACuD,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAqM,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAChX,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACW,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAwW,UAAUA,CAACnL,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACxL,UAAU,GAAG,IAAI;EACxB;EAEAuW,WAAWA,CAACpL,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMgL,IAAI,GAAIrL,KAAK,CAACsL,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAMlX,CAAC,GAAG2L,KAAK,CAACE,OAAO;IACvB,MAAM5L,CAAC,GAAG0L,KAAK,CAACG,OAAO;IAEvB,IAAI9L,CAAC,GAAGgX,IAAI,CAACG,IAAI,IAAInX,CAAC,GAAGgX,IAAI,CAACI,KAAK,IAAInX,CAAC,GAAG+W,IAAI,CAACK,GAAG,IAAIpX,CAAC,GAAG+W,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC9W,UAAU,GAAG,KAAK;;EAE3B;EAEA+W,MAAMA,CAAC5L,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACxL,UAAU,GAAG,KAAK;IAEvB,MAAM0V,KAAK,GAAGvK,KAAK,CAAC6L,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAAChR,MAAM,GAAG,CAAC,EAAE;MAC7BjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgT,KAAK,CAAChR,MAAM,CAAC;MAE1D;MACAuS,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAC5O,OAAO,CAAE6O,IAAI,IAAI;QACjClT,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCiT,IAAI,CAAC7Y,IAAI,EACT6Y,IAAI,CAAClP,IAAI,EACTkP,IAAI,CAACxO,IAAI,CACV;QACD,IAAI,CAACyO,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAACpX,YAAY,CAACoM,WAAW,CAC3B,GAAG+K,KAAK,CAAChR,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQoR,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAItC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMqC,MAAM,GAAGlN,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMkN,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGhP,IAAI,CAAC2E,GAAG,CAACiK,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIrB,IAAI,CAAC,CAACuD,IAAI,CAAC,EAAEtC,IAAI,CAAC7Y,IAAI,EAAE;cACjD2J,IAAI,EAAEkP,IAAI,CAAClP,IAAI;cACfyR,YAAY,EAAExS,IAAI,CAACsD,GAAG;aACvB,CAAC;YACF8L,OAAO,CAACiB,cAAc,CAAC;WACxB,MAAM;YACLhB,MAAM,CAAC,IAAIlC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACD8C,IAAI,CAAClP,IAAI,EACT0Q,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAAC3D,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAIlC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7D0E,GAAG,CAAChH,GAAG,GAAG4H,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQ3F,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACtO,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAAC2W,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAACzW,aAAa,EAAE;MACtB0W,YAAY,CAAC,IAAI,CAAC1W,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAG6F,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC/F,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAAC2W,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAAC3W,QAAiB;IAC3C;IACA,MAAMiF,UAAU,GAAG,IAAI,CAAC7P,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEmN,GAAG;IAC1E,IAAI0C,UAAU,IAAI,IAAI,CAAClI,YAAY,EAAElG,EAAE,EAAE;MACvCkK,OAAO,CAACC,GAAG,CACT,gCAAgChB,QAAQ,YAAYiF,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEA4R,cAAcA,CAAChV,IAAU;IACvBd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEa,IAAI,CAAC;IACrD,IAAI,CAACxC,UAAU,GAAGwC,IAAI;IACtB,IAAI,CAAC5C,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACyQ,cAAc,EAAE;IACrB,IAAI,CAAClT,YAAY,CAACoM,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA6N,cAAcA,CAAA;IACZ/V,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACgH,OAAO,EAAE;IACd,IAAI,CAACnL,YAAY,CAAC4W,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAsD,gBAAgBA,CAACnQ,OAAY;IAC3B7F,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4F,OAAO,CAAC/P,EAAE,CAAC;IAC5D,IAAI,CAACmgB,mBAAmB,CAACpQ,OAAO,CAAC;EACnC;EAEAqQ,cAAcA,CAAC3Q,SAAiB;IAC9B,OAAO,IAAI,CAACvH,gBAAgB,KAAKuH,SAAS;EAC5C;EAEA0Q,mBAAmBA,CAACpQ,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAAC/P,EAAE;IAC5B,MAAM2W,QAAQ,GAAG,IAAI,CAAC0J,WAAW,CAACtQ,OAAO,CAAC;IAE1C,IAAI,CAAC4G,QAAQ,EAAE;MACbzM,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAE2E,SAAS,CAAC;MACtE,IAAI,CAACzJ,YAAY,CAAC+F,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACqU,cAAc,CAAC3Q,SAAS,CAAC,EAAE;MAClC,IAAI,CAAC6Q,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAACxQ,OAAO,EAAE4G,QAAQ,CAAC;EAC5C;EAEQ4J,kBAAkBA,CAACxQ,OAAY,EAAE4G,QAAgB;IACvD,MAAMlH,SAAS,GAAGM,OAAO,CAAC/P,EAAE;IAE5B,IAAI;MACFkK,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCsF,SAAS,EACT,MAAM,EACNkH,QAAQ,CACT;MAED,IAAI,CAAC1O,YAAY,GAAG,IAAIuY,KAAK,CAAC7J,QAAQ,CAAC;MACvC,IAAI,CAACzO,gBAAgB,GAAGuH,SAAS;MAEjC;MACA,MAAMgR,WAAW,GAAG,IAAI,CAAC/P,oBAAoB,CAACjB,SAAS,CAAC;MACxD,IAAI,CAACsB,oBAAoB,CAACtB,SAAS,EAAE;QACnCkB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE2P,WAAW,CAAC3P,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAE6P,WAAW,CAAC7P,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAC3I,YAAY,CAACyY,YAAY,GAAGD,WAAW,CAAC3P,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAC7I,YAAY,CAAC0Y,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAAC1Y,YAAY,EAAE;UACrB,IAAI,CAAC8I,oBAAoB,CAACtB,SAAS,EAAE;YACnCmB,QAAQ,EAAE,IAAI,CAAC3I,YAAY,CAAC2I;WAC7B,CAAC;UACF1G,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAClC,YAAY,CAAC2I,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAC3I,YAAY,CAAC0Y,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAAC1Y,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;UAC5D,MAAMoB,WAAW,GAAG,IAAI,CAAC5I,YAAY,CAAC4I,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAC5I,YAAY,CAAC2I,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACtB,SAAS,EAAE;YAAEoB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAAC1K,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAAChH,YAAY,CAAC0Y,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/CzW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsF,SAAS,CAAC;QACxD,IAAI,CAAC6Q,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACrY,YAAY,CAAC0Y,gBAAgB,CAAC,OAAO,EAAG7V,KAAK,IAAI;QACpDZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAACuU,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACrY,YAAY,CACdiD,IAAI,EAAE,CACNsH,IAAI,CAAC,MAAK;QACTtI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAACnE,YAAY,CAACoM,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDK,KAAK,CAAE3H,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAACuU,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOxV,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAAC9E,YAAY,CAAC+F,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAACuU,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACrY,YAAY,EAAE;MACrBiC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACjC,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAAC2Y,KAAK,EAAE;MACzB,IAAI,CAAC3Y,YAAY,CAAC4I,WAAW,GAAG,CAAC;MACjC,IAAI,CAAC5I,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACjC,GAAG,CAACgJ,aAAa,EAAE;EAC1B;EAEAoR,WAAWA,CAACtQ,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC2G,QAAQ,EAAE,OAAO3G,OAAO,CAAC2G,QAAQ;IAC7C,IAAI3G,OAAO,CAAC4G,QAAQ,EAAE,OAAO5G,OAAO,CAAC4G,QAAQ;IAC7C,IAAI5G,OAAO,CAAC6G,KAAK,EAAE,OAAO7G,OAAO,CAAC6G,KAAK;IAEvC;IACA,MAAMiK,eAAe,GAAG9Q,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAEuD,UAAU,CAAC,QAAQ,CAAC,IAAIjD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAI2S,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACnS,GAAG,IAAImS,eAAe,CAAClS,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAmS,aAAaA,CAAC/Q,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAAC/P,EAAE,IAAI,EAAE;IAClC,MAAM+gB,IAAI,GAAGtR,SAAS,CACnBuR,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAAC5J,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAM6J,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM/B,MAAM,GAAG,CAAC,GAAI,CAACyB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACpS,IAAI,CAACsQ,MAAM,CAAC;;IAGpB,OAAO8B,KAAK;EACd;EAEAE,gBAAgBA,CAACvR,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAAC/P,EAAE,CAAC;IAClD,MAAMuhB,UAAU,GAAG,EAAE;IACrB,OAAOhR,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAI4Q,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAACzR,OAAY;IAC9B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAAC/P,EAAE,CAAC;IAClD,OAAO,IAAI,CAACyhB,eAAe,CAACzQ,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEA6Q,gBAAgBA,CAAC3R,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAAC/P,EAAE,CAAC;IAClD,MAAM4Q,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIb,OAAO,CAAC4R,QAAQ,EAAE/Q,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAAC6Q,eAAe,CAAC7Q,QAAQ,CAAC;EACvC;EAEQ6Q,eAAeA,CAAC3H,OAAe;IACrC,MAAMD,OAAO,GAAGtJ,IAAI,CAACC,KAAK,CAACsJ,OAAO,GAAG,EAAE,CAAC;IACxC,MAAM8H,gBAAgB,GAAGrR,IAAI,CAACC,KAAK,CAACsJ,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAI+H,gBAAgB,CAAC5N,QAAQ,EAAE,CAAC+F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA8H,gBAAgBA,CAAC9R,OAAY,EAAE+R,SAAiB;IAC9C,MAAMrS,SAAS,GAAGM,OAAO,CAAC/P,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACiI,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;MAC7D;;IAGF,MAAM8R,UAAU,GAAG,EAAE;IACrB,MAAMQ,cAAc,GAAID,SAAS,GAAGP,UAAU,GAAI,GAAG;IACrD,MAAMS,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAC9Z,YAAY,CAAC2I,QAAQ;IAEpE,IAAI,CAAC3I,YAAY,CAAC4I,WAAW,GAAGmR,QAAQ;IACxC9X,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6X,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAC,gBAAgBA,CAAClS,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAAC/P,EAAE;IAC5B,MAAMgR,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACjB,SAAS,CAAC;IAEjD;IACA,MAAMyS,QAAQ,GAAGlR,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACtB,SAAS,EAAE;MAAEqB,KAAK,EAAEoR;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAACja,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;MAC5D,IAAI,CAACxH,YAAY,CAACyY,YAAY,GAAGwB,QAAQ;;IAG3C,IAAI,CAAClc,YAAY,CAACoM,WAAW,CAAC,YAAY8P,QAAQ,GAAG,CAAC;EACxD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7Y,aAAa,CAAC8Y,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAAC7Z,SAAS,EAAE;MAClBgR,aAAa,CAAC,IAAI,CAAChR,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACR,cAAc,EAAE;MACvBwR,aAAa,CAAC,IAAI,CAACxR,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACsB,aAAa,EAAE;MACtB0W,YAAY,CAAC,IAAI,CAAC1W,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACxB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC6T,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC7T,aAAa,CAAC8T,IAAI,EAAE;;MAE3B,IAAI,CAAC9T,aAAa,CAAC2S,MAAM,EAAEoB,SAAS,EAAE,CAACrN,OAAO,CAAEsN,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAAC2E,iBAAiB,EAAE;EAC1B;;;uBAj3EW5a,oBAAoB,EAAA3H,EAAA,CAAAskB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxkB,EAAA,CAAAskB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1kB,EAAA,CAAAskB,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3kB,EAAA,CAAAskB,iBAAA,CAAAM,EAAA,CAAA5c,cAAA,GAAAhI,EAAA,CAAAskB,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA9kB,EAAA,CAAAskB,iBAAA,CAAAtkB,EAAA,CAAA+kB,iBAAA;IAAA;EAAA;;;YAApBpd,oBAAoB;MAAAqd,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAApE,GAAA;QAAA,IAAAoE,EAAA;;;;;;;;;;;;;;;UClBjCnlB,EAAA,CAAAE,cAAA,aASC;UA4DKF,EAAA,CAAAY,UAAA,mBAAAwkB,sDAAA;YAAA,OAASrE,GAAA,CAAAxM,qBAAA,EAAuB;UAAA,EAAC;UAmBjCvU,EAAA,CAAAC,SAAA,WAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAejEF,EAAA,CAAAY,UAAA,mBAAAykB,mDAAA;YAAA,OAAStE,GAAA,CAAAhf,eAAA,CAAAgf,GAAA,CAAAvgB,gBAAA,kBAAAugB,GAAA,CAAAvgB,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAgBE;UAEFJ,EAAA,CAAAyD,UAAA,IAAA6hB,mCAAA,iBAaO;UACTtlB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,aAAmC;UAY/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA8D;UAE5DF,EAAA,CAAAyD,UAAA,KAAA8hB,oCAAA,kBAkCM;UAENvlB,EAAA,CAAAyD,UAAA,KAAA+hB,qCAAA,mBAMO;UACTxlB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAGtDF,EAAA,CAAAY,UAAA,mBAAA6kB,uDAAA;YAAA,OAAS1E,GAAA,CAAA7N,cAAA,EAAgB;UAAA,EAAC;UAc1BlT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAA8kB,uDAAA;YAAA,OAAS3E,GAAA,CAAA5N,cAAA,EAAgB;UAAA,EAAC;UAc1BnT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAA+kB,uDAAA;YAAA,OAAS5E,GAAA,CAAA7f,YAAA,EAAc;UAAA,EAAC;UAcxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAeC;UAdCF,EAAA,CAAAY,UAAA,mBAAAglB,uDAAA;YAAA,OAAS7E,GAAA,CAAAzM,cAAA,EAAgB;UAAA,EAAC;UAe1BtU,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAoiB,oCAAA,mBA8EM;UACR7lB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAklB,sDAAA3iB,MAAA;YAAA,OAAU4d,GAAA,CAAA/G,QAAA,CAAA7W,MAAA,CAAgB;UAAA,EAAC,sBAAA4iB,wDAAA5iB,MAAA;YAAA,OACf4d,GAAA,CAAAf,UAAA,CAAA7c,MAAA,CAAkB;UAAA,EADH,uBAAA6iB,yDAAA7iB,MAAA;YAAA,OAEd4d,GAAA,CAAAd,WAAA,CAAA9c,MAAA,CAAmB;UAAA,EAFL,kBAAA8iB,oDAAA9iB,MAAA;YAAA,OAGnB4d,GAAA,CAAAN,MAAA,CAAAtd,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAAyiB,oCAAA,kBAoDM;UAGNlmB,EAAA,CAAAyD,UAAA,KAAA0iB,oCAAA,kBAsBM;UAGNnmB,EAAA,CAAAyD,UAAA,KAAA2iB,oCAAA,kBA0BM;UAGNpmB,EAAA,CAAAyD,UAAA,KAAA4iB,oCAAA,kBA4OM;UACRrmB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAA0lB,wDAAA;YAAA,OAAYvF,GAAA,CAAApP,WAAA,EAAa;UAAA,EAAC;UAI1B3R,EAAA,CAAAE,cAAA,eAAqC;UAIjCF,EAAA,CAAAY,UAAA,mBAAA2lB,uDAAA;YAAA,OAASxF,GAAA,CAAArL,iBAAA,EAAmB;UAAA,EAAC;UAgB7B1V,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAiBC;UAfCF,EAAA,CAAAY,UAAA,mBAAA4lB,uDAAA;YAAA,OAASzF,GAAA,CAAA/K,oBAAA,EAAsB;UAAA,EAAC;UAgBhChW,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAyC;UAIrCF,EAAA,CAAAY,UAAA,qBAAA6lB,2DAAAtjB,MAAA;YAAA,OAAW4d,GAAA,CAAApH,cAAA,CAAAxW,MAAA,CAAsB;UAAA,EAAC,mBAAAujB,yDAAAvjB,MAAA;YAAA,OACzB4d,GAAA,CAAAtH,aAAA,CAAAtW,MAAA,CAAqB;UAAA,EADI,mBAAAwjB,yDAAA;YAAA,OAEzB5F,GAAA,CAAAjH,YAAA,EAAc;UAAA,EAFW;UAoBnC9Z,EAAA,CAAAI,YAAA,EAAW;UAIdJ,EAAA,CAAAE,cAAA,kBA0BC;UACCF,EAAA,CAAAyD,UAAA,KAAAmjB,kCAAA,gBAA4D;UAC5D5mB,EAAA,CAAAyD,UAAA,KAAAojB,oCAAA,kBAUO;UACT7mB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAqjB,oCAAA,kBAkDM;UAGN9mB,EAAA,CAAAyD,UAAA,KAAAsjB,oCAAA,mBAsJM;UAGN/mB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAAomB,uDAAA7jB,MAAA;YAAA,OAAU4d,GAAA,CAAA5B,cAAA,CAAAhc,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAIJJ,EAAA,CAAAyD,UAAA,KAAAwjB,oCAAA,kBAYO;UACTjnB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,8BAQC;UAHCF,EAAA,CAAAY,UAAA,uBAAAsmB,uEAAA;YAAA,OAAanG,GAAA,CAAA3N,OAAA,EAAS;UAAA,EAAC,0BAAA+T,0EAAAhkB,MAAA;YAAA,OACP4d,GAAA,CAAAkB,cAAA,CAAA9e,MAAA,CAAsB;UAAA,EADf,0BAAAikB,0EAAA;YAAA,OAEPrG,GAAA,CAAAmB,cAAA,EAAgB;UAAA,EAFT;UAGxBliB,EAAA,CAAAI,YAAA,EAAqB;;;UA39BZJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAvgB,gBAAA,kBAAAugB,GAAA,CAAAvgB,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA2e,GAAA,CAAAvgB,gBAAA,kBAAAugB,GAAA,CAAAvgB,gBAAA,CAAAc,QAAA;UAkBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAvgB,gBAAA,kBAAAugB,GAAA,CAAAvgB,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAygB,GAAA,CAAAvgB,gBAAA,kBAAAugB,GAAA,CAAAvgB,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAA1V,YAAA,CAAkB;UAmCdrL,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAA6e,GAAA,CAAA1V,YAAA,CAAmB;UA+D5BrL,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAA0e,GAAA,CAAAlY,UAAA,6BAA2D,UAAAkY,GAAA,CAAAlY,UAAA;UAoB3D7I,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAA0e,GAAA,CAAA3f,YAAA,6BAA6D,UAAA2f,GAAA,CAAA3f,YAAA;UAU9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAA3f,YAAA,CAAkB;UAwFrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAA0e,GAAA,CAAArX,UAAA,4CAA0E;UAIvE1J,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAArX,UAAA,CAAgB;UAuDhB1J,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAA1Y,SAAA,CAAe;UAyBfrI,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAA6e,GAAA,CAAA1Y,SAAA,IAAA0Y,GAAA,CAAA/a,QAAA,CAAAoI,MAAA,OAAyC;UA6BzCpO,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAA6e,GAAA,CAAA1Y,SAAA,IAAA0Y,GAAA,CAAA/a,QAAA,CAAAoI,MAAA,KAAuC;UAmPxCpO,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAA6e,GAAA,CAAAvV,WAAA,CAAyB;UAmBrBxL,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAAqC,WAAA,eAAA0e,GAAA,CAAAvY,eAAA,6BAAgE,UAAAuY,GAAA,CAAAvY,eAAA;UAsBhExI,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAqC,WAAA,eAAA0e,GAAA,CAAAtY,kBAAA,6BAAmE,UAAAsY,GAAA,CAAAtY,kBAAA;UAkCnEzI,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAkC,UAAA,aAAA6e,GAAA,CAAAnV,eAAA,GAA8B;UAsBhC5L,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAAqC,WAAA,gBAAA0e,GAAA,CAAAvV,WAAA,CAAAoG,KAAA,IAAAmP,GAAA,CAAAjY,gBAAA,yBAEC,YAAAiY,GAAA,CAAAvV,WAAA,CAAAoG,KAAA,IAAAmP,GAAA,CAAAjY,gBAAA;UAjBD9I,EAAA,CAAAkC,UAAA,cAAA6e,GAAA,CAAAvV,WAAA,CAAAoG,KAAA,IAAAmP,GAAA,CAAAjY,gBAAA,CAAmD;UAyBpB9I,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAA6e,GAAA,CAAAjY,gBAAA,CAAuB;UAEnD9I,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAjY,gBAAA,CAAsB;UAe1B9I,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAvY,eAAA,CAAqB;UAqDrBxI,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAtY,kBAAA,CAAwB;UA6JzBzI,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAA6e,GAAA,CAAAhB,kBAAA,GAA+B;UAOhC/f,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAkC,UAAA,SAAA6e,GAAA,CAAAvY,eAAA,IAAAuY,GAAA,CAAAtY,kBAAA,IAAAsY,GAAA,CAAA3f,YAAA,CAA2D;UAgB9DpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,cAAA6e,GAAA,CAAA1W,QAAA,CAAsB,eAAA0W,GAAA,CAAAtW,UAAA,cAAAsW,GAAA,CAAAzW,QAAA,sBAAAyW,GAAA,CAAAvgB,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}