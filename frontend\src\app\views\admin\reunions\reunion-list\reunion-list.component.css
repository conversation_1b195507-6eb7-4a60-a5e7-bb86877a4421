/* Animations pour la page des réunions */
.page-container {
  overflow: hidden;
}

.page-title {
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  width: 0;
  height: 3px;
  bottom: -5px;
  left: 0;
  background-color: #8b5cf6; /* Violet */
  transition: width 0.6s ease;
}

.page-title:hover::after {
  width: 100%;
}

/* Animation pour les cartes de réunion */
.reunion-card {
  transform: translateY(30px);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.reunion-card.animated {
  transform: translateY(0);
  opacity: 1;
}

/* Animation pour le conteneur vide */
.empty-container {
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.empty-container.animated {
  transform: scale(1);
  opacity: 1;
}

/* Animation pour le spinner de chargement */
.loading-spinner {
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);
  }
}

/* Animation pour l'entrée de la page */
.page-enter {
  animation: fadeInUp 0.8s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation pour la barre de recherche */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* Animation pour les champs de recherche qui apparaissent l'un après l'autre */
@keyframes slideInFromRight {
  0% {
    transform: translateX(30px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation pour les champs de recherche */
.flex-col > div:nth-child(1) {
  animation: slideInFromRight 0.4s ease-out forwards;
}

.flex-col > div:nth-child(2) {
  animation: slideInFromRight 0.4s ease-out 0.1s forwards;
  opacity: 0;
}

/* Animation pour les inputs */
input, select {
  transition: all 0.3s ease;
}

input:focus, select:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.1), 0 2px 4px -1px rgba(139, 92, 246, 0.06);
}

/* Animation pour le bouton de recherche */
@keyframes gentle-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(167, 139, 250, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0);
  }
}

.search-button {
  animation: gentle-pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animations pour les champs de recherche */
.search-input-container {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-input-container:hover {
  transform: translateY(-2px);
}

.search-input {
  position: relative;
  z-index: 1;
  background: transparent;
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
}

/* Animation de l'effet d'onde */
.search-input-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(139, 92, 246, 0.1),
    transparent
  );
  transition: all 0.6s ease;
  z-index: 0;
}

.search-input-container:hover::before {
  left: 100%;
  transition: all 0.6s ease;
}

/* Animation de pulsation subtile */
@keyframes subtlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(139, 92, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);
  }
}

.search-input-container:focus-within {
  animation: subtlePulse 2s infinite;
  border-color: #8b5cf6;
}

/* Animation de l'icône de recherche */
@keyframes rotateIcon {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.search-icon {
  transition: all 0.3s ease;
}

.search-input-container:focus-within .search-icon {
  animation: rotateIcon 1s ease;
  color: #8b5cf6;
}

/* Animation du placeholder */
.search-input::placeholder {
  transition: all 0.3s ease;
}

.search-input:focus::placeholder {
  opacity: 0.5;
  transform: translateX(10px);
}

/* Animation du label flottant */
.floating-label {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #9ca3af;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 2;
}

.search-input:focus ~ .floating-label,
.search-input:not(:placeholder-shown) ~ .floating-label {
  top: 0;
  left: 8px;
  font-size: 12px;
  padding: 0 4px;
  background-color: white;
  color: #8b5cf6;
  transform: translateY(-50%);
}

/* Animation pour le select */
.search-select {
  position: relative;
  transition: all 0.3s ease;
  background-image: linear-gradient(to right, #f9fafb 0%, white 100%);
}

.search-select:hover {
  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);
}

.search-select:focus {
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);
}

/* Animation pour les éléments de la liste */
.staggered-item {
  opacity: 0;
  transform: translateY(20px);
}

.staggered-item.animated {
  animation: fadeInStaggered 0.5s forwards;
}

@keyframes fadeInStaggered {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}