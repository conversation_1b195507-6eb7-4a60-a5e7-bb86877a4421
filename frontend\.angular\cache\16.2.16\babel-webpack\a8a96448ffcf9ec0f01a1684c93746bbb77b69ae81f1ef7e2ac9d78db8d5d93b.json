{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/toast.service\";\nimport * as i2 from \"@angular/common\";\nfunction ToastContainerComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const toast_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", toast_r1.title, \" \");\n  }\n}\nfunction ToastContainerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, ToastContainerComponent_div_1_div_5_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ToastContainerComponent_div_1_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const toast_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.removeToast(toast_r1.id));\n    });\n    i0.ɵɵelement(9, \"i\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const toast_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getToastClass(toast_r1.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getToastIcon(toast_r1.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", toast_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", toast_r1.message, \" \");\n  }\n}\nexport class ToastContainerComponent {\n  constructor(toastService) {\n    this.toastService = toastService;\n    this.toasts = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.toastService.toasts$.pipe(takeUntil(this.destroy$)).subscribe(toasts => {\n      this.toasts = toasts;\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  removeToast(id) {\n    this.toastService.dismiss(id);\n  }\n  getToastIcon(type) {\n    switch (type) {\n      case 'success':\n        return 'fas fa-check-circle';\n      case 'error':\n        return 'fas fa-times-circle';\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n        return 'fas fa-info-circle';\n      default:\n        return 'fas fa-info-circle';\n    }\n  }\n  getToastClass(type) {\n    switch (type) {\n      case 'success':\n        return 'toast-success';\n      case 'error':\n        return 'toast-error';\n      case 'warning':\n        return 'toast-warning';\n      case 'info':\n        return 'toast-info';\n      default:\n        return 'toast-info';\n    }\n  }\n  trackByToastId(index, toast) {\n    return toast.id;\n  }\n  static {\n    this.ɵfac = function ToastContainerComponent_Factory(t) {\n      return new (t || ToastContainerComponent)(i0.ɵɵdirectiveInject(i1.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ToastContainerComponent,\n      selectors: [[\"app-toast-container\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"fixed\", \"top-4\", \"right-4\", \"z-50\", \"space-y-2\"], [\"class\", \"toast-item transform transition-all duration-300 ease-in-out\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"toast-item\", \"transform\", \"transition-all\", \"duration-300\", \"ease-in-out\", 3, \"ngClass\"], [1, \"flex\", \"items-start\", \"p-4\", \"rounded-lg\", \"shadow-lg\", \"backdrop-blur-sm\", \"border\", \"max-w-sm\"], [1, \"flex-shrink-0\", \"mr-3\"], [1, \"text-lg\", 3, \"ngClass\"], [1, \"flex-1\", \"min-w-0\"], [\"class\", \"text-sm font-medium mb-1\", 4, \"ngIf\"], [1, \"text-sm\"], [\"type\", \"button\", 1, \"flex-shrink-0\", \"ml-3\", \"text-sm\", \"opacity-70\", \"hover:opacity-100\", \"transition-opacity\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"text-sm\", \"font-medium\", \"mb-1\"]],\n      template: function ToastContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ToastContainerComponent_div_1_Template, 10, 4, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.toasts)(\"ngForTrackBy\", ctx.trackByToastId);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf],\n      styles: [\"\\n\\n.toast-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.3s ease-out;\\n}\\n\\n.toast-item.removing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideOutRight 0.3s ease-in;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideOutRight {\\n  from {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n  to {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n}\\n\\n\\n\\n.toast-success[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.toast-success[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\\n  background-color: rgb(20 83 45 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-error[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-error[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\\n  background-color: rgb(127 29 29 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-error[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-error[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-warning[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-warning[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\\n  background-color: rgb(113 63 18 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-info[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-info[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\\n  background-color: rgb(30 58 138 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n\\n.toast-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n.toast-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n\\n\\n.toast-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  padding: 0.25rem;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.toast-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: rgb(0 0 0 / 0.1);\\n}\\n.toast-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:is(.dark   *)[_ngcontent-%COMP%] {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "toast_r1", "title", "ɵɵelement", "ɵɵtemplate", "ToastContainerComponent_div_1_div_5_Template", "ɵɵlistener", "ToastContainerComponent_div_1_Template_button_click_8_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "removeToast", "id", "ɵɵproperty", "ctx_r0", "getToastClass", "type", "getToastIcon", "message", "ToastContainerComponent", "constructor", "toastService", "toasts", "destroy$", "ngOnInit", "toasts$", "pipe", "subscribe", "ngOnDestroy", "next", "complete", "dismiss", "trackByToastId", "index", "toast", "ɵɵdirectiveInject", "i1", "ToastService", "selectors", "decls", "vars", "consts", "template", "ToastContainerComponent_Template", "rf", "ctx", "ToastContainerComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\shared\\components\\toast-container\\toast-container.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\shared\\components\\toast-container\\toast-container.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { ToastService } from '../../../services/toast.service';\nimport { Toast } from '../../../models/message.model';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-toast-container',\n  templateUrl: './toast-container.component.html',\n  styleUrls: ['./toast-container.component.css']\n})\nexport class ToastContainerComponent implements OnInit, OnDestroy {\n  toasts: Toast[] = [];\n  private destroy$ = new Subject<void>();\n\n  constructor(private toastService: ToastService) {}\n\n  ngOnInit(): void {\n    this.toastService.toasts$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(toasts => {\n        this.toasts = toasts;\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  removeToast(id: string): void {\n    this.toastService.dismiss(id);\n  }\n\n  getToastIcon(type: string): string {\n    switch (type) {\n      case 'success':\n        return 'fas fa-check-circle';\n      case 'error':\n        return 'fas fa-times-circle';\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n        return 'fas fa-info-circle';\n      default:\n        return 'fas fa-info-circle';\n    }\n  }\n\n  getToastClass(type: string): string {\n    switch (type) {\n      case 'success':\n        return 'toast-success';\n      case 'error':\n        return 'toast-error';\n      case 'warning':\n        return 'toast-warning';\n      case 'info':\n        return 'toast-info';\n      default:\n        return 'toast-info';\n    }\n  }\n\n  trackByToastId(index: number, toast: Toast): string {\n    return toast.id;\n  }\n}\n", "<!-- Toast Container -->\n<div class=\"fixed top-4 right-4 z-50 space-y-2\">\n  <div\n    *ngFor=\"let toast of toasts; trackBy: trackByToastId\"\n    class=\"toast-item transform transition-all duration-300 ease-in-out\"\n    [ngClass]=\"getToastClass(toast.type)\"\n  >\n    <div class=\"flex items-start p-4 rounded-lg shadow-lg backdrop-blur-sm border max-w-sm\">\n      <!-- Icon -->\n      <div class=\"flex-shrink-0 mr-3\">\n        <i [ngClass]=\"getToastIcon(toast.type)\" class=\"text-lg\"></i>\n      </div>\n      \n      <!-- Content -->\n      <div class=\"flex-1 min-w-0\">\n        <div *ngIf=\"toast.title\" class=\"text-sm font-medium mb-1\">\n          {{ toast.title }}\n        </div>\n        <div class=\"text-sm\">\n          {{ toast.message }}\n        </div>\n      </div>\n      \n      <!-- Close button -->\n      <button\n        type=\"button\"\n        (click)=\"removeToast(toast.id)\"\n        class=\"flex-shrink-0 ml-3 text-sm opacity-70 hover:opacity-100 transition-opacity\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;ICWlCC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,QAAA,CAAAC,KAAA,MACF;;;;;;IAfNP,EAAA,CAAAC,cAAA,aAIC;IAIKD,EAAA,CAAAQ,SAAA,WAA4D;IAC9DR,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAS,UAAA,IAAAC,4CAAA,iBAEM;IACNV,EAAA,CAAAC,cAAA,aAAqB;IACnBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAIC;IAFCD,EAAA,CAAAW,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAT,QAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAd,QAAA,CAAAe,EAAA,CAAqB;IAAA,EAAC;IAG/BrB,EAAA,CAAAQ,SAAA,YAA4B;IAC9BR,EAAA,CAAAG,YAAA,EAAS;;;;;IAzBXH,EAAA,CAAAsB,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAAlB,QAAA,CAAAmB,IAAA,EAAqC;IAK9BzB,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAsB,UAAA,YAAAC,MAAA,CAAAG,YAAA,CAAApB,QAAA,CAAAmB,IAAA,EAAoC;IAKjCzB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAsB,UAAA,SAAAhB,QAAA,CAAAC,KAAA,CAAiB;IAIrBP,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,QAAA,CAAAqB,OAAA,MACF;;;ADTR,OAAM,MAAOC,uBAAuB;EAIlCC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAHhC,KAAAC,MAAM,GAAY,EAAE;IACZ,KAAAC,QAAQ,GAAG,IAAIlC,OAAO,EAAQ;EAEW;EAEjDmC,QAAQA,CAAA;IACN,IAAI,CAACH,YAAY,CAACI,OAAO,CACtBC,IAAI,CAACpC,SAAS,CAAC,IAAI,CAACiC,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAACL,MAAM,IAAG;MAClB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC,CAAC;EACN;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACL,QAAQ,CAACM,IAAI,EAAE;IACpB,IAAI,CAACN,QAAQ,CAACO,QAAQ,EAAE;EAC1B;EAEAnB,WAAWA,CAACC,EAAU;IACpB,IAAI,CAACS,YAAY,CAACU,OAAO,CAACnB,EAAE,CAAC;EAC/B;EAEAK,YAAYA,CAACD,IAAY;IACvB,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,qBAAqB;MAC9B,KAAK,OAAO;QACV,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC,KAAK,MAAM;QACT,OAAO,oBAAoB;MAC7B;QACE,OAAO,oBAAoB;;EAEjC;EAEAD,aAAaA,CAACC,IAAY;IACxB,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,eAAe;MACxB,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,SAAS;QACZ,OAAO,eAAe;MACxB,KAAK,MAAM;QACT,OAAO,YAAY;MACrB;QACE,OAAO,YAAY;;EAEzB;EAEAgB,cAAcA,CAACC,KAAa,EAAEC,KAAY;IACxC,OAAOA,KAAK,CAACtB,EAAE;EACjB;;;uBAvDWO,uBAAuB,EAAA5B,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAvBlB,uBAAuB;MAAAmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpCrD,EAAA,CAAAC,cAAA,aAAgD;UAC9CD,EAAA,CAAAS,UAAA,IAAA8C,sCAAA,kBA8BM;UACRvD,EAAA,CAAAG,YAAA,EAAM;;;UA9BgBH,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAsB,UAAA,YAAAgC,GAAA,CAAAvB,MAAA,CAAW,iBAAAuB,GAAA,CAAAb,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}