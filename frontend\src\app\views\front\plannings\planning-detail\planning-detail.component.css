/* Styles pour la page de détail du planning */

/* Animation d'entrée pour les sections */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation de pulse pour les boutons */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
  }
}

/* Animation de rotation pour l'icône de chargement */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Style pour le conteneur principal */
.container {
  max-width: 1200px;
  animation: fadeInUp 0.5s ease-out;
}

/* Style pour la carte principale */
.planning-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.planning-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #7c3aed, #4f46e5, #3b82f6);
}

/* Style pour l'en-tête du planning */
.planning-header {
  padding: 2rem;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.05) 0%, rgba(79, 70, 229, 0.1) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.planning-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  position: relative;
  display: inline-block;
}

.planning-header h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #7c3aed, #4f46e5);
  transition: width 0.3s ease;
}

.planning-header h1:hover::after {
  width: 100%;
}

.planning-header p {
  color: #4a5568;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Style pour les sections */
.planning-section {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
}

.planning-section:nth-child(2) {
  animation-delay: 0.1s;
}

.planning-section:nth-child(3) {
  animation-delay: 0.2s;
}

.planning-section:nth-child(4) {
  animation-delay: 0.3s;
}

.planning-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.planning-section h2 svg {
  margin-right: 0.5rem;
  color: #7c3aed;
}

/* Style pour les informations */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.info-item:hover {
  background-color: rgba(124, 58, 237, 0.05);
}

.info-item svg {
  color: #7c3aed;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.info-item span {
  color: #4a5568;
  font-size: 1rem;
}

.info-item strong {
  color: #2d3748;
  font-weight: 600;
}

/* Style pour les participants */
.participants-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.participant-badge {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border: 1px solid #e5e7eb;
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.participant-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.participant-badge span {
  color: #4b5563;
  font-weight: 500;
}

/* Style pour les boutons */
.btn {
  padding: 0.625rem 1.25rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
  z-index: -1;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(109, 40, 217, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Style pour le calendrier */
.calendar-container {
  margin-top: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Style pour les événements du jour sélectionné */
.day-events {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  animation: fadeInUp 0.4s ease-out;
}

.day-events h3 {
  color: #4b5563;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(124, 58, 237, 0.2);
}

.event-item {
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #7c3aed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.event-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.event-item strong {
  display: block;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.event-item div {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Style pour le bouton retour */
.back-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: #7c3aed;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(124, 58, 237, 0.05);
  color: #6d28d9;
  transform: translateX(-5px);
}

.back-button svg {
  margin-right: 0.5rem;
}

/* Style pour le spinner de chargement */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(124, 58, 237, 0.1);
  border-radius: 50%;
  border-top-color: #7c3aed;
  animation: rotate 1s linear infinite;
  margin: 2rem auto;
}