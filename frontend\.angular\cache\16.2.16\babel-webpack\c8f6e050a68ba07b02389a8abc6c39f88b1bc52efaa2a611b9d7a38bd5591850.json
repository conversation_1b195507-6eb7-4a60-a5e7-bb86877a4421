{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, catchError, of, tap, throwError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nimport * as i3 from \"./authuser.service\";\nexport class DataService {\n  constructor(http, jwtHelper, authService) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n    this.authService = authService;\n    this.usersCache$ = new BehaviorSubject([]);\n    this.lastFetchTime = 0;\n    this.CACHE_DURATION = 300000;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.userAddedSubject = new BehaviorSubject(null);\n    this.userAdded$ = this.userAddedSubject.asObservable();\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.initializeCurrentUser();\n  }\n  fetchCurrentUser() {\n    return this.getProfile().pipe(tap(user => this.currentUserSubject.next(user)));\n  }\n  getAdminHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token}`,\n      role: 'admin',\n      'Content-Type': 'application/json'\n    });\n  }\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getCommonParams() {\n    return new HttpParams().set('secret', environment.secret).set('client', environment.client);\n  }\n  syncCurrentUser() {\n    return this.getProfile().pipe(tap(user => {\n      this.currentUserSubject.next(user);\n      this.authService.setCurrentUser(user);\n    }), catchError(error => {\n      // If fetch fails, try to get from auth service\n      const authUser = this.authService.getCurrentUser();\n      if (authUser) {\n        this.currentUserSubject.next(authUser);\n        return of(authUser);\n      }\n      return throwError(() => error);\n    }));\n  }\n  getProfile() {\n    return this.http.get(`${environment.urlBackend}users/profile`, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(catchError(this.handleError));\n  }\n  initializeCurrentUser() {\n    const token = localStorage.getItem('token');\n    if (token && !this.jwtHelper.isTokenExpired(token)) {\n      this.syncCurrentUser().subscribe({\n        error: () => {\n          const decodedToken = this.jwtHelper.decodeToken(token);\n          // Déterminer l'image de profil à utiliser\n          let profileImage = 'assets/images/default-profile.png';\n          // Vérifier d'abord profileImage\n          if (decodedToken.profileImage && decodedToken.profileImage !== 'null' && decodedToken.profileImage !== 'undefined' && decodedToken.profileImage.trim() !== '') {\n            profileImage = decodedToken.profileImage;\n          }\n          // Ensuite vérifier image si profileImage n'est pas valide\n          else if (decodedToken.image && decodedToken.image !== 'null' && decodedToken.image !== 'undefined' && decodedToken.image.trim() !== '') {\n            profileImage = decodedToken.image;\n          }\n          console.log('DataService - Using profile image:', profileImage);\n          const fallbackUser = {\n            _id: decodedToken.id,\n            username: decodedToken.username,\n            email: decodedToken.email,\n            role: decodedToken.role,\n            image: profileImage,\n            profileImage: profileImage,\n            isActive: true\n          };\n          this.currentUserSubject.next(fallbackUser);\n          this.authService.setCurrentUser(fallbackUser);\n        }\n      });\n    }\n  }\n  updateCurrentUser(userData) {\n    const currentUser = this.currentUserSubject.value;\n    if (currentUser) {\n      this.currentUserSubject.next({\n        ...currentUser,\n        ...userData\n      });\n    }\n  }\n  updateSelf(userId, updateData) {\n    return this.http.put(`${environment.urlBackend}users/updateself/${userId}`, updateData, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => {\n      this.updateUserInCache(updatedUser);\n      this.updateCurrentUser(updatedUser);\n    }), catchError(this.handleError));\n  }\n  changePassword(currentPassword, newPassword) {\n    const userId = this.currentUserValue?._id;\n    if (!userId) {\n      return throwError(() => new Error('User not logged in'));\n    }\n    const passwordData = {\n      currentPassword,\n      newPassword\n    };\n    return this.http.put(`${environment.urlBackend}users/updateself/${userId}`, passwordData, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(response => {\n      if (response.token) {\n        localStorage.setItem('token', response.token);\n        const decodedToken = this.jwtHelper.decodeToken(response.token);\n        this.updateCurrentUser(decodedToken);\n      }\n    }), catchError(error => {\n      if (error.status === 400) {\n        if (error.error?.errors?.general) {\n          return throwError(() => new Error(error.error.errors.general));\n        }\n        return throwError(() => new Error(error.error?.message || 'Validation failed'));\n      }\n      return throwError(() => new Error('Failed to change password'));\n    }));\n  }\n  uploadProfileImage(file) {\n    const formData = new FormData();\n    formData.append('image', file);\n    return this.http.post(`${environment.urlBackend}users/upload-profile-image`, formData, {\n      headers: new HttpHeaders({\n        Authorization: `Bearer ${localStorage.getItem('token')}`\n      }),\n      params: this.getCommonParams()\n    }).pipe(tap(response => {\n      if (response.token) {\n        localStorage.setItem('token', response.token);\n        const decodedToken = this.jwtHelper.decodeToken(response.token);\n        this.updateCurrentUser({\n          image: response.imageUrl,\n          ...decodedToken\n        });\n      }\n    }), catchError(this.handleError));\n  }\n  removeProfileImage() {\n    return this.http.delete(`${environment.urlBackend}users/remove-profile-image`, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(response => {\n      if (response.token) {\n        localStorage.setItem('token', response.token);\n        let decodeToken = this.jwtHelper.decodeToken(response.token);\n        this.updateCurrentUser(decodeToken);\n      }\n    }), catchError(this.handleError));\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  isAdmin() {\n    return this.currentUserValue?.role === 'admin';\n  }\n  isCurrentUser(userId) {\n    return this.currentUserValue?._id === userId;\n  }\n  updateUserInCache(updatedUser) {\n    const updatedUsers = this.usersCache$.value.map(u => u._id === updatedUser._id ? {\n      ...u,\n      ...updatedUser\n    } : u);\n    this.usersCache$.next(updatedUsers);\n  }\n  refreshUserCache() {\n    this.lastFetchTime = 0;\n    this.getAllUsers(true).subscribe();\n  }\n  getAllUsers(forceRefresh = false) {\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache$.value.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION;\n    if (cacheValid) {\n      return this.usersCache$.asObservable();\n    }\n    this.lastFetchTime = now;\n    return this.http.get(`${environment.urlBackend}users/getall`, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(users => this.usersCache$.next([...users])), catchError(this.handleError));\n  }\n  getOneUser(id) {\n    return this.http.get(`${environment.urlBackend}users/getone/${id}`, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(catchError(this.handleError));\n  }\n  addUser(userData) {\n    return this.http.post(`${environment.urlBackend}users/add`, userData, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(newUser => {\n      const currentUsers = this.usersCache$.value;\n      this.usersCache$.next([...currentUsers, newUser]);\n      this.userAddedSubject.next();\n    }), catchError(this.handleError));\n  }\n  deleteUser(id) {\n    return this.http.delete(`${environment.urlBackend}users/delete/${id}`, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(() => {\n      const updatedUsers = this.usersCache$.value.filter(u => u._id !== id);\n      this.usersCache$.next(updatedUsers);\n    }), catchError(this.handleError));\n  }\n  updateUserByAdmin(id, data) {\n    return this.http.put(`${environment.urlBackend}users/update/${id}`, data, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n  }\n  deactivateUser(id) {\n    return this.http.put(`${environment.urlBackend}users/update/${id}/deactivate`, {}, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n  }\n  reactivateUser(id) {\n    return this.http.put(`${environment.urlBackend}users/update/${id}/reactivate`, {}, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n  }\n  updateUserRole(id, role) {\n    return this.http.put(`${environment.urlBackend}admin/users/${id}/role`, {\n      role\n    }, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n  }\n  toggleUserActivation(id, isActive) {\n    return this.http.put(`${environment.urlBackend}admin/users/${id}/activation`, {\n      isActive\n    }, {\n      headers: this.getAdminHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue';\n    if (error.status === 0) {\n      errorMessage = 'Erreur réseau - impossible de contacter le serveur';\n    } else if (error.status >= 400 && error.status < 500) {\n      errorMessage = error.error?.message || error.message;\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur serveur - veuillez réessayer plus tard';\n    }\n    console.error(`Erreur ${error.status}:`, error.error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function DataService_Factory(t) {\n      return new (t || DataService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService), i0.ɵɵinject(i3.AuthuserService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DataService,\n      factory: DataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "BehaviorSubject", "catchError", "of", "tap", "throwError", "environment", "DataService", "constructor", "http", "jwtHelper", "authService", "usersCache$", "lastFetchTime", "CACHE_DURATION", "currentUserSubject", "userAddedSubject", "userAdded$", "asObservable", "currentUser$", "initializeCurrentUser", "fetchCurrentUser", "getProfile", "pipe", "user", "next", "getAdminHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "role", "getUserHeaders", "getCommonParams", "set", "secret", "client", "syncCurrentUser", "setCurrentUser", "error", "authUser", "getCurrentUser", "get", "urlBackend", "headers", "params", "handleError", "subscribe", "decodedToken", "decodeToken", "profileImage", "trim", "image", "console", "log", "fallbackUser", "_id", "id", "username", "email", "isActive", "updateCurrentUser", "userData", "currentUser", "value", "updateSelf", "userId", "updateData", "put", "updatedUser", "updateUserInCache", "changePassword", "currentPassword", "newPassword", "currentUserValue", "passwordData", "response", "setItem", "status", "errors", "general", "message", "uploadProfileImage", "file", "formData", "FormData", "append", "post", "imageUrl", "removeProfileImage", "delete", "isAdmin", "isCurrentUser", "updatedUsers", "map", "u", "refreshUserCache", "getAllUsers", "forceRefresh", "now", "Date", "cacheValid", "length", "users", "getOneUser", "addUser", "newUser", "currentUsers", "deleteUser", "filter", "updateUserByAdmin", "data", "deactivateUser", "reactivateUser", "updateUserRole", "toggleUserActivation", "errorMessage", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "i3", "AuthuserService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\data.service.ts"], "sourcesContent": ["import {\r\n  HttpClient,\r\n  HttpErrorResponse,\r\n  HttpHeaders,\r\n  HttpParams,\r\n} from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport {\r\n  BehaviorSubject,\r\n  Observable,\r\n  catchError,\r\n  of,\r\n  tap,\r\n  throwError,\r\n} from 'rxjs';\r\nimport { User } from '../models/user.model';\r\nimport { environment } from 'src/environments/environment';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\nimport { AuthuserService } from './authuser.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class DataService {\r\n  private usersCache$ = new BehaviorSubject<User[]>([]);\r\n  private lastFetchTime: number = 0;\r\n  private readonly CACHE_DURATION = 300000;\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  private userAddedSubject = new BehaviorSubject<void | null>(null);\r\n  public userAdded$ = this.userAddedSubject.asObservable();\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  constructor(\r\n    private http: HttpClient,\r\n    private jwtHelper: JwtHelperService,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.initializeCurrentUser();\r\n  }\r\n  fetchCurrentUser(): Observable<User> {\r\n    return this.getProfile().pipe(\r\n      tap((user) => this.currentUserSubject.next(user))\r\n    );\r\n  }\r\n  private getAdminHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n      throw new Error('Token invalide ou expiré');\r\n    }\r\n    return new HttpHeaders({\r\n      Authorization: `Bearer ${token}`,\r\n      role: 'admin',\r\n      'Content-Type': 'application/json',\r\n    });\r\n  }\r\n\r\n  private getUserHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n      throw new Error('Token invalide ou expiré');\r\n    }\r\n    return new HttpHeaders({\r\n      Authorization: `Bearer ${token || ''}`,\r\n      'Content-Type': 'application/json',\r\n    });\r\n  }\r\n  private getCommonParams(): HttpParams {\r\n    return new HttpParams()\r\n      .set('secret', environment.secret)\r\n      .set('client', environment.client);\r\n  }\r\n  syncCurrentUser(): Observable<User> {\r\n    return this.getProfile().pipe(\r\n      tap((user) => {\r\n        this.currentUserSubject.next(user);\r\n        this.authService.setCurrentUser(user);\r\n      }),\r\n      catchError((error) => {\r\n        // If fetch fails, try to get from auth service\r\n        const authUser = this.authService.getCurrentUser();\r\n        if (authUser) {\r\n          this.currentUserSubject.next(authUser);\r\n          return of(authUser);\r\n        }\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n  getProfile(): Observable<User> {\r\n    return this.http\r\n      .get<User>(`${environment.urlBackend}users/profile`, {\r\n        headers: this.getUserHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n  private initializeCurrentUser(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (token && !this.jwtHelper.isTokenExpired(token)) {\r\n      this.syncCurrentUser().subscribe({\r\n        error: () => {\r\n          const decodedToken = this.jwtHelper.decodeToken(token);\r\n\r\n          // Déterminer l'image de profil à utiliser\r\n          let profileImage = 'assets/images/default-profile.png';\r\n\r\n          // Vérifier d'abord profileImage\r\n          if (\r\n            decodedToken.profileImage &&\r\n            decodedToken.profileImage !== 'null' &&\r\n            decodedToken.profileImage !== 'undefined' &&\r\n            decodedToken.profileImage.trim() !== ''\r\n          ) {\r\n            profileImage = decodedToken.profileImage;\r\n          }\r\n          // Ensuite vérifier image si profileImage n'est pas valide\r\n          else if (\r\n            decodedToken.image &&\r\n            decodedToken.image !== 'null' &&\r\n            decodedToken.image !== 'undefined' &&\r\n            decodedToken.image.trim() !== ''\r\n          ) {\r\n            profileImage = decodedToken.image;\r\n          }\r\n\r\n          console.log('DataService - Using profile image:', profileImage);\r\n\r\n          const fallbackUser = {\r\n            _id: decodedToken.id,\r\n            username: decodedToken.username,\r\n            email: decodedToken.email,\r\n            role: decodedToken.role,\r\n            image: profileImage,\r\n            profileImage: profileImage,\r\n            isActive: true,\r\n          };\r\n\r\n          this.currentUserSubject.next(fallbackUser);\r\n          this.authService.setCurrentUser(fallbackUser);\r\n        },\r\n      });\r\n    }\r\n  }\r\n  updateCurrentUser(userData: Partial<User>): void {\r\n    const currentUser = this.currentUserSubject.value;\r\n    if (currentUser) {\r\n      this.currentUserSubject.next({ ...currentUser, ...userData });\r\n    }\r\n  }\r\n\r\n  updateSelf(userId: string, updateData: any): Observable<any> {\r\n    return this.http\r\n      .put<User>(\r\n        `${environment.urlBackend}users/updateself/${userId}`,\r\n        updateData,\r\n        {\r\n          headers: this.getUserHeaders(),\r\n          params: this.getCommonParams(),\r\n        }\r\n      )\r\n      .pipe(\r\n        tap((updatedUser) => {\r\n          this.updateUserInCache(updatedUser);\r\n          this.updateCurrentUser(updatedUser);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  changePassword(\r\n    currentPassword: string,\r\n    newPassword: string\r\n  ): Observable<any> {\r\n    const userId = this.currentUserValue?._id;\r\n    if (!userId) {\r\n      return throwError(() => new Error('User not logged in'));\r\n    }\r\n    const passwordData = {\r\n      currentPassword,\r\n      newPassword,\r\n    };\r\n    return this.http\r\n      .put<any>(\r\n        `${environment.urlBackend}users/updateself/${userId}`,\r\n        passwordData,\r\n        {\r\n          headers: this.getUserHeaders(),\r\n          params: this.getCommonParams(),\r\n        }\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n            const decodedToken = this.jwtHelper.decodeToken(response.token);\r\n            this.updateCurrentUser(decodedToken);\r\n          }\r\n        }),\r\n        catchError((error: HttpErrorResponse) => {\r\n          if (error.status === 400) {\r\n            if (error.error?.errors?.general) {\r\n              return throwError(() => new Error(error.error.errors.general));\r\n            }\r\n            return throwError(\r\n              () => new Error(error.error?.message || 'Validation failed')\r\n            );\r\n          }\r\n          return throwError(() => new Error('Failed to change password'));\r\n        })\r\n      );\r\n  }\r\n  uploadProfileImage(\r\n    file: File\r\n  ): Observable<{ imageUrl: string; token?: string }> {\r\n    const formData = new FormData();\r\n    formData.append('image', file);\r\n\r\n    return this.http\r\n      .post<{ imageUrl: string; token?: string }>(\r\n        `${environment.urlBackend}users/upload-profile-image`,\r\n        formData,\r\n        {\r\n          headers: new HttpHeaders({\r\n            Authorization: `Bearer ${localStorage.getItem('token')}`,\r\n          }),\r\n          params: this.getCommonParams(),\r\n        }\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n            const decodedToken = this.jwtHelper.decodeToken(response.token);\r\n            this.updateCurrentUser({\r\n              image: response.imageUrl,\r\n              ...decodedToken,\r\n            });\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  removeProfileImage(): Observable<{ message: string; token: string }> {\r\n    return this.http\r\n      .delete<{ message: string; token: string }>(\r\n        `${environment.urlBackend}users/remove-profile-image`,\r\n        {\r\n          headers: this.getUserHeaders(),\r\n          params: this.getCommonParams(),\r\n        }\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n            let decodeToken = this.jwtHelper.decodeToken(response.token);\r\n            this.updateCurrentUser(decodeToken);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  get currentUserValue(): User | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  isAdmin(): boolean {\r\n    return this.currentUserValue?.role === 'admin';\r\n  }\r\n  isCurrentUser(userId: string): boolean {\r\n    return this.currentUserValue?._id === userId;\r\n  }\r\n  private updateUserInCache(updatedUser: User): void {\r\n    const updatedUsers = this.usersCache$.value.map((u) =>\r\n      u._id === updatedUser._id ? { ...u, ...updatedUser } : u\r\n    );\r\n    this.usersCache$.next(updatedUsers);\r\n  }\r\n  refreshUserCache(): void {\r\n    this.lastFetchTime = 0;\r\n    this.getAllUsers(true).subscribe();\r\n  }\r\n  getAllUsers(forceRefresh = false): Observable<User[]> {\r\n    const now = Date.now();\r\n    const cacheValid =\r\n      !forceRefresh &&\r\n      this.usersCache$.value.length > 0 &&\r\n      now - this.lastFetchTime <= this.CACHE_DURATION;\r\n\r\n    if (cacheValid) {\r\n      return this.usersCache$.asObservable();\r\n    }\r\n\r\n    this.lastFetchTime = now;\r\n    return this.http\r\n      .get<User[]>(`${environment.urlBackend}users/getall`, {\r\n        headers: this.getAdminHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(\r\n        tap((users) => this.usersCache$.next([...users])),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  getOneUser(id: string): Observable<User> {\r\n    return this.http\r\n      .get<User>(`${environment.urlBackend}users/getone/${id}`, {\r\n        headers: this.getAdminHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n  addUser(userData: User): Observable<User> {\r\n    return this.http\r\n      .post<User>(`${environment.urlBackend}users/add`, userData, {\r\n        headers: this.getAdminHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(\r\n        tap((newUser) => {\r\n          const currentUsers = this.usersCache$.value;\r\n          this.usersCache$.next([...currentUsers, newUser]);\r\n          this.userAddedSubject.next();\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  deleteUser(id: string): Observable<void> {\r\n    return this.http\r\n      .delete<void>(`${environment.urlBackend}users/delete/${id}`, {\r\n        headers: this.getAdminHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(\r\n        tap(() => {\r\n          const updatedUsers = this.usersCache$.value.filter(\r\n            (u) => u._id !== id\r\n          );\r\n          this.usersCache$.next(updatedUsers);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  updateUserByAdmin(id: string, data: Partial<User>): Observable<User> {\r\n    return this.http\r\n      .put<User>(`${environment.urlBackend}users/update/${id}`, data, {\r\n        headers: this.getAdminHeaders(),\r\n        params: this.getCommonParams(),\r\n      })\r\n      .pipe(\r\n        tap((updatedUser) => this.updateUserInCache(updatedUser)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  deactivateUser(id: string): Observable<User> {\r\n    return this.http\r\n      .put<User>(\r\n        `${environment.urlBackend}users/update/${id}/deactivate`,\r\n        {},\r\n        { headers: this.getAdminHeaders(), params: this.getCommonParams() }\r\n      )\r\n      .pipe(\r\n        tap((updatedUser) => this.updateUserInCache(updatedUser)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  reactivateUser(id: string): Observable<User> {\r\n    return this.http\r\n      .put<User>(\r\n        `${environment.urlBackend}users/update/${id}/reactivate`,\r\n        {},\r\n        { headers: this.getAdminHeaders(), params: this.getCommonParams() }\r\n      )\r\n      .pipe(\r\n        tap((updatedUser) => this.updateUserInCache(updatedUser)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  updateUserRole(id: string, role: string): Observable<any> {\r\n    return this.http\r\n      .put<any>(\r\n        `${environment.urlBackend}admin/users/${id}/role`,\r\n        { role },\r\n        { headers: this.getAdminHeaders(), params: this.getCommonParams() }\r\n      )\r\n      .pipe(\r\n        tap((updatedUser) => this.updateUserInCache(updatedUser)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  toggleUserActivation(id: string, isActive: boolean): Observable<any> {\r\n    return this.http\r\n      .put<any>(\r\n        `${environment.urlBackend}admin/users/${id}/activation`,\r\n        { isActive },\r\n        { headers: this.getAdminHeaders(), params: this.getCommonParams() }\r\n      )\r\n      .pipe(\r\n        tap((updatedUser) => this.updateUserInCache(updatedUser)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n  private handleError(error: HttpErrorResponse): Observable<never> {\r\n    let errorMessage = 'Une erreur est survenue';\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Erreur réseau - impossible de contacter le serveur';\r\n    } else if (error.status >= 400 && error.status < 500) {\r\n      errorMessage = error.error?.message || error.message;\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Erreur serveur - veuillez réessayer plus tard';\r\n    }\r\n\r\n    console.error(`Erreur ${error.status}:`, error.error);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAGEA,WAAW,EACXC,UAAU,QACL,sBAAsB;AAE7B,SACEC,eAAe,EAEfC,UAAU,EACVC,EAAE,EACFC,GAAG,EACHC,UAAU,QACL,MAAM;AAEb,SAASC,WAAW,QAAQ,8BAA8B;;;;;AAO1D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,SAA2B,EAC3BC,WAA4B;IAF5B,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IAVb,KAAAC,WAAW,GAAG,IAAIX,eAAe,CAAS,EAAE,CAAC;IAC7C,KAAAY,aAAa,GAAW,CAAC;IAChB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,kBAAkB,GAAG,IAAId,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAe,gBAAgB,GAAG,IAAIf,eAAe,CAAc,IAAI,CAAC;IAC1D,KAAAgB,UAAU,GAAG,IAAI,CAACD,gBAAgB,CAACE,YAAY,EAAE;IACjD,KAAAC,YAAY,GAAG,IAAI,CAACJ,kBAAkB,CAACG,YAAY,EAAE;IAM1D,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EACAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACC,UAAU,EAAE,CAACC,IAAI,CAC3BnB,GAAG,CAAEoB,IAAI,IAAK,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAACD,IAAI,CAAC,CAAC,CAClD;EACH;EACQE,eAAeA,CAAA;IACrB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACjB,SAAS,CAACoB,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIhC,WAAW,CAAC;MACrBiC,aAAa,EAAE,UAAUL,KAAK,EAAE;MAChCM,IAAI,EAAE,OAAO;MACb,cAAc,EAAE;KACjB,CAAC;EACJ;EAEQC,cAAcA,CAAA;IACpB,MAAMP,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACjB,SAAS,CAACoB,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIhC,WAAW,CAAC;MACrBiC,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EACQQ,eAAeA,CAAA;IACrB,OAAO,IAAInC,UAAU,EAAE,CACpBoC,GAAG,CAAC,QAAQ,EAAE9B,WAAW,CAAC+B,MAAM,CAAC,CACjCD,GAAG,CAAC,QAAQ,EAAE9B,WAAW,CAACgC,MAAM,CAAC;EACtC;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjB,UAAU,EAAE,CAACC,IAAI,CAC3BnB,GAAG,CAAEoB,IAAI,IAAI;MACX,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAACD,IAAI,CAAC;MAClC,IAAI,CAACb,WAAW,CAAC6B,cAAc,CAAChB,IAAI,CAAC;IACvC,CAAC,CAAC,EACFtB,UAAU,CAAEuC,KAAK,IAAI;MACnB;MACA,MAAMC,QAAQ,GAAG,IAAI,CAAC/B,WAAW,CAACgC,cAAc,EAAE;MAClD,IAAID,QAAQ,EAAE;QACZ,IAAI,CAAC3B,kBAAkB,CAACU,IAAI,CAACiB,QAAQ,CAAC;QACtC,OAAOvC,EAAE,CAACuC,QAAQ,CAAC;;MAErB,OAAOrC,UAAU,CAAC,MAAMoC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EACAnB,UAAUA,CAAA;IACR,OAAO,IAAI,CAACb,IAAI,CACbmC,GAAG,CAAO,GAAGtC,WAAW,CAACuC,UAAU,eAAe,EAAE;MACnDC,OAAO,EAAE,IAAI,CAACZ,cAAc,EAAE;MAC9Ba,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CAACrB,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAAC;EACvC;EACQ5B,qBAAqBA,CAAA;IAC3B,MAAMO,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,IAAI,CAAC,IAAI,CAACjB,SAAS,CAACoB,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,IAAI,CAACY,eAAe,EAAE,CAACU,SAAS,CAAC;QAC/BR,KAAK,EAAEA,CAAA,KAAK;UACV,MAAMS,YAAY,GAAG,IAAI,CAACxC,SAAS,CAACyC,WAAW,CAACxB,KAAK,CAAC;UAEtD;UACA,IAAIyB,YAAY,GAAG,mCAAmC;UAEtD;UACA,IACEF,YAAY,CAACE,YAAY,IACzBF,YAAY,CAACE,YAAY,KAAK,MAAM,IACpCF,YAAY,CAACE,YAAY,KAAK,WAAW,IACzCF,YAAY,CAACE,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EACvC;YACAD,YAAY,GAAGF,YAAY,CAACE,YAAY;;UAE1C;UAAA,KACK,IACHF,YAAY,CAACI,KAAK,IAClBJ,YAAY,CAACI,KAAK,KAAK,MAAM,IAC7BJ,YAAY,CAACI,KAAK,KAAK,WAAW,IAClCJ,YAAY,CAACI,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EAChC;YACAD,YAAY,GAAGF,YAAY,CAACI,KAAK;;UAGnCC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEJ,YAAY,CAAC;UAE/D,MAAMK,YAAY,GAAG;YACnBC,GAAG,EAAER,YAAY,CAACS,EAAE;YACpBC,QAAQ,EAAEV,YAAY,CAACU,QAAQ;YAC/BC,KAAK,EAAEX,YAAY,CAACW,KAAK;YACzB5B,IAAI,EAAEiB,YAAY,CAACjB,IAAI;YACvBqB,KAAK,EAAEF,YAAY;YACnBA,YAAY,EAAEA,YAAY;YAC1BU,QAAQ,EAAE;WACX;UAED,IAAI,CAAC/C,kBAAkB,CAACU,IAAI,CAACgC,YAAY,CAAC;UAC1C,IAAI,CAAC9C,WAAW,CAAC6B,cAAc,CAACiB,YAAY,CAAC;QAC/C;OACD,CAAC;;EAEN;EACAM,iBAAiBA,CAACC,QAAuB;IACvC,MAAMC,WAAW,GAAG,IAAI,CAAClD,kBAAkB,CAACmD,KAAK;IACjD,IAAID,WAAW,EAAE;MACf,IAAI,CAAClD,kBAAkB,CAACU,IAAI,CAAC;QAAE,GAAGwC,WAAW;QAAE,GAAGD;MAAQ,CAAE,CAAC;;EAEjE;EAEAG,UAAUA,CAACC,MAAc,EAAEC,UAAe;IACxC,OAAO,IAAI,CAAC5D,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,oBAAoBuB,MAAM,EAAE,EACrDC,UAAU,EACV;MACEvB,OAAO,EAAE,IAAI,CAACZ,cAAc,EAAE;MAC9Ba,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CACF,CACAZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAI;MAClB,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC;MACnC,IAAI,CAACR,iBAAiB,CAACQ,WAAW,CAAC;IACrC,CAAC,CAAC,EACFrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACAyB,cAAcA,CACZC,eAAuB,EACvBC,WAAmB;IAEnB,MAAMP,MAAM,GAAG,IAAI,CAACQ,gBAAgB,EAAElB,GAAG;IACzC,IAAI,CAACU,MAAM,EAAE;MACX,OAAO/D,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAE1D,MAAM8C,YAAY,GAAG;MACnBH,eAAe;MACfC;KACD;IACD,OAAO,IAAI,CAAClE,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,oBAAoBuB,MAAM,EAAE,EACrDS,YAAY,EACZ;MACE/B,OAAO,EAAE,IAAI,CAACZ,cAAc,EAAE;MAC9Ba,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CACF,CACAZ,IAAI,CACHnB,GAAG,CAAE0E,QAAQ,IAAI;MACf,IAAIA,QAAQ,CAACnD,KAAK,EAAE;QAClBC,YAAY,CAACmD,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACnD,KAAK,CAAC;QAC7C,MAAMuB,YAAY,GAAG,IAAI,CAACxC,SAAS,CAACyC,WAAW,CAAC2B,QAAQ,CAACnD,KAAK,CAAC;QAC/D,IAAI,CAACoC,iBAAiB,CAACb,YAAY,CAAC;;IAExC,CAAC,CAAC,EACFhD,UAAU,CAAEuC,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACuC,MAAM,KAAK,GAAG,EAAE;QACxB,IAAIvC,KAAK,CAACA,KAAK,EAAEwC,MAAM,EAAEC,OAAO,EAAE;UAChC,OAAO7E,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAACU,KAAK,CAACA,KAAK,CAACwC,MAAM,CAACC,OAAO,CAAC,CAAC;;QAEhE,OAAO7E,UAAU,CACf,MAAM,IAAI0B,KAAK,CAACU,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI,mBAAmB,CAAC,CAC7D;;MAEH,OAAO9E,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACAqD,kBAAkBA,CAChBC,IAAU;IAEV,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAO,IAAI,CAAC5E,IAAI,CACbgF,IAAI,CACH,GAAGnF,WAAW,CAACuC,UAAU,4BAA4B,EACrDyC,QAAQ,EACR;MACExC,OAAO,EAAE,IAAI/C,WAAW,CAAC;QACvBiC,aAAa,EAAE,UAAUJ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;OACvD,CAAC;MACFkB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CACF,CACAZ,IAAI,CACHnB,GAAG,CAAE0E,QAAQ,IAAI;MACf,IAAIA,QAAQ,CAACnD,KAAK,EAAE;QAClBC,YAAY,CAACmD,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACnD,KAAK,CAAC;QAC7C,MAAMuB,YAAY,GAAG,IAAI,CAACxC,SAAS,CAACyC,WAAW,CAAC2B,QAAQ,CAACnD,KAAK,CAAC;QAC/D,IAAI,CAACoC,iBAAiB,CAAC;UACrBT,KAAK,EAAEwB,QAAQ,CAACY,QAAQ;UACxB,GAAGxC;SACJ,CAAC;;IAEN,CAAC,CAAC,EACFhD,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACA2C,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAClF,IAAI,CACbmF,MAAM,CACL,GAAGtF,WAAW,CAACuC,UAAU,4BAA4B,EACrD;MACEC,OAAO,EAAE,IAAI,CAACZ,cAAc,EAAE;MAC9Ba,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CACF,CACAZ,IAAI,CACHnB,GAAG,CAAE0E,QAAQ,IAAI;MACf,IAAIA,QAAQ,CAACnD,KAAK,EAAE;QAClBC,YAAY,CAACmD,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACnD,KAAK,CAAC;QAC7C,IAAIwB,WAAW,GAAG,IAAI,CAACzC,SAAS,CAACyC,WAAW,CAAC2B,QAAQ,CAACnD,KAAK,CAAC;QAC5D,IAAI,CAACoC,iBAAiB,CAACZ,WAAW,CAAC;;IAEvC,CAAC,CAAC,EACFjD,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EAEA,IAAI4B,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC7D,kBAAkB,CAACmD,KAAK;EACtC;EAEA2B,OAAOA,CAAA;IACL,OAAO,IAAI,CAACjB,gBAAgB,EAAE3C,IAAI,KAAK,OAAO;EAChD;EACA6D,aAAaA,CAAC1B,MAAc;IAC1B,OAAO,IAAI,CAACQ,gBAAgB,EAAElB,GAAG,KAAKU,MAAM;EAC9C;EACQI,iBAAiBA,CAACD,WAAiB;IACzC,MAAMwB,YAAY,GAAG,IAAI,CAACnF,WAAW,CAACsD,KAAK,CAAC8B,GAAG,CAAEC,CAAC,IAChDA,CAAC,CAACvC,GAAG,KAAKa,WAAW,CAACb,GAAG,GAAG;MAAE,GAAGuC,CAAC;MAAE,GAAG1B;IAAW,CAAE,GAAG0B,CAAC,CACzD;IACD,IAAI,CAACrF,WAAW,CAACa,IAAI,CAACsE,YAAY,CAAC;EACrC;EACAG,gBAAgBA,CAAA;IACd,IAAI,CAACrF,aAAa,GAAG,CAAC;IACtB,IAAI,CAACsF,WAAW,CAAC,IAAI,CAAC,CAAClD,SAAS,EAAE;EACpC;EACAkD,WAAWA,CAACC,YAAY,GAAG,KAAK;IAC9B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;IACtB,MAAME,UAAU,GACd,CAACH,YAAY,IACb,IAAI,CAACxF,WAAW,CAACsD,KAAK,CAACsC,MAAM,GAAG,CAAC,IACjCH,GAAG,GAAG,IAAI,CAACxF,aAAa,IAAI,IAAI,CAACC,cAAc;IAEjD,IAAIyF,UAAU,EAAE;MACd,OAAO,IAAI,CAAC3F,WAAW,CAACM,YAAY,EAAE;;IAGxC,IAAI,CAACL,aAAa,GAAGwF,GAAG;IACxB,OAAO,IAAI,CAAC5F,IAAI,CACbmC,GAAG,CAAS,GAAGtC,WAAW,CAACuC,UAAU,cAAc,EAAE;MACpDC,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAC/BqB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CACHnB,GAAG,CAAEqG,KAAK,IAAK,IAAI,CAAC7F,WAAW,CAACa,IAAI,CAAC,CAAC,GAAGgF,KAAK,CAAC,CAAC,CAAC,EACjDvG,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACA0D,UAAUA,CAAC/C,EAAU;IACnB,OAAO,IAAI,CAAClD,IAAI,CACbmC,GAAG,CAAO,GAAGtC,WAAW,CAACuC,UAAU,gBAAgBc,EAAE,EAAE,EAAE;MACxDb,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAC/BqB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CAACrB,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAAC;EACvC;EACA2D,OAAOA,CAAC3C,QAAc;IACpB,OAAO,IAAI,CAACvD,IAAI,CACbgF,IAAI,CAAO,GAAGnF,WAAW,CAACuC,UAAU,WAAW,EAAEmB,QAAQ,EAAE;MAC1DlB,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAC/BqB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CACHnB,GAAG,CAAEwG,OAAO,IAAI;MACd,MAAMC,YAAY,GAAG,IAAI,CAACjG,WAAW,CAACsD,KAAK;MAC3C,IAAI,CAACtD,WAAW,CAACa,IAAI,CAAC,CAAC,GAAGoF,YAAY,EAAED,OAAO,CAAC,CAAC;MACjD,IAAI,CAAC5F,gBAAgB,CAACS,IAAI,EAAE;IAC9B,CAAC,CAAC,EACFvB,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EAEA8D,UAAUA,CAACnD,EAAU;IACnB,OAAO,IAAI,CAAClD,IAAI,CACbmF,MAAM,CAAO,GAAGtF,WAAW,CAACuC,UAAU,gBAAgBc,EAAE,EAAE,EAAE;MAC3Db,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAC/BqB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CACHnB,GAAG,CAAC,MAAK;MACP,MAAM2F,YAAY,GAAG,IAAI,CAACnF,WAAW,CAACsD,KAAK,CAAC6C,MAAM,CAC/Cd,CAAC,IAAKA,CAAC,CAACvC,GAAG,KAAKC,EAAE,CACpB;MACD,IAAI,CAAC/C,WAAW,CAACa,IAAI,CAACsE,YAAY,CAAC;IACrC,CAAC,CAAC,EACF7F,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACAgE,iBAAiBA,CAACrD,EAAU,EAAEsD,IAAmB;IAC/C,OAAO,IAAI,CAACxG,IAAI,CACb6D,GAAG,CAAO,GAAGhE,WAAW,CAACuC,UAAU,gBAAgBc,EAAE,EAAE,EAAEsD,IAAI,EAAE;MAC9DnE,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAC/BqB,MAAM,EAAE,IAAI,CAACZ,eAAe;KAC7B,CAAC,CACDZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC,EACzDrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACAkE,cAAcA,CAACvD,EAAU;IACvB,OAAO,IAAI,CAAClD,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,gBAAgBc,EAAE,aAAa,EACxD,EAAE,EACF;MAAEb,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAAEqB,MAAM,EAAE,IAAI,CAACZ,eAAe;IAAE,CAAE,CACpE,CACAZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC,EACzDrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACAmE,cAAcA,CAACxD,EAAU;IACvB,OAAO,IAAI,CAAClD,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,gBAAgBc,EAAE,aAAa,EACxD,EAAE,EACF;MAAEb,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAAEqB,MAAM,EAAE,IAAI,CAACZ,eAAe;IAAE,CAAE,CACpE,CACAZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC,EACzDrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EAEAoE,cAAcA,CAACzD,EAAU,EAAE1B,IAAY;IACrC,OAAO,IAAI,CAACxB,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,eAAec,EAAE,OAAO,EACjD;MAAE1B;IAAI,CAAE,EACR;MAAEa,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAAEqB,MAAM,EAAE,IAAI,CAACZ,eAAe;IAAE,CAAE,CACpE,CACAZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC,EACzDrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EAEAqE,oBAAoBA,CAAC1D,EAAU,EAAEG,QAAiB;IAChD,OAAO,IAAI,CAACrD,IAAI,CACb6D,GAAG,CACF,GAAGhE,WAAW,CAACuC,UAAU,eAAec,EAAE,aAAa,EACvD;MAAEG;IAAQ,CAAE,EACZ;MAAEhB,OAAO,EAAE,IAAI,CAACpB,eAAe,EAAE;MAAEqB,MAAM,EAAE,IAAI,CAACZ,eAAe;IAAE,CAAE,CACpE,CACAZ,IAAI,CACHnB,GAAG,CAAEmE,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC,EACzDrE,UAAU,CAAC,IAAI,CAAC8C,WAAW,CAAC,CAC7B;EACL;EACQA,WAAWA,CAACP,KAAwB;IAC1C,IAAI6E,YAAY,GAAG,yBAAyB;IAE5C,IAAI7E,KAAK,CAACuC,MAAM,KAAK,CAAC,EAAE;MACtBsC,YAAY,GAAG,oDAAoD;KACpE,MAAM,IAAI7E,KAAK,CAACuC,MAAM,IAAI,GAAG,IAAIvC,KAAK,CAACuC,MAAM,GAAG,GAAG,EAAE;MACpDsC,YAAY,GAAG7E,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO;KACrD,MAAM,IAAI1C,KAAK,CAACuC,MAAM,IAAI,GAAG,EAAE;MAC9BsC,YAAY,GAAG,+CAA+C;;IAGhE/D,OAAO,CAACd,KAAK,CAAC,UAAUA,KAAK,CAACuC,MAAM,GAAG,EAAEvC,KAAK,CAACA,KAAK,CAAC;IACrD,OAAOpC,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAACuF,YAAY,CAAC,CAAC;EAClD;;;uBA1YW/G,WAAW,EAAAgH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAXvH,WAAW;MAAAwH,OAAA,EAAXxH,WAAW,CAAAyH,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}