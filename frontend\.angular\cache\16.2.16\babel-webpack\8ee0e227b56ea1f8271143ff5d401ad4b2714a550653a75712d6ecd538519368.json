{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport class HighlightPresencePipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(value) {\n    if (!value) {\n      return this.sanitizer.bypassSecurityTrustHtml('');\n    }\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = value.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n  static {\n    this.ɵfac = function HighlightPresencePipe_Factory(t) {\n      return new (t || HighlightPresencePipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"highlightPresence\",\n      type: HighlightPresencePipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["HighlightPresencePipe", "constructor", "sanitizer", "transform", "value", "bypassSecurityTrustHtml", "formattedText", "replace", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\pipes\\highlight-presence.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\r\n\r\n@Pipe({\r\n  name: 'highlightPresence'\r\n})\r\nexport class HighlightPresencePipe implements PipeTransform {\r\n\r\n  constructor(private sanitizer: DomSanitizer) {}\r\n\r\n  transform(value: string): SafeHtml {\r\n    if (!value) {\r\n      return this.sanitizer.bypassSecurityTrustHtml('');\r\n    }\r\n\r\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\r\n    const formattedText = value.replace(\r\n      /\\(presence obligatoire\\)/gi,\r\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\r\n    );\r\n\r\n    // Sanitize le HTML pour éviter les problèmes de sécurité\r\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\r\n  }\r\n}\r\n"], "mappings": ";;AAMA,OAAM,MAAOA,qBAAqB;EAEhCC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;EAAiB;EAE9CC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI,CAACF,SAAS,CAACG,uBAAuB,CAAC,EAAE,CAAC;;IAGnD;IACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,OAAO,CACjC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAACL,SAAS,CAACG,uBAAuB,CAACC,aAAa,CAAC;EAC9D;;;uBAjBWN,qBAAqB,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAArBX,qBAAqB;MAAAY,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}