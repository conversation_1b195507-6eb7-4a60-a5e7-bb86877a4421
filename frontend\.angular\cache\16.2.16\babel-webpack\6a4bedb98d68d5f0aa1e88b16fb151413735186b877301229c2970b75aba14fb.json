{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\nimport { CallModule } from './components/call/call.module';\nimport { ConnectionStatusModule } from './components/connection-status/connection-status.module';\nimport { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\nimport { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\nimport { CreateUserModalComponent } from './views/admin/dashboard/create-user-modal/create-user-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@auth0/angular-jwt\";\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      if (!environment.production) {\n        console.debug('JWT token retrieved from storage');\n      }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`]\n  };\n}\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule, JwtModule.forRoot({\n        jwtOptionsProvider: {\n          provide: JWT_OPTIONS,\n          useFactory: jwtOptionsFactory\n        }\n      }), GraphQLModule, ApolloModule, CallModule, ConnectionStatusModule, GraphqlStatusModule, VoiceMessageModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, CreateUserModalComponent],\n    imports: [BrowserModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule, i1.JwtModule, GraphQLModule, ApolloModule, CallModule, ConnectionStatusModule, GraphqlStatusModule, VoiceMessageModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "LayoutsModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "JwtModule", "JWT_OPTIONS", "environment", "BrowserAnimationsModule", "GraphQLModule", "ApolloModule", "CallModule", "ConnectionStatusModule", "GraphqlStatusModule", "VoiceMessageModule", "SharedModule", "CreateUserModalComponent", "jwtOptionsFactory", "tokenGetter", "production", "console", "debug", "localStorage", "getItem", "allowedDomains", "URL", "urlBackend", "hostname", "disallowedRoutes", "origin", "AppModule", "bootstrap", "forRoot", "jwtOptionsProvider", "provide", "useFactory", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { LayoutsModule } from './layouts/layouts.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\r\nimport { environment } from 'src/environments/environment';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { GraphQLModule } from './graphql.module';\r\nimport { ApolloModule } from 'apollo-angular';\r\nimport { CallModule } from './components/call/call.module';\r\nimport { ConnectionStatusModule } from './components/connection-status/connection-status.module';\r\nimport { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\r\nimport { VoiceMessageModule } from './components/voice-message/voice-message.module';\r\nimport { SharedModule } from './shared/shared.module';\r\nimport { CreateUserModalComponent } from './views/admin/dashboard/create-user-modal/create-user-modal.component';\r\n// Factory simplifiée sans injection de JwtHelperService\r\nexport function jwtOptionsFactory() {\r\n  return {\r\n    tokenGetter: () => {\r\n      if (!environment.production) {\r\n        console.debug('JWT token retrieved from storage');\r\n      }\r\n      return localStorage.getItem('token');\r\n    },\r\n    allowedDomains: [new URL(environment.urlBackend).hostname],\r\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`],\r\n  };\r\n}\r\n\r\n@NgModule({\r\n  declarations: [AppComponent, CreateUserModalComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    LayoutsModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n    BrowserAnimationsModule,\r\n    JwtModule.forRoot({\r\n      jwtOptionsProvider: {\r\n        provide: JWT_OPTIONS,\r\n        useFactory: jwtOptionsFactory,\r\n      },\r\n    }),\r\n    GraphQLModule,\r\n    ApolloModule,\r\n    CallModule,\r\n    ConnectionStatusModule,\r\n    GraphqlStatusModule,\r\n    VoiceMessageModule,\r\n    SharedModule,\r\n  ],\r\n  providers: [],\r\n  bootstrap: [AppComponent],\r\n})\r\nexport class AppModule {}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,SAAS,EAAEC,WAAW,QAAQ,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,mBAAmB,QAAQ,mDAAmD;AACvF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,wBAAwB,QAAQ,uEAAuE;;;AAChH;AACA,OAAM,SAAUC,iBAAiBA,CAAA;EAC/B,OAAO;IACLC,WAAW,EAAEA,CAAA,KAAK;MAChB,IAAI,CAACX,WAAW,CAACY,UAAU,EAAE;QAC3BC,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;;MAEnD,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACtC,CAAC;IACDC,cAAc,EAAE,CAAC,IAAIC,GAAG,CAAClB,WAAW,CAACmB,UAAU,CAAC,CAACC,QAAQ,CAAC;IAC1DC,gBAAgB,EAAE,CAAC,GAAG,IAAIH,GAAG,CAAClB,WAAW,CAACmB,UAAU,CAAC,CAACG,MAAM,cAAc;GAC3E;AACH;AA6BA,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR/B,YAAY;IAAA;EAAA;;;gBAtBtBF,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBI,uBAAuB,EACvBH,SAAS,CAAC2B,OAAO,CAAC;QAChBC,kBAAkB,EAAE;UAClBC,OAAO,EAAE5B,WAAW;UACpB6B,UAAU,EAAElB;;OAEf,CAAC,EACFR,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY;IAAA;EAAA;;;2EAKHe,SAAS;IAAAM,YAAA,GA1BLpC,YAAY,EAAEgB,wBAAwB;IAAAqB,OAAA,GAEnDvC,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBI,uBAAuB,EAAA8B,EAAA,CAAAjC,SAAA,EAOvBI,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}