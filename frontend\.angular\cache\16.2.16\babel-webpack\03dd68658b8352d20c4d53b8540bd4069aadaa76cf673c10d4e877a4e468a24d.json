{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"src/app/services/toast.service\";\nimport * as i4 from \"@angular/common\";\nfunction CreateUserModalComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateUserModalComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateUserModalComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1, \" Password must be at least 6 characters long \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateUserModalComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, role_r5), \" \");\n  }\n}\nfunction CreateUserModalComponent_i_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20\": a0\n  };\n};\nexport class CreateUserModalComponent {\n  constructor(fb, authService, toastService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.closeModal = new EventEmitter();\n    this.userCreated = new EventEmitter();\n    this.loading = false;\n    this.roles = ['student', 'teacher', 'admin'];\n    this.createUserForm = this.fb.group({\n      fullName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      role: ['student', Validators.required]\n    });\n  }\n  onSubmit() {\n    if (this.createUserForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.toastService.showError('Authentication token not found');\n      return;\n    }\n    // The password will be generated on the server side\n    this.authService.createUserWithGeneratedPassword(this.createUserForm.value, token).subscribe({\n      next: response => {\n        this.toastService.showSuccess('User created successfully. Password has been sent to their email.');\n        this.userCreated.emit(response.user);\n        this.closeModal.emit(true);\n        this.loading = false;\n      },\n      error: error => {\n        this.toastService.showError(error.error?.message || 'Failed to create user');\n        this.loading = false;\n      }\n    });\n  }\n  close() {\n    this.closeModal.emit(true);\n  }\n  static {\n    this.ɵfac = function CreateUserModalComponent_Factory(t) {\n      return new (t || CreateUserModalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateUserModalComponent,\n      selectors: [[\"app-create-user-modal\"]],\n      outputs: {\n        closeModal: \"closeModal\",\n        userCreated: \"userCreated\"\n      },\n      decls: 33,\n      vars: 17,\n      consts: [[1, \"fixed\", \"inset-0\", \"bg-black/50\", \"dark:bg-black/70\", \"backdrop-blur-sm\", \"z-40\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-xl\", \"w-full\", \"max-w-md\", \"relative\"], [1, \"p-6\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"absolute\", \"top-4\", \"right-4\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-lg\"], [1, \"p-6\"], [1, \"space-y-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [\"type\", \"text\", \"formControlName\", \"fullName\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngClass\"], [\"class\", \"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\", 4, \"ngIf\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngClass\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngClass\"], [\"formControlName\", \"role\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"font-medium\", \"hover:opacity-90\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", \"justify-center\", 3, \"disabled\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"mt-1\", \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [3, \"value\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n      template: function CreateUserModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5, \" Create New User \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CreateUserModalComponent_Template_button_click_6_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(7, \"i\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function CreateUserModalComponent_Template_form_ngSubmit_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(10, \"div\")(11, \"label\", 9);\n          i0.ɵɵtext(12, \" Full Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 10);\n          i0.ɵɵtemplate(14, CreateUserModalComponent_div_14_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\")(16, \"label\", 9);\n          i0.ɵɵtext(17, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 12);\n          i0.ɵɵtemplate(19, CreateUserModalComponent_div_19_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\")(21, \"label\", 9);\n          i0.ɵɵtext(22, \" Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"input\", 13);\n          i0.ɵɵtemplate(24, CreateUserModalComponent_div_24_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\")(26, \"label\", 9);\n          i0.ɵɵtext(27, \" Role \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"select\", 14);\n          i0.ɵɵtemplate(29, CreateUserModalComponent_option_29_Template, 3, 4, \"option\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"button\", 16);\n          i0.ɵɵtemplate(31, CreateUserModalComponent_i_31_Template, 1, 0, \"i\", 17);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formGroup\", ctx.createUserForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ((tmp_1_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_1_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.createUserForm.get(\"fullName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ((tmp_3_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_3_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.createUserForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c0, ((tmp_5_0 = ctx.createUserForm.get(\"password\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.createUserForm.get(\"password\")) == null ? null : tmp_5_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.createUserForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.createUserForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.createUserForm.invalid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Creating...\" : \"Create User\", \" \");\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i4.TitleCasePipe],\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "role_r5", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵelement", "CreateUserModalComponent", "constructor", "fb", "authService", "toastService", "closeModal", "userCreated", "loading", "roles", "createUserForm", "group", "fullName", "required", "email", "role", "onSubmit", "invalid", "token", "localStorage", "getItem", "showError", "createUserWithGeneratedPassword", "value", "subscribe", "next", "response", "showSuccess", "emit", "user", "error", "message", "close", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "ToastService", "selectors", "outputs", "decls", "vars", "consts", "template", "CreateUserModalComponent_Template", "rf", "ctx", "ɵɵlistener", "CreateUserModalComponent_Template_button_click_6_listener", "CreateUserModalComponent_Template_form_ngSubmit_9_listener", "ɵɵtemplate", "CreateUserModalComponent_div_14_Template", "CreateUserModalComponent_div_19_Template", "CreateUserModalComponent_div_24_Template", "CreateUserModalComponent_option_29_Template", "CreateUserModalComponent_i_31_Template", "ɵɵpureFunction1", "_c0", "tmp_1_0", "get", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\create-user-modal\\create-user-modal.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\create-user-modal\\create-user-modal.component.html"], "sourcesContent": ["import { Component, EventEmitter, Output } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\n\r\n@Component({\r\n  selector: 'app-create-user-modal',\r\n  templateUrl: './create-user-modal.component.html',\r\n  styleUrls: ['./create-user-modal.component.css']\r\n})\r\nexport class CreateUserModalComponent {\r\n  @Output() closeModal = new EventEmitter<boolean>();\r\n  @Output() userCreated = new EventEmitter<any>();\r\n\r\n  createUserForm: FormGroup;\r\n  loading = false;\r\n  roles = ['student', 'teacher', 'admin'];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private toastService: ToastService\r\n  ) {\r\n    this.createUserForm = this.fb.group({\r\n      fullName: ['', [Validators.required]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      role: ['student', Validators.required]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.createUserForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const token = localStorage.getItem('token');\r\n\r\n    if (!token) {\r\n      this.toastService.showError('Authentication token not found');\r\n      return;\r\n    }\r\n\r\n    // The password will be generated on the server side\r\n    this.authService.createUserWithGeneratedPassword(this.createUserForm.value, token).subscribe({\r\n      next: (response: any) => {\r\n        this.toastService.showSuccess('User created successfully. Password has been sent to their email.');\r\n        this.userCreated.emit(response.user);\r\n        this.closeModal.emit(true);\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.toastService.showError(error.error?.message || 'Failed to create user');\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  close() {\r\n    this.closeModal.emit(true);\r\n  }\r\n} ", "<!-- <PERSON><PERSON> Backdrop -->\r\n<div class=\"fixed inset-0 bg-black/50 dark:bg-black/70 backdrop-blur-sm z-40\"></div>\r\n\r\n<!-- Modal Content -->\r\n<div class=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\r\n  <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-xl w-full max-w-md relative\">\r\n    <!-- <PERSON>dal Header -->\r\n    <div class=\"p-6 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n      <h3 class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n        Create New User\r\n      </h3>\r\n      <button \r\n        (click)=\"close()\"\r\n        class=\"absolute top-4 right-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\r\n      >\r\n        <i class=\"fas fa-times text-lg\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Modal Body -->\r\n    <div class=\"p-6\">\r\n      <form [formGroup]=\"createUserForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-4\">\r\n        <!-- Full Name -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Full Name\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            formControlName=\"fullName\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            [ngClass]=\"{'border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20': createUserForm.get('fullName')?.invalid && createUserForm.get('fullName')?.touched}\"\r\n          />\r\n          <div *ngIf=\"createUserForm.get('fullName')?.invalid && createUserForm.get('fullName')?.touched\" class=\"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n            Full name is required\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Email -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Email\r\n          </label>\r\n          <input\r\n            type=\"email\"\r\n            formControlName=\"email\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            [ngClass]=\"{'border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20': createUserForm.get('email')?.invalid && createUserForm.get('email')?.touched}\"\r\n          />\r\n          <div *ngIf=\"createUserForm.get('email')?.invalid && createUserForm.get('email')?.touched\" class=\"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n            Please enter a valid email address\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Password -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Password\r\n          </label>\r\n          <input\r\n            type=\"password\"\r\n            formControlName=\"password\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n            [ngClass]=\"{'border-[#ff6b69] dark:border-[#ff8785] focus:border-[#ff6b69] dark:focus:border-[#ff8785] focus:ring-[#ff6b69]/20 dark:focus:ring-[#ff8785]/20': createUserForm.get('password')?.invalid && createUserForm.get('password')?.touched}\"\r\n          />\r\n          <div *ngIf=\"createUserForm.get('password')?.invalid && createUserForm.get('password')?.touched\" class=\"mt-1 text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n            Password must be at least 6 characters long\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Role -->\r\n        <div>\r\n          <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\r\n            Role\r\n          </label>\r\n          <select\r\n            formControlName=\"role\"\r\n            class=\"w-full px-3 py-2 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n          >\r\n            <option *ngFor=\"let role of roles\" [value]=\"role\">\r\n              {{ role | titlecase }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <button\r\n          type=\"submit\"\r\n          [disabled]=\"createUserForm.invalid || loading\"\r\n          class=\"w-full px-4 py-2.5 text-sm rounded-lg bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\r\n        >\r\n          <i *ngIf=\"loading\" class=\"fas fa-spinner fa-spin mr-2\"></i>\r\n          {{ loading ? 'Creating...' : 'Create User' }}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": "AAAA,SAAoBA,YAAY,QAAgB,eAAe;AAC/D,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;ICgCzDC,EAAA,CAAAC,cAAA,cAAwJ;IACtJD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAkJ;IAChJD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAwJ;IACtJD,EAAA,CAAAE,MAAA,oDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,iBAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IAC/CL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAQ,WAAA,OAAAH,OAAA,OACF;;;;;IAUFL,EAAA,CAAAS,SAAA,YAA2D;;;;;;;;ADjFrE,OAAM,MAAOC,wBAAwB;EAQnCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,YAA0B;IAF1B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IAVZ,KAAAC,UAAU,GAAG,IAAIjB,YAAY,EAAW;IACxC,KAAAkB,WAAW,GAAG,IAAIlB,YAAY,EAAO;IAG/C,KAAAmB,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAOrC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAClCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,CAAC,CAAC;MACrCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,IAAI,EAAE,CAAC,SAAS,EAAEzB,UAAU,CAACuB,QAAQ;KACtC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,cAAc,CAACO,OAAO,EAAE;MAC/B;;IAGF,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACF,KAAK,EAAE;MACV,IAAI,CAACb,YAAY,CAACgB,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,IAAI,CAACjB,WAAW,CAACkB,+BAA+B,CAAC,IAAI,CAACZ,cAAc,CAACa,KAAK,EAAEL,KAAK,CAAC,CAACM,SAAS,CAAC;MAC3FC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrB,YAAY,CAACsB,WAAW,CAAC,mEAAmE,CAAC;QAClG,IAAI,CAACpB,WAAW,CAACqB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC;QACpC,IAAI,CAACvB,UAAU,CAACsB,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACpB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzB,YAAY,CAACgB,SAAS,CAACS,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,uBAAuB,CAAC;QAC5E,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAwB,KAAKA,CAAA;IACH,IAAI,CAAC1B,UAAU,CAACsB,IAAI,CAAC,IAAI,CAAC;EAC5B;;;uBAlDW3B,wBAAwB,EAAAV,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAxBtC,wBAAwB;MAAAuC,SAAA;MAAAC,OAAA;QAAAnC,UAAA;QAAAC,WAAA;MAAA;MAAAmC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTrCxD,EAAA,CAAAS,SAAA,aAAoF;UAGpFT,EAAA,CAAAC,cAAA,aAAqE;UAK7DD,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAGC;UAFCD,EAAA,CAAA0D,UAAA,mBAAAC,0DAAA;YAAA,OAASF,GAAA,CAAAhB,KAAA,EAAO;UAAA,EAAC;UAGjBzC,EAAA,CAAAS,SAAA,WAAoC;UACtCT,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,aAAiB;UACoBD,EAAA,CAAA0D,UAAA,sBAAAE,2DAAA;YAAA,OAAYH,GAAA,CAAAhC,QAAA,EAAU;UAAA,EAAC;UAExDzB,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAKE;UACFT,EAAA,CAAA6D,UAAA,KAAAC,wCAAA,kBAEM;UACR9D,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAKE;UACFT,EAAA,CAAA6D,UAAA,KAAAE,wCAAA,kBAEM;UACR/D,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAKE;UACFT,EAAA,CAAA6D,UAAA,KAAAG,wCAAA,kBAEM;UACRhE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,cACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAA6D,UAAA,KAAAI,2CAAA,qBAES;UACXjE,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAA6D,UAAA,KAAAK,sCAAA,gBAA2D;UAC3DlE,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;UAxELH,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,cAAAqD,GAAA,CAAAtC,cAAA,CAA4B;UAU5BnB,EAAA,CAAAM,SAAA,GAAkP;UAAlPN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmE,eAAA,KAAAC,GAAA,IAAAC,OAAA,GAAAZ,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAD,OAAA,CAAA3C,OAAA,OAAA2C,OAAA,GAAAZ,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAD,OAAA,CAAAE,OAAA,GAAkP;UAE9OvE,EAAA,CAAAM,SAAA,GAAwF;UAAxFN,EAAA,CAAAI,UAAA,WAAAoE,OAAA,GAAAf,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAE,OAAA,CAAA9C,OAAA,OAAA8C,OAAA,GAAAf,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAE,OAAA,CAAAD,OAAA,EAAwF;UAc5FvE,EAAA,CAAAM,SAAA,GAA4O;UAA5ON,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmE,eAAA,KAAAC,GAAA,IAAAK,OAAA,GAAAhB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,4BAAAG,OAAA,CAAA/C,OAAA,OAAA+C,OAAA,GAAAhB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,4BAAAG,OAAA,CAAAF,OAAA,GAA4O;UAExOvE,EAAA,CAAAM,SAAA,GAAkF;UAAlFN,EAAA,CAAAI,UAAA,WAAAsE,OAAA,GAAAjB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,4BAAAI,OAAA,CAAAhD,OAAA,OAAAgD,OAAA,GAAAjB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,4BAAAI,OAAA,CAAAH,OAAA,EAAkF;UActFvE,EAAA,CAAAM,SAAA,GAAkP;UAAlPN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmE,eAAA,KAAAC,GAAA,IAAAO,OAAA,GAAAlB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAK,OAAA,CAAAjD,OAAA,OAAAiD,OAAA,GAAAlB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAK,OAAA,CAAAJ,OAAA,GAAkP;UAE9OvE,EAAA,CAAAM,SAAA,GAAwF;UAAxFN,EAAA,CAAAI,UAAA,WAAAwE,OAAA,GAAAnB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAM,OAAA,CAAAlD,OAAA,OAAAkD,OAAA,GAAAnB,GAAA,CAAAtC,cAAA,CAAAmD,GAAA,+BAAAM,OAAA,CAAAL,OAAA,EAAwF;UAcnEvE,EAAA,CAAAM,SAAA,GAAQ;UAARN,EAAA,CAAAI,UAAA,YAAAqD,GAAA,CAAAvC,KAAA,CAAQ;UASnClB,EAAA,CAAAM,SAAA,GAA8C;UAA9CN,EAAA,CAAAI,UAAA,aAAAqD,GAAA,CAAAtC,cAAA,CAAAO,OAAA,IAAA+B,GAAA,CAAAxC,OAAA,CAA8C;UAG1CjB,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAI,UAAA,SAAAqD,GAAA,CAAAxC,OAAA,CAAa;UACjBjB,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAO,kBAAA,MAAAkD,GAAA,CAAAxC,OAAA,sCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}